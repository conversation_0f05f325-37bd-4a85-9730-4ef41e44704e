import{buildOr as r,buildAnd as n,CompoundCondition as t}from"@ucast/mongo2js";function e(r){return Array.isArray(r)?r:[r]}function u(r,n,t){var e=r;var u=n;if(-1!==n.indexOf(".")){var i=n.split(".");u=i.pop();e=i.reduce((function(r,n){r[n]=r[n]||{};return r[n]}),r)}e[u]=t}Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function i(r,n,t,e){var u={};var i=r.rulesFor(n,t);for(var o=0;o<i.length;o++){var a=i[o];var f=a.inverted?"$and":"$or";if(!a.conditions)if(a.inverted)break;else{delete u[f];return u}else{u[f]=u[f]||[];u[f].push(e(a))}}return u.$or?u:null}function o(r){if(!r.ast)throw new Error('Ability rule "'+JSON.stringify(r)+'" does not have "ast" property. So, cannot be used to generate AST');return r.inverted?new t("not",[r.ast]):r.ast}function a(t,e,u){var a=i(t,e,u,o);if(null===a)return null;if(!a.$and)return a.$or?r(a.$or):n([]);if(a.$or)a.$and.push(r(a.$or));return n(a.$and)}function f(r,n,t){return r.rulesFor(n,t).reduce((function(r,n){if(n.inverted||!n.conditions)return r;return Object.keys(n.conditions).reduce((function(r,t){var e=n.conditions[t];if(!e||e.constructor!==Object)u(r,t,e);return r}),r)}),{})}function c(r,n,t,e){var u=r.detectSubjectType(t);var i=r.possibleRulesFor(n,u);var o=new Set;var a=o.delete.bind(o);var f=o.add.bind(o);var c=i.length;while(c--){var v=i[c];if(v.matchesConditions(t)){var l=v.inverted?a:f;e.fieldsFrom(v).forEach(l)}}return Array.from(o)}var v=function r(n){return Array.isArray(n)?n.join(","):n};function l(r,n){return r.map((function(r){var t=[v(r.action||r.actions),"function"===typeof n?e(r.subject).map(n).join(","):v(r.subject),r.conditions||0,r.inverted?1:0,r.fields?v(r.fields):0,r.reason||""];while(t.length>0&&!t[t.length-1])t.pop();return t}))}function b(r,n){return r.map((function(r){var t=r[0],e=r[1],u=r[2],i=r[3],o=r[4],a=r[5];var f=e.split(",");var c={inverted:!!i,action:t.split(","),subject:"function"===typeof n?f.map(n):f};if(u)c.conditions=u;if(o)c.fields=o.split(",");if(a)c.reason=a;return c}))}export{l as packRules,c as permittedFieldsOf,a as rulesToAST,f as rulesToFields,i as rulesToQuery,b as unpackRules};
//# sourceMappingURL=extra.js.map
