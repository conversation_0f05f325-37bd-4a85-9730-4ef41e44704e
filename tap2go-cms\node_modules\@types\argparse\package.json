{"name": "@types/argparse", "version": "1.0.38", "description": "TypeScript definitions for argparse", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/arcticwaters", "githubUsername": "arcticwaters"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tlaziuk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/cakoose", "githubUsername": "cakoose"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ondkloss", "githubUsername": "ondkloss"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/argparse"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "26bb4ca1b89eb9cba8de69870e7f3a77a51764f16d032f6d172e0016f313312a", "typeScriptVersion": "2.8"}