(function(t,r){"object"===typeof exports&&"undefined"!==typeof module?r(exports,require("@ucast/mongo2js")):"function"===typeof define&&define.amd?define(["exports","@ucast/mongo2js"],r):(t="undefined"!==typeof globalThis?globalThis:t||self,r(t.casl={},t.ucast.mongo2js))})(this,(function(t,r){"use strict";function i(t,r){for(var i=0;i<r.length;i++){var n=r[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(t,a(n.key),n)}}function n(t,r,n){if(r)i(t.prototype,r);if(n)i(t,n);Object.defineProperty(t,"prototype",{writable:false});return t}function e(){e=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n))t[n]=i[n]}return t};return e.apply(this,arguments)}function u(t,r){t.prototype=Object.create(r.prototype);t.prototype.constructor=t;o(t,r)}function o(t,r){o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function t(r,i){r.__proto__=i;return r};return o(t,r)}function f(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function s(t,r){if("object"!==typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,r||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}function a(t){var r=s(t,"string");return"symbol"===typeof r?r:String(r)}function c(t){return Array.isArray(t)?t:[t]}var h=Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);var v="__caslSubjectType__";function l(t,r){if(r)if(!h(r,v))Object.defineProperty(r,v,{value:t});else if(t!==r[v])throw new Error("Trying to cast object to subject type "+t+" but previously it was casted to "+r[v]);return r}var d=function t(r){var i=typeof r;return"string"===i||"function"===i};var b=function t(r){return r.modelName||r.name};var y=function t(r){return"string"===typeof r?r:b(r)};function p(t){if(h(t,v))return t[v];return b(t.constructor)}function w(t,r,i){var n=c(r);var e=0;while(e<n.length){var u=n[e++];if(h(t,u))n=i(n,t[u])}return n}function g(t,r){if("string"===typeof r&&-1!==t.indexOf(r))return r;for(var i=0;i<r.length;i++)if(-1!==t.indexOf(r[i]))return r[i];return null}var j=function t(r,i){return r.concat(i)};function A(t,r){if(r in t)throw new Error('Cannot use "'+r+"\" as an alias because it's reserved action.");var i=Object.keys(t);var n=function t(i,n){var e=g(i,n);if(e)throw new Error("Detected cycle "+e+" -> "+i.join(", "));var u="string"===typeof n&&n===r||-1!==i.indexOf(r)||Array.isArray(n)&&-1!==n.indexOf(r);if(u)throw new Error('Cannot make an alias to "'+r+'" because this is reserved action');return i.concat(n)};for(var e=0;e<i.length;e++)w(t,i[e],n)}function E(t,r){if(!r||false!==r.skipValidate)A(t,r&&r.anyAction||"manage");return function(r){return w(t,r,j)}}function m(t,r,i){for(var n=i;n<r.length;n++)t.push(r[n])}function M(t,r){if(!t||!t.length)return r||[];if(!r||!r.length)return t||[];var i=0;var n=0;var e=[];while(i<t.length&&n<r.length)if(t[i].priority<r[n].priority){e.push(t[i]);i++}else{e.push(r[n]);n++}m(e,t,i);m(e,r,n);return e}function $(t,r,i){var n=t.get(r);if(!n){n=i();t.set(r,n)}return n}var O=function t(r){return r};function x(t,r){if(Array.isArray(t.fields)&&!t.fields.length)throw new Error("`rawRule.fields` cannot be an empty array. https://bit.ly/390miLa");if(t.fields&&!r.fieldMatcher)throw new Error('You need to pass "fieldMatcher" option in order to restrict access by fields');if(t.conditions&&!r.conditionsMatcher)throw new Error('You need to pass "conditionsMatcher" option in order to restrict access by conditions')}var _=function(){function t(t,r,i){if(void 0===i)i=0;x(t,r);this.action=r.resolveAction(t.action);this.subject=t.subject;this.inverted=!!t.inverted;this.conditions=t.conditions;this.reason=t.reason;this.origin=t;this.fields=t.fields?c(t.fields):void 0;this.priority=i;this.t=r}var r=t.prototype;r.i=function t(){if(this.conditions&&!this.u)this.u=this.t.conditionsMatcher(this.conditions);return this.u};r.matchesConditions=function t(r){if(!this.conditions)return true;if(!r||d(r))return!this.inverted;var i=this.i();return i(r)};r.matchesField=function t(r){if(!this.fields)return true;if(!r)return!this.inverted;if(this.fields&&!this.o)this.o=this.t.fieldMatcher(this.fields);return this.o(r)};n(t,[{key:"ast",get:function t(){var r=this.i();return r?r.ast:void 0}}]);return t}();function F(t,r){var i={value:t,prev:r,next:null};if(r)r.next=i;return i}function S(t){if(t.next)t.next.prev=t.prev;if(t.prev)t.prev.next=t.next;t.next=t.prev=null}var T=function t(r){return{value:r.value,prev:r.prev,next:r.next}};var C=function t(){return{rules:[],merged:false}};var P=function t(){return new Map};var R=function(){function t(t,r){if(void 0===t)t=[];if(void 0===r)r={};this.h=false;this.v={conditionsMatcher:r.conditionsMatcher,fieldMatcher:r.fieldMatcher,resolveAction:r.resolveAction||O};this.l=r.anyAction||"manage";this.p=r.anySubjectType||"all";this.g=r.detectSubjectType||p;this.j=t;this.A=this.m(t)}var r=t.prototype;r.detectSubjectType=function t(r){if(d(r))return r;if(!r)return this.p;return this.g(r)};r.update=function t(r){var i={rules:r,ability:this,target:this};this.M("update",i);this.j=r;this.A=this.m(r);this.M("updated",i);return this};r.m=function t(r){var i=new Map;for(var n=r.length-1;n>=0;n--){var e=r.length-n-1;var u=new _(r[n],this.v,e);var o=c(u.action);var f=c(u.subject||this.p);if(!this.h&&u.fields)this.h=true;for(var s=0;s<f.length;s++){var a=$(i,f[s],P);for(var h=0;h<o.length;h++)$(a,o[h],C).rules.push(u)}}return i};r.possibleRulesFor=function t(r,i){if(void 0===i)i=this.p;if(!d(i))throw new Error('"possibleRulesFor" accepts only subject types (i.e., string or class) as the 2nd parameter');var n=$(this.A,i,P);var e=$(n,r,C);if(e.merged)return e.rules;var u=r!==this.l&&n.has(this.l)?n.get(this.l).rules:void 0;var o=M(e.rules,u);if(i!==this.p)o=M(o,this.possibleRulesFor(r,this.p));e.rules=o;e.merged=true;return o};r.rulesFor=function t(r,i,n){var e=this.possibleRulesFor(r,i);if(n&&"string"!==typeof n)throw new Error("The 3rd, `field` parameter is expected to be a string. See https://stalniy.github.io/casl/en/api/casl-ability#can-of-pure-ability for details");if(!this.h)return e;return e.filter((function(t){return t.matchesField(n)}))};r.actionsFor=function t(r){if(!d(r))throw new Error('"actionsFor" accepts only subject types (i.e., string or class) as a parameter');var i=new Set;var n=this.A.get(r);if(n)Array.from(n.keys()).forEach((function(t){return i.add(t)}));var e=r!==this.p?this.A.get(this.p):void 0;if(e)Array.from(e.keys()).forEach((function(t){return i.add(t)}));return Array.from(i)};r.on=function t(r,i){this.$=this.$||new Map;var n=this.$;var e=n.get(r)||null;var u=F(i,e);n.set(r,u);return function(){var t=n.get(r);if(!u.next&&!u.prev&&t===u)n.delete(r);else if(u===t)n.set(r,u.prev);S(u)}};r.M=function t(r,i){if(!this.$)return;var n=this.$.get(r)||null;while(null!==n){var e=n.prev?T(n.prev):null;n.value(i);n=e}};n(t,[{key:"rules",get:function t(){return this.j}}]);return t}();var k=function(t){u(PureAbility,t);function PureAbility(){return t.apply(this,arguments)||this}var r=PureAbility.prototype;r.can=function t(r,i,n){var e=this.relevantRuleFor(r,i,n);return!!e&&!e.inverted};r.relevantRuleFor=function t(r,i,n){var e=this.detectSubjectType(i);var u=this.rulesFor(r,e,n);for(var o=0,f=u.length;o<f;o++)if(u[o].matchesConditions(i))return u[o];return null};r.cannot=function t(r,i,n){return!this.can(r,i,n)};return PureAbility}(R);var q={$eq:r.$eq,$ne:r.$ne,$lt:r.$lt,$lte:r.$lte,$gt:r.$gt,$gte:r.$gte,$in:r.$in,$nin:r.$nin,$all:r.$all,$size:r.$size,$regex:r.$regex,$options:r.$options,$elemMatch:r.$elemMatch,$exists:r.$exists};var B={eq:r.eq,ne:r.ne,lt:r.lt,lte:r.lte,gt:r.gt,gte:r.gte,in:r.within,nin:r.nin,all:r.all,size:r.size,regex:r.regex,elemMatch:r.elemMatch,exists:r.exists,and:r.and};var z=function t(i,n,u){return r.createFactory(e({},q,i),e({},B,n),u)};var Y=r.createFactory(q,B);var D=/[-/\\^$+?.()|[\]{}]/g;var L=/\.?\*+\.?/g;var N=/\*+/;var G=/\./g;function H(t,r,i){var n="*"===i[0]||"."===t[0]&&"."===t[t.length-1]?"+":"*";var e=-1===t.indexOf("**")?"[^.]":".";var u=t.replace(G,"\\$&").replace(N,e+n);return r+t.length===i.length?"(?:"+u+")?":u}function I(t,r,i){if("."===t&&("*"===i[r-1]||"*"===i[r+1]))return t;return"\\"+t}function J(t){var r=t.map((function(t){return t.replace(D,I).replace(L,H)}));var i=r.length>1?"(?:"+r.join("|")+")":r[0];return new RegExp("^"+i+"$")}var K=function t(r){var i;return function(t){if("undefined"===typeof i)i=r.every((function(t){return-1===t.indexOf("*")}))?null:J(r);return null===i?-1!==r.indexOf(t):i.test(t)}};var Q=function(t){u(Ability,t);function Ability(r,i){if(void 0===r)r=[];if(void 0===i)i={};return t.call(this,r,e({conditionsMatcher:Y,fieldMatcher:K},i))||this}return Ability}(k);function createMongoAbility(t,r){if(void 0===t)t=[];if(void 0===r)r={};return new k(t,e({conditionsMatcher:Y,fieldMatcher:K},r))}function isAbilityClass(t){return"function"===typeof t.prototype.possibleRulesFor}var U=function(){function t(t){this.O=t}var r=t.prototype;r.because=function t(r){this.O.reason=r;return this};return t}();var V=function(){function AbilityBuilder(t){var r=this;this.rules=[];this._=t;this.can=function(t,i,n,e){return r.F(t,i,n,e,false)};this.cannot=function(t,i,n,e){return r.F(t,i,n,e,true)};this.build=function(t){return isAbilityClass(r._)?new r._(r.rules,t):r._(r.rules,t)}}var t=AbilityBuilder.prototype;t.F=function t(r,i,n,e,u){var o={action:r};if(u)o.inverted=u;if(i){o.subject=i;if(Array.isArray(n)||"string"===typeof n)o.fields=n;else if("undefined"!==typeof n)o.conditions=n;if("undefined"!==typeof e)o.conditions=e}this.rules.push(o);return new U(o)};return AbilityBuilder}();function defineAbility(t,r){var i=new V(createMongoAbility);var n=t(i.can,i.cannot);if(n&&"function"===typeof n.then)return n.then((function(){return i.build(r)}));return i.build(r)}var W=function t(r){return'Cannot execute "'+r.action+'" on "'+r.subjectType+'"'};var X=function t(r){this.message=r};X.prototype=Object.create(Error.prototype);var Z=function(t){u(ForbiddenError,t);ForbiddenError.setDefaultMessage=function t(r){this.S="string"===typeof r?function(){return r}:r};ForbiddenError.from=function t(r){return new this(r)};function ForbiddenError(r){var i;i=t.call(this,"")||this;i.ability=r;if("function"===typeof Error.captureStackTrace){i.name="ForbiddenError";Error.captureStackTrace(f(i),i.constructor)}return i}var r=ForbiddenError.prototype;r.setMessage=function t(r){this.message=r;return this};r.throwUnlessCan=function t(r,i,n){var e=this.unlessCan(r,i,n);if(e)throw e};r.unlessCan=function t(r,i,n){var e=this.ability.relevantRuleFor(r,i,n);if(e&&!e.inverted)return;this.action=r;this.subject=i;this.subjectType=y(this.ability.detectSubjectType(i));this.field=n;var u=e?e.reason:"";this.message=this.message||u||this.constructor.S(this);return this};return ForbiddenError}(X);Z.S=W;var tt=Object.freeze({__proto__:null});t.Ability=Q;t.AbilityBuilder=V;t.ForbiddenError=Z;t.PureAbility=k;t.buildMongoQueryMatcher=z;t.createAliasResolver=E;t.createMongoAbility=createMongoAbility;t.defineAbility=defineAbility;t.detectSubjectType=p;t.fieldPatternMatcher=K;t.getDefaultErrorMessage=W;t.hkt=tt;t.mongoQueryMatcher=Y;t.subject=l;t.wrapArray=c;Object.defineProperty(t,"__esModule",{value:true})}));
//# sourceMappingURL=index.js.map
