{"version": 3, "file": "index.native.umd.js", "sources": ["../src/native/index.ts"], "sourcesContent": ["import {\n  ComponentType,\n  Context as ContextOrig,\n  MutableRefObject,\n  Provider,\n  ReactNode,\n  createElement,\n  createContext as createContextOrig,\n  useContext as useContextOrig,\n  useEffect,\n  useLayoutEffect,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {\n  unstable_NormalPriority as NormalPriority,\n  unstable_runWithPriority as runWithPriority,\n} from 'scheduler';\n\nimport { batchedUpdates } from './batchedUpdates';\n\nconst CONTEXT_VALUE = Symbol();\nconst ORIGINAL_PROVIDER = Symbol();\n\nconst isSSR = typeof window === 'undefined'\n  || /ServerSideRendering/.test(window.navigator && window.navigator.userAgent);\n\nconst useIsomorphicLayoutEffect = isSSR ? useEffect : useLayoutEffect;\n\n// for preact that doesn't have runWithPriority\nconst runWithNormalPriority = runWithPriority\n  ? (thunk: () => void) => runWithPriority(NormalPriority, thunk)\n  : (thunk: () => void) => thunk();\n\ntype Version = number;\ntype Listener<Value> = (\n  action: { n: Version, p?: Promise<Value>, v?: Value }\n) => void\n\ntype ContextValue<Value> = {\n  [CONTEXT_VALUE]: {\n    /* \"v\"alue     */ v: MutableRefObject<Value>;\n    /* versio\"n\"   */ n: MutableRefObject<Version>;\n    /* \"l\"isteners */ l: Set<Listener<Value>>;\n    /* \"u\"pdate    */ u: (thunk: () => void, options?: { suspense: boolean }) => void;\n  };\n};\n\nexport interface Context<Value> {\n  Provider: ComponentType<{ value: Value; children: ReactNode }>;\n  displayName?: string;\n}\n\nconst createProvider = <Value>(\n  ProviderOrig: Provider<ContextValue<Value>>,\n) => {\n  const ContextProvider = ({ value, children }: { value: Value; children: ReactNode }) => {\n    const valueRef = useRef(value);\n    const versionRef = useRef(0);\n    const [resolve, setResolve] = useState<((v: Value) => void) | null>(null);\n    if (resolve) {\n      resolve(value);\n      setResolve(null);\n    }\n    const contextValue = useRef<ContextValue<Value>>();\n    if (!contextValue.current) {\n      const listeners = new Set<Listener<Value>>();\n      const update = (thunk: () => void, options?: { suspense: boolean }) => {\n        batchedUpdates(() => {\n          versionRef.current += 1;\n          const action: Parameters<Listener<Value>>[0] = {\n            n: versionRef.current,\n          };\n          if (options?.suspense) {\n            action.n *= -1; // this is intentional to make it temporary version\n            action.p = new Promise<Value>((r) => {\n              setResolve(() => (v: Value) => {\n                action.v = v;\n                delete action.p;\n                r(v);\n              });\n            });\n          }\n          listeners.forEach((listener) => listener(action));\n          thunk();\n        });\n      };\n      contextValue.current = {\n        [CONTEXT_VALUE]: {\n          /* \"v\"alue     */ v: valueRef,\n          /* versio\"n\"   */ n: versionRef,\n          /* \"l\"isteners */ l: listeners,\n          /* \"u\"pdate    */ u: update,\n        },\n      };\n    }\n    useIsomorphicLayoutEffect(() => {\n      valueRef.current = value;\n      versionRef.current += 1;\n      runWithNormalPriority(() => {\n        (contextValue.current as ContextValue<Value>)[CONTEXT_VALUE].l.forEach((listener) => {\n          listener({ n: versionRef.current, v: value });\n        });\n      });\n    }, [value]);\n    return createElement(ProviderOrig, { value: contextValue.current }, children);\n  };\n  return ContextProvider;\n};\n\nconst identity = <T>(x: T) => x;\n\n/**\n * This creates a special context for `useContextSelector`.\n *\n * @example\n * import { createContext } from 'use-context-selector';\n *\n * const PersonContext = createContext({ firstName: '', familyName: '' });\n */\nexport function createContext<Value>(defaultValue: Value) {\n  const context = createContextOrig<ContextValue<Value>>({\n    [CONTEXT_VALUE]: {\n      /* \"v\"alue     */ v: { current: defaultValue },\n      /* versio\"n\"   */ n: { current: -1 },\n      /* \"l\"isteners */ l: new Set(),\n      /* \"u\"pdate    */ u: (f) => f(),\n    },\n  });\n  (context as unknown as {\n    [ORIGINAL_PROVIDER]: Provider<ContextValue<Value>>;\n  })[ORIGINAL_PROVIDER] = context.Provider;\n  (context as unknown as Context<Value>).Provider = createProvider(context.Provider);\n  delete (context as any).Consumer; // no support for Consumer\n  return context as unknown as Context<Value>;\n}\n\n/**\n * This hook returns context selected value by selector.\n *\n * It will only accept context created by `createContext`.\n * It will trigger re-render if only the selected value is referentially changed.\n *\n * The selector should return referentially equal result for same input for better performance.\n *\n * @example\n * import { useContextSelector } from 'use-context-selector';\n *\n * const firstName = useContextSelector(PersonContext, state => state.firstName);\n */\nexport function useContextSelector<Value, Selected>(\n  context: Context<Value>,\n  selector: (value: Value) => Selected,\n) {\n  const contextValue = useContextOrig(\n    context as unknown as ContextOrig<ContextValue<Value>>,\n  )[CONTEXT_VALUE];\n  if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n    if (!contextValue) {\n      throw new Error('useContextSelector requires special context');\n    }\n  }\n  const {\n    /* \"v\"alue     */ v: { current: value },\n    /* versio\"n\"   */ n: { current: version },\n    /* \"l\"isteners */ l: listeners,\n  } = contextValue;\n  const selected = selector(value);\n  const [state, dispatch] = useReducer((\n    prev: readonly [Value, Selected],\n    action?: Parameters<Listener<Value>>[0],\n  ) => {\n    if (!action) {\n      // case for `dispatch()` below\n      return [value, selected] as const;\n    }\n    if ('p' in action) {\n      throw action.p;\n    }\n    if (action.n === version) {\n      if (Object.is(prev[1], selected)) {\n        return prev; // bail out\n      }\n      return [value, selected] as const;\n    }\n    try {\n      if ('v' in action) {\n        if (Object.is(prev[0], action.v)) {\n          return prev; // do not update\n        }\n        const nextSelected = selector(action.v);\n        if (Object.is(prev[1], nextSelected)) {\n          return prev; // do not update\n        }\n        return [action.v, nextSelected] as const;\n      }\n    } catch (e) {\n      // ignored (stale props or some other reason)\n    }\n    return [...prev] as const; // schedule update\n  }, [value, selected] as const);\n  if (!Object.is(state[1], selected)) {\n    // schedule re-render\n    // this is safe because it's self contained\n    dispatch();\n  }\n  useIsomorphicLayoutEffect(() => {\n    listeners.add(dispatch);\n    return () => {\n      listeners.delete(dispatch);\n    };\n  }, [listeners]);\n  return state[1];\n}\n\n/**\n * This hook returns the entire context value.\n * Use this instead of React.useContext for consistent behavior.\n *\n * @example\n * import { useContext } from 'use-context-selector';\n *\n * const person = useContext(PersonContext);\n */\nexport function useContext<Value>(context: Context<Value>) {\n  return useContextSelector(context, identity);\n}\n\n/**\n * This hook returns an update function that accepts a thunk function\n *\n * Use this for a function that will change a value in\n * concurrent rendering in React 18.\n * Otherwise, there's no need to use this hook.\n *\n * @example\n * import { useContextUpdate } from 'use-context-selector';\n *\n * const update = useContextUpdate();\n *\n * // Wrap set state function\n * update(() => setState(...));\n *\n * // Experimental suspense mode\n * update(() => setState(...), { suspense: true });\n */\nexport function useContextUpdate<Value>(context: Context<Value>) {\n  const contextValue = useContextOrig(\n    context as unknown as ContextOrig<ContextValue<Value>>,\n  )[CONTEXT_VALUE];\n  if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n    if (!contextValue) {\n      throw new Error('useContextUpdate requires special context');\n    }\n  }\n  const { u: update } = contextValue;\n  return update;\n}\n\n/**\n * This is a Provider component for bridging multiple react roots\n *\n * @example\n * const valueToBridge = useBridgeValue(PersonContext);\n * return (\n *   <Renderer>\n *     <BridgeProvider context={PersonContext} value={valueToBridge}>\n *       {children}\n *     </BridgeProvider>\n *   </Renderer>\n * );\n */\nexport const BridgeProvider = ({ context, value, children }:{\n  context: Context<any>;\n  value: any;\n  children: ReactNode;\n}) => {\n  const { [ORIGINAL_PROVIDER]: ProviderOrig } = context as unknown as {\n    [ORIGINAL_PROVIDER]: Provider<unknown>;\n  };\n  if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n    if (!ProviderOrig) {\n      throw new Error('BridgeProvider requires special context');\n    }\n  }\n  return createElement(ProviderOrig, { value }, children);\n};\n\n/**\n * This hook return a value for BridgeProvider\n */\nexport const useBridgeValue = (context: Context<any>) => {\n  const bridgeValue = useContextOrig(context as unknown as ContextOrig<ContextValue<unknown>>);\n  if (typeof process === 'object' && process.env.NODE_ENV !== 'production') {\n    if (!bridgeValue[CONTEXT_VALUE]) {\n      throw new Error('useBridgeValue requires special context');\n    }\n  }\n  return bridgeValue as any;\n};\n"], "names": ["CONTEXT_VALUE", "Symbol", "ORIGINAL_PROVIDER", "useIsomorphicLayoutEffect", "window", "test", "navigator", "userAgent", "useEffect", "useLayoutEffect", "runWithNormalPriority", "runWithPriority", "unstable_runWithPriority", "thunk", "NormalPriority", "identity", "x", "useContextSelector", "context", "selector", "contextValue", "useContextOrig", "useContext", "process", "env", "NODE_ENV", "Error", "value", "v", "current", "version", "n", "listeners", "l", "selected", "useReducer", "prev", "action", "p", "Object", "is", "nextSelected", "e", "state", "dispatch", "_useReducer", "add", "_ref2", "children", "Provider<PERSON><PERSON>", "createElement", "defaultValue", "_createContextOrig", "createContextOrig", "createContext", "Set", "u", "f", "Provider", "_ref", "valueRef", "useRef", "versionRef", "_useState", "useState", "resolve", "setResolve", "_contextValue$current", "options", "batchedUpdates", "unstable_batchedUpdates", "suspense", "Promise", "r", "for<PERSON>ach", "listener", "Consumer", "bridgeValue"], "mappings": "qXAsBA,IAAMA,EAAgBC,SACCC,EAAGD,SAKKE,EAHC,oBAAXC,QAChB,sBAAsBC,KAAKD,OAAOE,WAAaF,OAAOE,UAAUC,WAE3BC,EAAAA,UAAYC,EAAAA,gBAGhDC,EAAwBC,EAAeC,yBACzC,SAACC,GAAsBF,OAAAA,EAAAA,yBAAgBG,EAAAA,wBAAgBD,IACvD,SAACA,GAAD,OAA4BA,KA8E1BE,EAAW,SAAIC,GAASA,OAAAA,GAwCd,SAAAC,EACdC,EACAC,GAEA,IAAMC,EAAeC,EAAcC,WACjCJ,GACAlB,GACF,GAAuB,iBAAnBuB,SAAwD,eAAzBA,QAAQC,IAAIC,WACxCL,EACH,MAAUM,IAAAA,MAAM,+CAGpB,IAAAC,EAIIP,EAHgBQ,EAAKC,QACSC,EAE9BV,EAFgBW,EAAKF,QACFG,EACnBZ,EADgBa,EAEdC,EAAWf,EAASQ,GACAQ,EAAAA,EAAUA,WAAC,SACnCC,EACAC,GAEA,IAAKA,EAEH,MAAO,CAACV,EAAOO,GAEjB,GAAI,MAAOG,EACT,MAAMA,EAAOC,EAEf,GAAID,EAAON,IAAMD,EACf,OAAIS,OAAOC,GAAGJ,EAAK,GAAIF,GAEtBE,EACM,CAACT,EAAOO,GAEjB,IACE,GAAI,MAAJG,EAAmB,CACjB,GAAIE,OAAOC,GAAGJ,EAAK,GAAIC,EAAOT,GAC5B,OAAOQ,EAET,IAAkBK,EAAGtB,EAASkB,EAAOT,GACrC,OAAIW,OAAOC,GAAGJ,EAAK,GAAIK,GAEtBL,EACM,CAACC,EAAOT,EAAGa,IAEpB,MAAOC,IAGT,MAAWN,GAAAA,OAAAA,IACV,CAACT,EAAOO,IAhCJS,EAAOC,EAAAA,GAAAA,EAiCdC,EAAA,GAWA,OAXKN,OAAOC,GAAGG,EAAM,GAAIT,IAGvBU,IAEFzC,EAA0B,WAExB,OADA6B,EAAUc,IAAIF,GACP,WACLZ,EAAA,OAAiBY,KAElB,CAACZ,IACQW,EAAC,oBA4De,SAIzBI,GAAA,IAJqCpB,EAIrCoB,EAJqCpB,MAAOqB,EAI5CD,EAJ4CC,SAK/CC,EADGF,EAJ4B7B,QAKtBhB,GAGT,GAAuB,iBAAnBqB,SAAwD,eAAzBA,QAAQC,IAAIC,WACxCwB,EACH,UAAMvB,MAAU,2CAGpB,OAAOwB,EAAAA,cAAcD,EAAc,CAAEtB,MAAAA,GAASqB,oBArK1C,SAA+BG,GACnC,IAAAC,EAnEAH,IAmEgBI,EAAiBC,gBAC9BtD,EAAAA,IAAAA,GAAgB,CACG4B,EAAG,CAAEC,QAASsB,GACdpB,EAAG,CAAEF,SAAU,GACfI,EAAG,IAAIsB,IACPC,EAAG,SAACC,GAAMA,OAAAA,MALCL,IAajC,OALClC,EAEEhB,GAAqBgB,EAAQwC,SAC/BxC,EAAsCwC,UA9EvCT,EA8EiE/B,EAAQwC,SA5EjD,SAA+DC,GAAA,IAAAhC,EAAAgC,EAA5DhC,MAAOqB,EAAqDW,EAArDX,SAC1BY,EAAWC,EAAAA,OAAOlC,GACRmC,EAAGD,EAAAA,OAAO,GAC1BE,EAA8BC,EAAAA,SAAsC,MAA7DC,EAAPF,EAAA,GAAgBG,EAAhBH,EAAA,GACIE,IACFA,EAAQtC,GACRuC,EAAW,OAEb,IAAkB9C,EAAGyC,EAAAA,SACrB,IAAKzC,EAAaS,QAAS,CACzB,IAAAsC,EAAenC,EAAG,IAAIuB,IAqBtBnC,EAAaS,UAAbsC,EAAA,IACGnE,GAAgB,CACG4B,EAAGgC,EACH7B,EAAG+B,EACH7B,EAAGD,EACHwB,EAzBP,SAAC3C,EAAmBuD,GACjCC,EAAcC,wBAAC,WACbR,EAAWjC,SAAW,EACtB,IAAMQ,EAAyC,CAC7CN,EAAG+B,EAAWjC,SAEhB,MAAIuC,GAAAA,EAASG,WACXlC,EAAON,IAAM,EACbM,EAAOC,EAAI,IAAIkC,QAAe,SAACC,GAC7BP,EAAW,WAAM,OAAA,SAACtC,GAChBS,EAAOT,EAAIA,SACJS,EAAOC,EACdmC,EAAE7C,SAIRI,EAAU0C,QAAQ,SAACC,GAAaA,OAAAA,EAAStC,KACzCxB,QAGJsD,GAkBF,OATAhE,EAA0B,WACxByD,EAAS/B,QAAUF,EACnBmC,EAAWjC,SAAW,EACtBnB,EAAsB,WACnBU,EAAaS,QAAgC7B,GAAeiC,EAAEyC,QAAQ,SAACC,GACtEA,EAAS,CAAE5C,EAAG+B,EAAWjC,QAASD,EAAGD,SAGxC,CAACA,IACGuB,EAAaA,cAACD,EAAc,CAAEtB,MAAOP,EAAaS,SAAWmB,YA4B/C9B,EAAC0D,SACjB1D,oBA6JqB,SAACA,GAC7B,IAAiB2D,EAAGxD,EAAAA,WAAeH,GACnC,GAAuB,iBAAZK,SAAiD,eAAzBA,QAAQC,IAAIC,WACxCoD,EAAY7E,GACf,MAAM,IAAA0B,MAAU,2CAGpB,OAAOmD,gBA1EH,SAA4B3D,GAChC,OAAyBD,EAACC,EAASH,8CAqB/B,SAAkCG,GACtC,IAAkBE,EAAGC,EAAAA,WACnBH,GACAlB,GACF,GAAuB,iBAAZuB,SAAiD,eAAzBA,QAAQC,IAAIC,WACxCL,EACH,MAAM,IAAAM,MAAU,6CAIpB,OADsBN,EAAdoC"}