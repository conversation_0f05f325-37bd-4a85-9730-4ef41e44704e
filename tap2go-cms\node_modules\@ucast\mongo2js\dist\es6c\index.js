"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@ucast/core"),t=require("@ucast/mongo"),n=require("@ucast/js");function r(e){return null===e||"object"!=typeof e?e:e instanceof Date?e.getTime():e&&"function"==typeof e.toJSON?e.toJSON():e}const o=(e,t)=>n.compare(r(e),r(t));function c(r,c,u){const s=new t.MongoQueryParser(r),i=n.createJsInterpreter(c,Object.assign({compare:o},u));if(u&&u.forPrimitives){const t={field:e.ITSELF},n=s.parse;s.setParse(e=>n(e,t))}return e.createTranslatorFactory(s.parse,i)}const u=c(t.allParsingInstructions,n.allInterpreters),s=c(["$and","$or"].reduce((e,t)=>(e[t]=Object.assign({},e[t],{type:"field"}),e),Object.assign({},t.allParsingInstructions,{$nor:Object.assign({},t.allParsingInstructions.$nor,{type:"field",parse:t.defaultParsers.compound})})),n.allInterpreters,{forPrimitives:!0}),i=u;Object.keys(e).forEach((function(t){"default"!==t&&Object.defineProperty(exports,t,{enumerable:!0,get:function(){return e[t]}})})),Object.keys(t).forEach((function(e){"default"!==e&&Object.defineProperty(exports,e,{enumerable:!0,get:function(){return t[e]}})})),Object.keys(n).forEach((function(e){"default"!==e&&Object.defineProperty(exports,e,{enumerable:!0,get:function(){return n[e]}})})),exports.createFactory=c,exports.filter=i,exports.guard=u,exports.squire=s;
//# sourceMappingURL=index.js.map
