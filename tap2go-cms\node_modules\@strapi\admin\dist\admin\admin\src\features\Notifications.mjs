import { jsxs, jsx } from 'react/jsx-runtime';
import * as React from 'react';
import { Flex, useCallbackRef, <PERSON><PERSON>, <PERSON> } from '@strapi/design-system';
import { useIntl } from 'react-intl';

const NotificationsContext = /*#__PURE__*/ React.createContext({
    toggleNotification: ()=>{}
});
/**
 * @internal
 * @description DO NOT USE. This will be removed before stable release of v5.
 */ const NotificationsProvider = ({ children })=>{
    const notificationIdRef = React.useRef(0);
    const [notifications, setNotifications] = React.useState([]);
    const toggleNotification = React.useCallback(({ type, message, link, timeout, blockTransition, onClose, title })=>{
        setNotifications((s)=>[
                ...s,
                {
                    id: notificationIdRef.current++,
                    type,
                    message,
                    link,
                    timeout,
                    blockTransition,
                    onClose,
                    title
                }
            ]);
    }, []);
    const clearNotification = React.useCallback((id)=>{
        setNotifications((s)=>s.filter((n)=>n.id !== id));
    }, []);
    const value = React.useMemo(()=>({
            toggleNotification
        }), [
        toggleNotification
    ]);
    return /*#__PURE__*/ jsxs(NotificationsContext.Provider, {
        value: value,
        children: [
            /*#__PURE__*/ jsx(Flex, {
                left: "50%",
                marginLeft: "-250px",
                position: "fixed",
                direction: "column",
                alignItems: "stretch",
                gap: 2,
                top: `4.6rem`,
                width: `50rem`,
                zIndex: "notification",
                children: notifications.map((notification)=>{
                    return /*#__PURE__*/ jsx(Notification, {
                        ...notification,
                        clearNotification: clearNotification
                    }, notification.id);
                })
            }),
            children
        ]
    });
};
const Notification = ({ clearNotification, blockTransition = false, id, link, message, onClose, timeout = 2500, title, type })=>{
    const { formatMessage } = useIntl();
    /**
   * Chances are `onClose` won't be classed as stabilised,
   * so we use `useCallbackRef` to avoid make it stable.
   */ const onCloseCallback = useCallbackRef(onClose);
    const handleClose = React.useCallback(()=>{
        onCloseCallback();
        clearNotification(id);
    }, [
        clearNotification,
        id,
        onCloseCallback
    ]);
    // eslint-disable-next-line consistent-return
    React.useEffect(()=>{
        if (!blockTransition) {
            const timeoutReference = setTimeout(()=>{
                handleClose();
            }, timeout);
            return ()=>{
                clearTimeout(timeoutReference);
            };
        }
    }, [
        blockTransition,
        handleClose,
        timeout
    ]);
    let variant;
    let alertTitle;
    if (type === 'info') {
        variant = 'default';
        alertTitle = formatMessage({
            id: 'notification.default.title',
            defaultMessage: 'Information:'
        });
    } else if (type === 'danger') {
        variant = 'danger';
        alertTitle = formatMessage({
            id: 'notification.warning.title',
            defaultMessage: 'Warning:'
        });
    } else if (type === 'warning') {
        variant = 'warning';
        alertTitle = formatMessage({
            id: 'notification.warning.title',
            defaultMessage: 'Warning:'
        });
    } else {
        variant = 'success';
        alertTitle = formatMessage({
            id: 'notification.success.title',
            defaultMessage: 'Success:'
        });
    }
    if (title) {
        alertTitle = title;
    }
    return /*#__PURE__*/ jsx(Alert, {
        action: link ? /*#__PURE__*/ jsx(Link, {
            href: link.url,
            isExternal: true,
            children: link.label
        }) : undefined,
        onClose: handleClose,
        closeLabel: formatMessage({
            id: 'global.close',
            defaultMessage: 'Close'
        }),
        title: alertTitle,
        variant: variant,
        children: message
    });
};
/* -------------------------------------------------------------------------------------------------
 * Hook
 * -----------------------------------------------------------------------------------------------*/ /**
 * @preserve
 * @description Returns an object to interact with the notification
 * system. The callbacks are wrapped in `useCallback` for a stable
 * identity.
 *
 * @example
 * ```tsx
 * import { useNotification } from '@strapi/strapi/admin';
 *
 * const MyComponent = () => {
 *  const { toggleNotification } = useNotification();
 *
 *  return <button onClick={() => toggleNotification({ message: 'Hello world!' })}>Click me</button>;
 */ const useNotification = ()=>React.useContext(NotificationsContext);

export { NotificationsProvider, useNotification };
//# sourceMappingURL=Notifications.mjs.map
