{"version": 3, "file": "useWebhooks.mjs", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Webhooks/hooks/useWebhooks.ts"], "sourcesContent": ["import { SerializedError } from '@reduxjs/toolkit';\n\nimport { GetWebhook, GetWebhooks } from '../../../../../../../shared/contracts/webhooks';\nimport {\n  useGetWebhooksQuery,\n  useCreateWebhookMutation,\n  useUpdateWebhookMutation,\n  useTriggerWebhookMutation,\n  useDeleteManyWebhooksMutation,\n} from '../../../../../services/webhooks';\nimport { BaseQueryError } from '../../../../../utils/baseQuery';\n\nconst useWebhooks = (\n  args: GetWebhook.Params | void = undefined,\n  queryArgs?: Parameters<typeof useGetWebhooksQuery>[1]\n) => {\n  const { data: webhooks, isLoading, error } = useGetWebhooksQuery(args, queryArgs);\n  const [createWebhook, { error: createError }] = useCreateWebhookMutation();\n  const [updateWebhook, { error: updateError }] = useUpdateWebhookMutation();\n\n  const [triggerWebhook] = useTriggerWebhookMutation();\n  const [deleteManyWebhooks] = useDeleteManyWebhooksMutation();\n\n  return {\n    webhooks: webhooks as GetWebhooks.Response['data'] | undefined,\n    isLoading: isLoading as boolean,\n    error: (error || createError || updateError) as BaseQueryError | SerializedError,\n    createWebhook,\n    updateWebhook,\n    triggerWebhook,\n    deleteManyWebhooks,\n  };\n};\n\nexport { useWebhooks };\n"], "names": ["useWebhooks", "args", "undefined", "queryArgs", "data", "webhooks", "isLoading", "error", "useGetWebhooksQuery", "createWebhook", "createError", "useCreateWebhookMutation", "updateWebhook", "updateError", "useUpdateWebhookMutation", "triggerWebhook", "useTriggerWebhookMutation", "deleteManyWebhooks", "useDeleteManyWebhooksMutation"], "mappings": ";;AAYA,MAAMA,WAAc,GAAA,CAClBC,IAAiCC,GAAAA,SAAS,EAC1CC,SAAAA,GAAAA;IAEA,MAAM,EAAEC,IAAMC,EAAAA,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAE,GAAGC,mBAAAA,CAAoBP,IAAME,EAAAA,SAAAA,CAAAA;AACvE,IAAA,MAAM,CAACM,aAAe,EAAA,EAAEF,OAAOG,WAAW,EAAE,CAAC,GAAGC,wBAAAA,EAAAA;AAChD,IAAA,MAAM,CAACC,aAAe,EAAA,EAAEL,OAAOM,WAAW,EAAE,CAAC,GAAGC,wBAAAA,EAAAA;IAEhD,MAAM,CAACC,eAAe,GAAGC,yBAAAA,EAAAA;IACzB,MAAM,CAACC,mBAAmB,GAAGC,6BAAAA,EAAAA;IAE7B,OAAO;QACLb,QAAUA,EAAAA,QAAAA;QACVC,SAAWA,EAAAA,SAAAA;AACXC,QAAAA,KAAAA,EAAQA,SAASG,WAAeG,IAAAA,WAAAA;AAChCJ,QAAAA,aAAAA;AACAG,QAAAA,aAAAA;AACAG,QAAAA,cAAAA;AACAE,QAAAA;AACF,KAAA;AACF;;;;"}