'use client';

import { useState, useEffect } from 'react';
import { TagIcon, PlusIcon, PhotoIcon } from '@heroicons/react/24/outline';

export default function BannersManagement() {
  const [loading, setLoading] = useState(false);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <TagIcon className="w-8 h-8 text-orange-500 mr-3" />
                Banner Management
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage homepage banners and promotional displays
              </p>
            </div>
            <button className="bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-orange-700 transition-colors">
              <PlusIcon className="w-5 h-5" />
              <span>New Banner</span>
            </button>
          </div>
        </div>
      </div>

      {/* Banners Grid */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Homepage Banners</h2>
        </div>
        
        <div className="p-8 text-center">
          <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No banners created yet</p>
          <p className="text-sm text-gray-400">
            Create eye-catching banners for your homepage and marketing campaigns
          </p>
        </div>
      </div>
    </div>
  );
}
