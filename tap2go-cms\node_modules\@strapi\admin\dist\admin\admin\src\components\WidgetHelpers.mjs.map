{"version": 3, "file": "WidgetHelpers.mjs", "sources": ["../../../../../admin/src/components/WidgetHelpers.tsx"], "sourcesContent": ["import { Flex, Loader, Typography } from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { EmptyDocuments, EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\n/* -------------------------------------------------------------------------------------------------\n * Loading\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LoadingProps {\n  children?: string;\n}\n\nconst Loading = ({ children }: LoadingProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" justifyContent=\"center\" alignItems=\"center\">\n      <Loader>\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.loading',\n            defaultMessage: 'Loading widget content',\n          })}\n      </Loader>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Error\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ErrorProps {\n  children?: string;\n}\n\nconst Error = ({ children }: ErrorProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" direction=\"column\" justifyContent=\"center\" alignItems=\"center\" gap={2}>\n      <WarningCircle width=\"3.2rem\" height=\"3.2rem\" fill=\"danger600\" />\n      <Typography variant=\"delta\">\n        {formatMessage({\n          id: 'global.error',\n          defaultMessage: 'Something went wrong',\n        })}\n      </Typography>\n      <Typography textColor=\"neutral600\">\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.error',\n            defaultMessage: \"Couldn't load widget content.\",\n          })}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NoData\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NoDataProps {\n  children?: string;\n}\n\nconst NoData = ({ children }: NoDataProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" direction=\"column\" justifyContent=\"center\" alignItems=\"center\" gap={6}>\n      <EmptyDocuments width=\"16rem\" height=\"8.8rem\" />\n      <Typography textColor=\"neutral600\">\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.no-data',\n            defaultMessage: 'No content found.',\n          })}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NoPermissions\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NoPermissionsProps {\n  children?: string;\n}\n\nconst NoPermissions = ({ children }: NoPermissionsProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" direction=\"column\" justifyContent=\"center\" alignItems=\"center\" gap={6}>\n      <EmptyPermissions width=\"16rem\" height=\"8.8rem\" />\n      <Typography textColor=\"neutral600\">\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.no-permissions',\n            defaultMessage: 'You don’t have the permission to see this widget',\n          })}\n      </Typography>\n    </Flex>\n  );\n};\n\nconst Widget = {\n  Loading,\n  Error,\n  NoData,\n  NoPermissions,\n};\n\nexport { Widget };\n"], "names": ["Loading", "children", "formatMessage", "useIntl", "_jsx", "Flex", "height", "justifyContent", "alignItems", "Loader", "id", "defaultMessage", "Error", "_jsxs", "direction", "gap", "WarningCircle", "width", "fill", "Typography", "variant", "textColor", "NoData", "EmptyDocuments", "NoPermissions", "EmptyPermissions", "Widget"], "mappings": ";;;;;;AAaA,MAAMA,OAAU,GAAA,CAAC,EAAEC,QAAQ,EAAgB,GAAA;IACzC,MAAM,EAAEC,aAAa,EAAE,GAAGC,OAAAA,EAAAA;AAE1B,IAAA,qBACEC,GAACC,CAAAA,IAAAA,EAAAA;QAAKC,MAAO,EAAA,MAAA;QAAOC,cAAe,EAAA,QAAA;QAASC,UAAW,EAAA,QAAA;AACrD,QAAA,QAAA,gBAAAJ,GAACK,CAAAA,MAAAA,EAAAA;AACER,YAAAA,QAAAA,EAAAA,QAAAA,IACCC,aAAc,CAAA;gBACZQ,EAAI,EAAA,yBAAA;gBACJC,cAAgB,EAAA;AAClB,aAAA;;;AAIV,CAAA;AAUA,MAAMC,KAAQ,GAAA,CAAC,EAAEX,QAAQ,EAAc,GAAA;IACrC,MAAM,EAAEC,aAAa,EAAE,GAAGC,OAAAA,EAAAA;AAE1B,IAAA,qBACEU,IAACR,CAAAA,IAAAA,EAAAA;QAAKC,MAAO,EAAA,MAAA;QAAOQ,SAAU,EAAA,QAAA;QAASP,cAAe,EAAA,QAAA;QAASC,UAAW,EAAA,QAAA;QAASO,GAAK,EAAA,CAAA;;0BACtFX,GAACY,CAAAA,aAAAA,EAAAA;gBAAcC,KAAM,EAAA,QAAA;gBAASX,MAAO,EAAA,QAAA;gBAASY,IAAK,EAAA;;0BACnDd,GAACe,CAAAA,UAAAA,EAAAA;gBAAWC,OAAQ,EAAA,OAAA;0BACjBlB,aAAc,CAAA;oBACbQ,EAAI,EAAA,cAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;;0BAEFP,GAACe,CAAAA,UAAAA,EAAAA;gBAAWE,SAAU,EAAA,YAAA;AACnBpB,gBAAAA,QAAAA,EAAAA,QAAAA,IACCC,aAAc,CAAA;oBACZQ,EAAI,EAAA,uBAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;;;;AAIV,CAAA;AAUA,MAAMW,MAAS,GAAA,CAAC,EAAErB,QAAQ,EAAe,GAAA;IACvC,MAAM,EAAEC,aAAa,EAAE,GAAGC,OAAAA,EAAAA;AAE1B,IAAA,qBACEU,IAACR,CAAAA,IAAAA,EAAAA;QAAKC,MAAO,EAAA,MAAA;QAAOQ,SAAU,EAAA,QAAA;QAASP,cAAe,EAAA,QAAA;QAASC,UAAW,EAAA,QAAA;QAASO,GAAK,EAAA,CAAA;;0BACtFX,GAACmB,CAAAA,cAAAA,EAAAA;gBAAeN,KAAM,EAAA,OAAA;gBAAQX,MAAO,EAAA;;0BACrCF,GAACe,CAAAA,UAAAA,EAAAA;gBAAWE,SAAU,EAAA,YAAA;AACnBpB,gBAAAA,QAAAA,EAAAA,QAAAA,IACCC,aAAc,CAAA;oBACZQ,EAAI,EAAA,yBAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;;;;AAIV,CAAA;AAUA,MAAMa,aAAgB,GAAA,CAAC,EAAEvB,QAAQ,EAAsB,GAAA;IACrD,MAAM,EAAEC,aAAa,EAAE,GAAGC,OAAAA,EAAAA;AAE1B,IAAA,qBACEU,IAACR,CAAAA,IAAAA,EAAAA;QAAKC,MAAO,EAAA,MAAA;QAAOQ,SAAU,EAAA,QAAA;QAASP,cAAe,EAAA,QAAA;QAASC,UAAW,EAAA,QAAA;QAASO,GAAK,EAAA,CAAA;;0BACtFX,GAACqB,CAAAA,gBAAAA,EAAAA;gBAAiBR,KAAM,EAAA,OAAA;gBAAQX,MAAO,EAAA;;0BACvCF,GAACe,CAAAA,UAAAA,EAAAA;gBAAWE,SAAU,EAAA,YAAA;AACnBpB,gBAAAA,QAAAA,EAAAA,QAAAA,IACCC,aAAc,CAAA;oBACZQ,EAAI,EAAA,gCAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;;;;AAIV,CAAA;AAEA,MAAMe,MAAS,GAAA;AACb1B,IAAAA,OAAAA;AACAY,IAAAA,KAAAA;AACAU,IAAAA,MAAAA;AACAE,IAAAA;AACF;;;;"}