{"version": 3, "file": "users.mjs", "sources": ["../../../../../admin/src/utils/users.ts"], "sourcesContent": ["import type { User } from '../features/Auth';\n\n/* -------------------------------------------------------------------------------------------------\n * getDisplayName\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Retrieves the display name of an admin panel user\n */\nconst getDisplayName = ({ firstname, lastname, username, email }: Partial<User> = {}): string => {\n  if (username) {\n    return username;\n  }\n\n  // firstname is not required if the user is created with a username\n  if (firstname) {\n    return `${firstname} ${lastname ?? ''}`.trim();\n  }\n\n  return email ?? '';\n};\n\n/* -------------------------------------------------------------------------------------------------\n * hashAdminUserEmail\n * -----------------------------------------------------------------------------------------------*/\n\nconst hashAdminUserEmail = async (payload?: User) => {\n  if (!payload || !payload.email) {\n    return null;\n  }\n\n  try {\n    return await digestMessage(payload.email);\n  } catch (error) {\n    return null;\n  }\n};\n\nconst bufferToHex = (buffer: ArrayBuffer) => {\n  return [...new Uint8Array(buffer)].map((b) => b.toString(16).padStart(2, '0')).join('');\n};\nconst digestMessage = async (message: string) => {\n  const msgUint8 = new TextEncoder().encode(message);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8);\n\n  return bufferToHex(hashBuffer);\n};\n\nexport { getDisplayName, hashAdminUserEmail };\n"], "names": ["getDisplayName", "firstname", "lastname", "username", "email", "trim", "hashAdminUserEmail", "payload", "digestMessage", "error", "bufferToHex", "buffer", "Uint8Array", "map", "b", "toString", "padStart", "join", "message", "msgUint8", "TextEncoder", "encode", "hash<PERSON><PERSON><PERSON>", "crypto", "subtle", "digest"], "mappings": "AAEA;;;;AAMC,IACKA,MAAAA,cAAAA,GAAiB,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAiB,GAAG,EAAE,GAAA;AAClF,IAAA,IAAID,QAAU,EAAA;QACZ,OAAOA,QAAAA;AACT;;AAGA,IAAA,IAAIF,SAAW,EAAA;QACb,OAAO,CAAC,EAAEA,SAAU,CAAA,CAAC,EAAEC,QAAY,IAAA,EAAA,CAAG,CAAC,CAACG,IAAI,EAAA;AAC9C;AAEA,IAAA,OAAOD,KAAS,IAAA,EAAA;AAClB;AAEA;;qGAIA,MAAME,qBAAqB,OAAOC,OAAAA,GAAAA;AAChC,IAAA,IAAI,CAACA,OAAAA,IAAW,CAACA,OAAAA,CAAQH,KAAK,EAAE;QAC9B,OAAO,IAAA;AACT;IAEA,IAAI;QACF,OAAO,MAAMI,aAAcD,CAAAA,OAAAA,CAAQH,KAAK,CAAA;AAC1C,KAAA,CAAE,OAAOK,KAAO,EAAA;QACd,OAAO,IAAA;AACT;AACF;AAEA,MAAMC,cAAc,CAACC,MAAAA,GAAAA;IACnB,OAAO;AAAI,QAAA,GAAA,IAAIC,UAAWD,CAAAA,MAAAA;AAAQ,KAAA,CAACE,GAAG,CAAC,CAACC,CAAAA,GAAMA,CAAEC,CAAAA,QAAQ,CAAC,EAAA,CAAA,CAAIC,QAAQ,CAAC,CAAG,EAAA,GAAA,CAAA,CAAA,CAAMC,IAAI,CAAC,EAAA,CAAA;AACtF,CAAA;AACA,MAAMT,gBAAgB,OAAOU,OAAAA,GAAAA;AAC3B,IAAA,MAAMC,QAAW,GAAA,IAAIC,WAAcC,EAAAA,CAAAA,MAAM,CAACH,OAAAA,CAAAA;AAC1C,IAAA,MAAMI,aAAa,MAAMC,MAAAA,CAAOC,MAAM,CAACC,MAAM,CAAC,SAAWN,EAAAA,QAAAA,CAAAA;AAEzD,IAAA,OAAOT,WAAYY,CAAAA,UAAAA,CAAAA;AACrB,CAAA;;;;"}