import register from './register.mjs';
import bootstrap from './bootstrap.mjs';
import destroy from './destroy.mjs';
import adminContentTypes from './content-types/index.mjs';
import services from './services/index.mjs';
import controllers from './controllers/index.mjs';
import routes from './routes/index.mjs';
import auditLogsRoutes from './audit-logs/routes/audit-logs.mjs';
import auditLogsController from './audit-logs/controllers/audit-logs.mjs';
import { createAuditLogsService } from './audit-logs/services/audit-logs.mjs';
import { createAuditLogsLifecycleService } from './audit-logs/services/lifecycles.mjs';
import { auditLog } from './audit-logs/content-types/audit-log.mjs';

const getAdminEE = ()=>{
    const eeAdmin = {
        register,
        bootstrap,
        destroy,
        contentTypes: {
            // Always register the audit-log content type to prevent data loss
            'audit-log': auditLog,
            ...adminContentTypes
        },
        services,
        controllers,
        routes
    };
    // Only add the other audit-logs APIs if the feature is enabled by the user and the license
    if (strapi.config.get('admin.auditLogs.enabled', true) && strapi.ee.features.isEnabled('audit-logs')) {
        return {
            ...eeAdmin,
            controllers: {
                ...eeAdmin.controllers,
                'audit-logs': auditLogsController
            },
            routes: {
                ...eeAdmin.routes,
                'audit-logs': auditLogsRoutes
            },
            async register ({ strapi: strapi1 }) {
                // Run the the default registration
                await eeAdmin.register({
                    strapi: strapi1
                });
                // Register an internal audit logs service
                strapi1.add('audit-logs', createAuditLogsService(strapi1));
                // Register an internal audit logs lifecycle service
                const auditLogsLifecycle = createAuditLogsLifecycleService(strapi1);
                strapi1.add('audit-logs-lifecycle', auditLogsLifecycle);
                await auditLogsLifecycle.register();
            },
            async destroy ({ strapi: strapi1 }) {
                strapi1.get('audit-logs-lifecycle').destroy();
                await eeAdmin.destroy({
                    strapi: strapi1
                });
            }
        };
    }
    return eeAdmin;
};

export { getAdminEE as default };
//# sourceMappingURL=index.mjs.map
