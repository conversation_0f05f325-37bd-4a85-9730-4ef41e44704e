{"version": 3, "file": "usePrev.js", "sources": ["../../../../../admin/src/hooks/usePrev.ts"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\nexport const usePrev = <T>(value: T): T | undefined => {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n};\n"], "names": ["usePrev", "value", "ref", "useRef", "useEffect", "current"], "mappings": ";;;;AAEO,MAAMA,UAAU,CAAIC,KAAAA,GAAAA;AACzB,IAAA,MAAMC,GAAMC,GAAAA,YAAAA,EAAAA;IAEZC,eAAU,CAAA,IAAA;AACRF,QAAAA,GAAAA,CAAIG,OAAO,GAAGJ,KAAAA;KACb,EAAA;AAACA,QAAAA;AAAM,KAAA,CAAA;AAEV,IAAA,OAAOC,IAAIG,OAAO;AACpB;;;;"}