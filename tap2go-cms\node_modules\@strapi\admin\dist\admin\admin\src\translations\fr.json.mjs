var Analytics = "Statistiques";
var Documentation = "Documentation";
var Email = "E-mail";
var Password = "Mot de passe";
var Provider = "Provider";
var ResetPasswordToken = "ResetPasswordToken";
var Role = "Rôle";
var Username = "Nom d'utilisateur";
var Users = "Utilisateurs";
var anErrorOccurred = "Oups ! Une erreur s'est produite. Veuillez réessayer.";
var noPreview = "Aucun aperçu disponible";
var clearLabel = "Vider";
var dark = "Sombre";
var light = "Clair";
var or = "OU";
var selectButtonTitle = "Sélectionner";
var skipToContent = "Aller au contenu";
var submit = "Soumettre";
var fr = {
    Analytics: Analytics,
    "Auth.components.Oops.text": "Votre compte a été suspendu.",
    "Auth.components.Oops.text.admin": "Si c'est une erreur, veuillez contacter votre administrateur.",
    "Auth.components.Oops.title": "Oups !",
    "Auth.form.active.label": "Actif",
    "Auth.form.button.forgot-password": "Envoyer à nouveau",
    "Auth.form.button.go-home": "Retour à l'accueil",
    "Auth.form.button.login": "Se connecter",
    "Auth.form.button.login.providers.error": "Nous ne pouvons pas vous connecter via le fournisseur sélectionné",
    "Auth.form.button.login.strapi": "Se connecter avec Strapi",
    "Auth.form.button.password-recovery": "Récupération de mot de passe",
    "Auth.form.button.register": "Prêt à commencer",
    "Auth.form.confirmPassword.label": "Confirmation du mot de passe",
    "Auth.form.currentPassword.label": "Mot de passe actuel",
    "Auth.form.email.label": "Email",
    "Auth.form.email.placeholder": "<EMAIL>",
    "Auth.form.error.blocked": "Votre compte a été bloqué par l'administrateur.",
    "Auth.form.error.code.provide": "Le code est incorrect.",
    "Auth.form.error.confirmed": "L'e-mail de votre compte n'est pas confirmé.",
    "Auth.form.error.email.invalid": "Cette e-mail n'est pas valide.",
    "Auth.form.error.email.provide": "Votre identifiant est manquant.",
    "Auth.form.error.email.taken": "Cet e-mail est déjà utilisé",
    "Auth.form.error.invalid": "Votre identifiant ou mot de passe est incorrect.",
    "Auth.form.error.params.provide": "Les informations sont incorrectes.",
    "Auth.form.error.password.format": "Votre mot de passe ne peut pas contenir trois fois le symbole `$`.",
    "Auth.form.error.password.local": "Ce compte n'a pas de mot de passe.",
    "Auth.form.error.password.matching": "Les mots de passe ne sont pas identique.",
    "Auth.form.error.password.provide": "Votre mot de passe est manquant.",
    "Auth.form.error.ratelimit": "Trop de tentatives, veuillez réessayer dans une minute.",
    "Auth.form.error.user.not-exist": "Cette e-mail n'existe pas.",
    "Auth.form.error.username.taken": "Ce nom est déjà utilisé",
    "Auth.form.firstname.label": "Prénom",
    "Auth.form.firstname.placeholder": "John",
    "Auth.form.forgot-password.email.label": "Entrez votre e-mail",
    "Auth.form.forgot-password.email.label.success": "E-mail envoyé avec succès à l'adresse suivante",
    "Auth.form.lastname.label": "Nom",
    "Auth.form.lastname.placeholder": "Doe",
    "Auth.form.password.hide-password": "Cacher le mot de passe",
    "Auth.form.password.hint": "Le mot de passe doit contenir au moins 8 caractères, 1 majuscule, 1 minuscule et 1 chiffre.",
    "Auth.form.password.show-password": "Afficher le mot de passe",
    "Auth.form.register.news.label": "Me tenir au courant des nouvelles fonctionnalités et améliorations à venir (en faisant cela vous acceptez les {terms} et {policy}).",
    "Auth.form.register.subtitle": "Vos identifiants sont utilisé uniquement pour vous authentifier sur l'interface d'administration. Toutes les données sauvegardées seront stockées dans votre propre base de données.",
    "Auth.form.rememberMe.label": "Se souvenir de moi",
    "Auth.form.username.label": "Nom d'utilisateur",
    "Auth.form.username.placeholder": "Kai Doe",
    "Auth.form.welcome.subtitle": "Connectez-vous à votre compte Strapi",
    "Auth.form.welcome.title": "Bienvenue !",
    "Auth.link.forgot-password": "Mot de passe oublié ?",
    "Auth.link.ready": "Prêt à vous connecter ?",
    "Auth.link.signin": "Connexion",
    "Auth.link.signin.account": "Vous avez déjà un compte ?",
    "Auth.login.sso.divider": "Ou connectez-vous avec",
    "Auth.login.sso.loading": "Chargement des fournisseurs",
    "Auth.login.sso.subtitle": "Vous connecter via SSO",
    "Auth.privacy-policy-agreement.policy": "la politique de confidentialité",
    "Auth.privacy-policy-agreement.terms": "termes",
    "Auth.reset-password.title": "Réinitialiser le mot de passe",
    "Content Manager": "Content Manager",
    "Content Type Builder": "Content Types Builder",
    Documentation: Documentation,
    Email: Email,
    "Files Upload": "Téléversement de fichiers",
    "HomePage.head.title": "Accueil",
    "HomePage.roadmap": "Voir la roadmap",
    "HomePage.welcome.congrats": "Bravo !",
    "HomePage.welcome.congrats.content": "Vous êtes connecté en tant que premier Administrateur. Afin de découvrir les fonctionnalités proposées par Strapi,",
    "HomePage.welcome.congrats.content.bold": "nous vous conseillons de créer votre première Collection.",
    "Media Library": "Mediathèque",
    "New entry": "Nouvelle entrée",
    Password: Password,
    Provider: Provider,
    ResetPasswordToken: ResetPasswordToken,
    Role: Role,
    "Roles & Permissions": "Rôles & Permissions",
    "Roles.ListPage.notification.delete-all-not-allowed": "Certains rôles n'ont pas pu être supprimés car ils sont associés à des utilisateurs.",
    "Roles.ListPage.notification.delete-not-allowed": "Un rôle ne peu pas être supprimé s'il est associé à des utilisateurs.",
    "Roles.RoleRow.select-all": "Sélectionner {name} pour action groupée",
    "Roles.RoleRow.user-count": "{number, plural, =0 {# utilisateur} one {# utilisateur} other {# utilisateurs}}",
    "Roles.components.List.empty.withSearch": "Il n'y a pas de rôles correspondant à la recherche ({search})...",
    "Settings.PageTitle": "Réglages - {name}",
    "Settings.apiTokens.ListView.headers.createdAt": "Créé le",
    "Settings.apiTokens.ListView.headers.description": "Description",
    "Settings.apiTokens.ListView.headers.lastUsedAt": "Dernière utilisation le",
    "Settings.apiTokens.ListView.headers.name": "Nom",
    "Settings.apiTokens.ListView.headers.type": "Type de jeton",
    "Settings.apiTokens.regenerate": "Régénérer",
    "Settings.apiTokens.createPage.title": "Créer un jeton d'API",
    "Settings.transferTokens.createPage.title": "Créer un jeton de transfert",
    "Settings.tokens.RegenerateDialog.title": "Régénérer le jeton",
    "Settings.apiTokens.addFirstToken": "Ajouter votre premier jeton d'API",
    "Settings.apiTokens.addNewToken": "Ajouter un nouveau jeton d'API",
    "Settings.tokens.copy.editMessage": "Pour des raisons de sécurité, vous ne pouvoir voir votre jeton qu'une seule fois",
    "Settings.tokens.copy.editTitle": "Ce jeton n'est désormais plus accessible",
    "Settings.tokens.copy.lastWarning": "Assurez-vous de copier ce jeton, vous ne pourrez plus le revoir par la suite !",
    "Settings.apiTokens.create": "Ajouter une entrée",
    "Settings.apiTokens.createPage.permissions.description": "Seules les actions rattachées à une route sont listées ci-dessous.",
    "Settings.apiTokens.createPage.permissions.title": "Permissions",
    "Settings.apiTokens.description": "Liste des jetons générés pour consommer l'API",
    "Settings.apiTokens.createPage.BoundRoute.title": "Route rattachée à",
    "Settings.apiTokens.createPage.permissions.header.title": "Paramètres avancés",
    "Settings.apiTokens.createPage.permissions.header.hint": "Sélectionner les actions de l'application ou du plugin et sur l'icône de la roue crantée pour afficher la route rattachée",
    "Settings.apiTokens.lastHour": "dernière heure",
    "Settings.tokens.duration.30-days": "30 jours",
    "Settings.tokens.duration.7-days": "7 jours",
    "Settings.tokens.duration.90-days": "90 jours",
    "Settings.tokens.duration.expiration-date": "Date d'expiration",
    "Settings.tokens.duration.unlimited": "Illimité",
    "Settings.apiTokens.emptyStateLayout": "Vous n'avez pas encore de contenu...",
    "Settings.tokens.form.duration": "Durée de vie du jeton",
    "Settings.tokens.form.type": "Type de jeton",
    "Settings.tokens.form.name": "Nom",
    "Settings.tokens.form.description": "Description",
    "Settings.tokens.notification.copied": "Jeton copié dans le press-papiers.",
    "Settings.tokens.popUpWarning.message": "Êtes-vous sûr(e) de vouloir régénérer ce jeton ?",
    "Settings.tokens.Button.cancel": "Annuler",
    "Settings.tokens.Button.regenerate": "Régénérer",
    "Settings.tokens.types.full-access": "Accès total",
    "Settings.tokens.types.read-only": "Lecture seule",
    "Settings.tokens.types.custom": "Custom",
    "Settings.tokens.regenerate": "Régénérer",
    "Settings.transferTokens.title": "Jetons de transfert",
    "Settings.transferTokens.description": "Liste des jetons de transfert générés",
    "Settings.transferTokens.create": "Créer un nouveau jeton de transfert",
    "Settings.transferTokens.addFirstToken": "Ajouter votre premier jeton de transfert",
    "Settings.transferTokens.addNewToken": "Ajouter un nouveau jeton de transfert",
    "Settings.transferTokens.emptyStateLayout": "Vous n'avez aucun contenu pour le moment...",
    "Settings.tokens.ListView.headers.name": "Nom",
    "Settings.tokens.ListView.headers.description": "Description",
    "Settings.transferTokens.ListView.headers.type": "Type de jeton",
    "Settings.tokens.ListView.headers.createdAt": "Créé le",
    "Settings.tokens.ListView.headers.lastUsedAt": "Dernière utilisation",
    "Settings.application.ee.admin-seats.count": "<text>{enforcementUserCount}</text>/{permittedSeats}",
    "Settings.application.ee.admin-seats.at-limit-tooltip": "Limite atteinte : ajouter des places pour inviter d'autres utilisateurs",
    "Settings.application.ee.admin-seats.add-seats": "Gérer les places",
    "Settings.application.ee.admin-seats.support": "Contacter le service clients",
    "Settings.application.customization": "Customisation",
    "Settings.application.customization.auth-logo.carousel-hint": "Remplacer le logo dans la page de connexion",
    "Settings.application.customization.carousel-hint": "Changer le logo dans l'interface d'administration (dimensions maximales: {dimension}x{dimension}, poids maximal du fichier : {size}KB)",
    "Settings.application.customization.carousel-slide.label": "Logo slide",
    "Settings.application.customization.carousel.auth-logo.title": "Logo de connexion",
    "Settings.application.customization.carousel.change-action": "Changer le logo",
    "Settings.application.customization.carousel.menu-logo.title": "Logo du menu",
    "Settings.application.customization.carousel.reset-action": "Réinitialiser le logo",
    "Settings.application.customization.carousel.title": "Logo",
    "Settings.application.customization.menu-logo.carousel-hint": "Remplacer le logo dans la navigation principale",
    "Settings.application.customization.modal.cancel": "Annuler",
    "Settings.application.customization.modal.pending": "Téléchargement du logo",
    "Settings.application.customization.modal.pending.card-badge": "image",
    "Settings.application.customization.modal.pending.choose-another": "Choisir un autre logo",
    "Settings.application.customization.modal.pending.subtitle": "Gérer le logo choisi avant de le télécharger",
    "Settings.application.customization.modal.pending.title": "Logo prêt pour le téléchargement",
    "Settings.application.customization.modal.pending.upload": "Téléchargement du logo",
    "Settings.application.customization.modal.tab.label": "Comment voulez-vous télécharger vos medias ?",
    "Settings.application.customization.modal.upload": "Télécharger le logo",
    "Settings.application.customization.modal.upload.cta.browse": "Explorer les fichiers",
    "Settings.application.customization.modal.upload.drag-drop": "Glisser-déposer ici ou",
    "Settings.application.customization.modal.upload.error-format": "Mauvais format chargé (formats acceptés : jpeg, jpg, png, svg).",
    "Settings.application.customization.modal.upload.error-network": "Erreur réseau",
    "Settings.application.customization.modal.upload.error-size": "Le fichier téléchargé est trop grand (dimensions max : {dimension}x{dimension}, poids max: {size}KB)",
    "Settings.application.customization.modal.upload.file-validation": "Dimensions maximales : {dimension}x{dimension}, poids maximal : {size}KB",
    "Settings.application.customization.modal.upload.from-computer": "Depuis l'ordinateur",
    "Settings.application.customization.modal.upload.from-url": "Depuis une URL",
    "Settings.application.customization.modal.upload.from-url.input-label": "URL",
    "Settings.application.customization.modal.upload.next": "Suivant",
    "Settings.application.customization.size-details": "Dimensions maximales : {dimension}×{dimension}, poids maximal : {size}KB",
    "Settings.application.description": "Informations globales du panneau d'administration",
    "Settings.application.edition-title": "édition actuel",
    "Settings.application.ee-or-ce": "{communityEdition, select, true {Édition Communauté} other {Édition Entreprise}}",
    "Settings.application.get-help": "Obtenir de l'aide",
    "Settings.application.link-pricing": "Voir tous les tarifs",
    "Settings.application.link-upgrade": "Mettez à niveau votre panneau d'administration",
    "Settings.application.node-version": "version de node",
    "Settings.application.strapi-version": "version de strapi",
    "Settings.application.strapiVersion": "version de strapi",
    "Settings.application.title": "Aperçu",
    "Settings.error": "Erreur",
    "Settings.global": "Paramètre Globaux",
    "Settings.permissions": "Panneau d'aministration",
    "Settings.permissions.category": "Paramètres de permissions pour la catégorie {category}",
    "Settings.permissions.category.plugins": "Paramètres de permissions pour le plugin {plugin}",
    "Settings.permissions.conditions.anytime": "N'importe quand",
    "Settings.permissions.conditions.apply": "Appliquer",
    "Settings.permissions.conditions.can": "Peut",
    "Settings.permissions.conditions.conditions": "Définir les conditions",
    "Settings.permissions.conditions.links": "Liens",
    "Settings.permissions.conditions.no-actions": "Vous devez d'abord sélectionner des actions (créer, lire, mettre à jour, ...) avant de définir des conditions sur celles-ci.",
    "Settings.permissions.conditions.none-selected": "N'importe quand",
    "Settings.permissions.conditions.or": "OU",
    "Settings.permissions.conditions.when": "Quand",
    "Settings.permissions.select-all-by-permission": "Sélectionner toutes les permissions de {label}",
    "Settings.permissions.select-by-permission": "Sélectionner la permission de {label}",
    "Settings.permissions.users.create": "Créer un nouvel Utilisateur",
    "Settings.permissions.users.email": "Email",
    "Settings.permissions.users.firstname": "Prénom",
    "Settings.permissions.users.lastname": "Nom",
    "Settings.permissions.users.form.sso": "Se connecter via SSO",
    "Settings.permissions.users.form.sso.description": "Quand activé, les  utilisateurs peuvent se connecter via SSO",
    "Settings.permissions.users.listview.header.subtitle": "Tous les utilisateurs ayant accès au panneau d'administration de Strapi",
    "Settings.permissions.users.tabs.label": "Onglet Autorisations",
    "Settings.profile.form.notify.data.loaded": "Les données de votre profil ont été chargées",
    "Settings.profile.form.section.experience.clear.select": "Vider la langue de l'interface sélectionnée",
    "Settings.profile.form.section.experience.here": "documentation",
    "Settings.profile.form.section.experience.interfaceLanguage": "Langue de l'interface",
    "Settings.profile.form.section.experience.interfaceLanguage.hint": "Cela affichera seulement votre propre interface dans la langue sélectionnée",
    "Settings.profile.form.section.experience.interfaceLanguageHelp": "La sélection changera la langue de l'interface uniquement pour vous. Veuillez vous référer à cette {here} pour rendre d'autres langues disponibles pour votre équipe.",
    "Settings.profile.form.section.experience.title": "Expérience",
    "Settings.profile.form.section.head.title": "Profil utilisateur",
    "Settings.profile.form.section.profile.page.title": "Page de profil",
    "Settings.roles.create.description": "Définir les droits attribués au rôle",
    "Settings.roles.create.title": "Créer un rôle",
    "Settings.roles.created": "Rôle créé",
    "Settings.roles.edit.title": "Editer un rôle",
    "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# utilisateurs} one {# utilisateur} other {# utilisateurs}} possédant ce rôle",
    "Settings.roles.form.created": "Créé",
    "Settings.roles.form.description": "Nom et description du rôle",
    "Settings.roles.form.permission.property-label": "permissions de {label}",
    "Settings.roles.form.permissions.attributesPermissions": "Permissions de champs",
    "Settings.roles.form.permissions.create": "Créer",
    "Settings.roles.form.permissions.delete": "Supprimer",
    "Settings.roles.form.permissions.publish": "Publier",
    "Settings.roles.form.permissions.read": "Lire",
    "Settings.roles.form.permissions.update": "Mettre à jour",
    "Settings.roles.list.button.add": "Ajouter un rôle",
    "Settings.roles.list.description": "Liste des rôles",
    "Settings.roles.title.singular": "rôle",
    "Settings.sso.description": "Configurer les paramètres de la fonctionnalité Single Sign-On.",
    "Settings.sso.form.defaultRole.description": "Cela attribuera le nouvel utilisateur authentifié au rôle sélectionné",
    "Settings.sso.form.defaultRole.description-not-allowed": "Vous devez avec la permission de lire les rôles administateurs",
    "Settings.sso.form.defaultRole.label": "Rôle par défaut",
    "Settings.sso.form.registration.description": "Créer un nouvel utilisateur lors de la connexion via SSO si aucun compte n'existe",
    "Settings.sso.form.registration.label": "Enregistrement automatique",
    "Settings.sso.title": "Single Sign-On",
    "Settings.webhooks.create": "Créer un webhook",
    "Settings.webhooks.create.header": "Créer un nouvel en-tête",
    "Settings.webhooks.created": "Webhook créé",
    "Settings.webhooks.event.publish-tooltip": "Cet événement n'existe que pour les contenus avec le système Brouillon/Publier activé",
    "Settings.webhooks.events.create": "Créer",
    "Settings.webhooks.events.update": "Mettre à jour",
    "Settings.webhooks.form.events": "Evénements",
    "Settings.webhooks.form.headers": "En-têtes",
    "Settings.webhooks.form.url": "Url",
    "Settings.webhooks.headers.remove": "Supprimer l'en-tête ligne {number}",
    "Settings.webhooks.key": "Clé",
    "Settings.webhooks.list.button.add": "Créer un nouveau webhook",
    "Settings.webhooks.list.description": "Recevoir des notifications de modifications en POST",
    "Settings.webhooks.list.empty.description": "Aucun webhook trouvé",
    "Settings.webhooks.list.empty.link": "Voir notre documentation",
    "Settings.webhooks.list.empty.title": "Il n'y a pas encore de webhooks",
    "Settings.webhooks.list.th.actions": "actions",
    "Settings.webhooks.list.th.status": "statut",
    "Settings.webhooks.singular": "webhook",
    "Settings.webhooks.title": "Webhooks",
    "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# élément} other {# éléments}} sélectionné",
    "Settings.webhooks.trigger": "Déclencheur",
    "Settings.webhooks.trigger.cancel": "Annuler le déclencheur",
    "Settings.webhooks.trigger.pending": "En attente...",
    "Settings.webhooks.trigger.save": "Veuillez sauvegarder pour déclencher",
    "Settings.webhooks.trigger.success": "Succès !",
    "Settings.webhooks.trigger.success.label": "Déclenchement réussi",
    "Settings.webhooks.trigger.test": "Déclencheur de test",
    "Settings.webhooks.trigger.title": "Sauvegarder avant de déclencher",
    "Settings.webhooks.value": "Valeur",
    Username: Username,
    Users: Users,
    "Users & Permissions": "Utilisateurs et autorisations",
    "Users.components.List.empty": "Aucun utilisateur...",
    "Users.components.List.empty.withFilters": "Aucun utilisateur avec les filtres appliqués...",
    "Users.components.List.empty.withSearch": "Aucun utilisateur correspondant à la recherche ({search})...",
    "admin.pages.MarketPlacePage.head": "Marketplace - Plugins",
    "admin.pages.MarketPlacePage.submit.plugin.link": "Soumettez votre plugin",
    "admin.pages.MarketPlacePage.subtitle": "Tirez le meilleur de Strapi",
    anErrorOccurred: anErrorOccurred,
    "app.component.CopyToClipboard.label": "Copier dans le presse-papier",
    noPreview: noPreview,
    "app.component.search.label": "Rechercher {target}",
    "app.component.table.duplicate": "Dupliquer {target}",
    "app.component.table.edit": "Modifier {target}",
    "app.component.table.select.one-entry": "Sélectionner {target}",
    "app.components.BlockLink.blog": "Blog",
    "app.components.BlockLink.blog.content": "Lire les dernières actualités à propos de Strapi et de son écosystème",
    "app.components.BlockLink.code": "Apps d'exemple",
    "app.components.BlockLink.code.content": "Apprenez en testant des projets réels développés par la communauté.",
    "app.components.BlockLink.documentation.content": "Découvrir les concepts essentials, guides et instructions.",
    "app.components.BlockLink.tutorial": "Tutoriels",
    "app.components.BlockLink.tutorial.content": "Suivre les instructions étapes par étapes pour utiliser et personnaliser Strapi.",
    "app.components.Button.cancel": "Annuler",
    "app.components.Button.confirm": "Confirmer",
    "app.components.Button.reset": "Annuler",
    "app.components.ComingSoonPage.comingSoon": "Bientôt disponible",
    "app.components.ConfirmDialog.title": "Confirmation",
    "app.components.DownloadInfo.download": "Téléchargement en cours...",
    "app.components.DownloadInfo.text": "Cela peut prendre une minute. Merci de patienter.",
    "app.components.EmptyAttributes.title": "Il n'y a pas encore de champ",
    "app.components.EmptyStateLayout.content-document": "Vous n'avez pas encore de contenu...",
    "app.components.EmptyStateLayout.content-permissions": "Vous n'avez pas les permissions pour accéder à ce contenu",
    "app.components.HomePage.button.blog": "Voir plus sur le blog",
    "app.components.HomePage.community": "Rejoignez la communauté",
    "app.components.HomePage.community.content": "Discutez avec les membres de l'équipe, contributeurs et développeurs sur différent supports.",
    "app.components.HomePage.create": "Créez votre première Collection",
    "app.components.HomePage.roadmap": "Voir notre roadmap",
    "app.components.HomePage.welcome": "Bienvenue à bord !",
    "app.components.HomePage.welcome.again": "Bienvenue ",
    "app.components.HomePage.welcomeBlock.content": "Félicitations ! Vous êtes connecté en tant que tout premier administrateur. Pour découvrir les puissantes fonctionnalités fournies par Strapi, nous vous recommandons de créer votre premier Type de Contenu !",
    "app.components.HomePage.welcomeBlock.content.again": "Nous espérons que votre projet avance bien... Découvrez les derniers articles à propos de Strapi. Nous faisons de notre mieux pour améliorer le produit selon vos retours.",
    "app.components.HomePage.welcomeBlock.content.issues": "issues",
    "app.components.HomePage.welcomeBlock.content.raise": " ou soumettez des ",
    "app.components.ImgPreview.hint": "Glissez-déposez dans cette zone ou {browse} un fichier à télécharger",
    "app.components.ImgPreview.hint.browse": "recherchez",
    "app.components.InputFile.newFile": "Ajouter un nouveau fichier",
    "app.components.InputFileDetails.open": "Ouvrir dans une nouvelle fenêtre",
    "app.components.InputFileDetails.originalName": "Nom d'origine :",
    "app.components.InputFileDetails.remove": "Supprimer ce fichier",
    "app.components.InputFileDetails.size": "Taille:",
    "app.components.InstallPluginPage.Download.description": "L'installation d'un plugin peut prendre quelques secondes.",
    "app.components.InstallPluginPage.Download.title": "Téléchargement en cours...",
    "app.components.InstallPluginPage.description": "Améliorez votre app sans efforts",
    "app.components.LeftMenu.collapse": "Réduire la barre de navigation",
    "app.components.LeftMenu.expand": "Développer la barre de navigation",
    "app.components.LeftMenu.logout": "Déconnexion",
    "app.components.LeftMenu.trialCountdown": "Votre essai se termine le {date}.",
    "app.components.LeftMenuFooter.help": "Aide",
    "app.components.LeftMenuFooter.poweredBy": "Propulsé par ",
    "app.components.LeftMenuLinkContainer.collectionTypes": "Types de collection",
    "app.components.LeftMenuLinkContainer.configuration": "Configurations",
    "app.components.LeftMenuLinkContainer.general": "Général",
    "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Aucun plugin installé",
    "app.components.LeftMenuLinkContainer.plugins": "Plugins",
    "app.components.LeftMenuLinkContainer.singleTypes": "Types uniques",
    "app.components.ListPluginsPage.deletePlugin.description": "La désinstallation du plugin peut prendre quelques secondes.",
    "app.components.ListPluginsPage.deletePlugin.title": "Désinstallation",
    "app.components.ListPluginsPage.description": "Liste des plugins installés dans le projet.",
    "app.components.ListPluginsPage.head.title": "List plugins",
    "app.components.Logout.logout": "Se déconnecter",
    "app.components.Logout.profile": "Profil",
    "app.components.MarketplaceBanner": "Découvrez les plugins construits par la communauté, et bien d'autres choses géniales pour démarrer votre projet, sur Strapi Awesome.",
    "app.components.MarketplaceBanner.image.alt": "un logo fusée de strapi",
    "app.components.MarketplaceBanner.link": "Aller voir ça maintenant",
    "app.components.NotFoundPage.back": "Retourner à la page d'accueil",
    "app.components.NotFoundPage.description": "Page introuvable",
    "app.components.Official": "Officiel",
    "app.components.Onboarding.help.button": "Bouton d'aide",
    "app.components.Onboarding.label.completed": "% complétées",
    "app.components.Onboarding.title": "Démarrons ensemble",
    "app.components.PluginCard.Button.label.download": "Télécharger",
    "app.components.PluginCard.Button.label.install": "Déjà installé",
    "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "La configuration d'autoReload a besoin d'être activée pour télécharger un plugin. Veuillez démarrer votre application avec `yarn develop`.",
    "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "J'ai compris !",
    "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Pour des raisoins de sécurité, un plugin ne peut être installé qu'en dévelopment.",
    "app.components.PluginCard.PopUpWarning.install.impossible.title": "Le téléchargement est impossible",
    "app.components.PluginCard.compatible": "Compatible avec votre app",
    "app.components.PluginCard.compatibleCommunity": "Compatible avec la communauté",
    "app.components.PluginCard.more-details": "Plus de détails",
    "app.components.ToggleCheckbox.off-label": "Désactivé",
    "app.components.ToggleCheckbox.on-label": "Activé",
    "app.components.Users.MagicLink.connect": "Envoyez ce lien à l'utilisateur pour qu'il se connecte.",
    "app.components.Users.MagicLink.connect.sso": "Envoyez ce lien à l'utilisateur, la première connexion peut être effectué via un fournisseur SSO",
    "app.components.Users.ModalCreateBody.block-title.details": "Détails",
    "app.components.Users.ModalCreateBody.block-title.roles": "Rôles de l'utilisateur",
    "app.components.Users.ModalCreateBody.block-title.roles.description": "Un utilisateur peut avoir un ou plusieurs rôles",
    "app.components.Users.SortPicker.button-label": "Trier par",
    "app.components.Users.SortPicker.sortby.email_asc": "Email (A à Z)",
    "app.components.Users.SortPicker.sortby.email_desc": "Email (Z à A)",
    "app.components.Users.SortPicker.sortby.firstname_asc": "Prénom (A à Z)",
    "app.components.Users.SortPicker.sortby.firstname_desc": "Prénom (Z à A)",
    "app.components.Users.SortPicker.sortby.lastname_asc": "Nom (A à Z)",
    "app.components.Users.SortPicker.sortby.lastname_desc": "Nom (Z à A)",
    "app.components.Users.SortPicker.sortby.username_asc": "Nom d'utilisateur (A à Z)",
    "app.components.Users.SortPicker.sortby.username_desc": "Nom d'utilisateur (Z à A)",
    "app.components.listPlugins.button": "Ajouter un Nouveau Plugin",
    "app.components.listPlugins.title.none": "Aucun plugin n'est installé",
    "app.components.listPluginsPage.deletePlugin.error": "Une erreur est survenue pendant la désintallation",
    "app.containers.App.notification.error.init": "Une erreur est survenue en requêtant l'API",
    "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Si vous ne recevez pas ce lien, veuillez contacter votre administrateur.",
    "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "La réception de votre lien de récupération de mot de passe peut prendre quelques minutes.",
    "app.containers.AuthPage.ForgotPasswordSuccess.title": "Email envoyé",
    "app.containers.Users.EditPage.form.active.label": "Actif",
    "app.containers.Users.EditPage.header.label": "Modifier {name}",
    "app.containers.Users.EditPage.header.label-loading": "Modifier l'utilisateur",
    "app.containers.Users.EditPage.roles-bloc-title": "Rôles attribués",
    "app.containers.Users.ModalForm.footer.button-success": "Créer l'utilisateur",
    "app.links.configure-view": "Configurez la vue",
    "app.static.links.cheatsheet": "Aide-mémoire",
    "app.utils.SelectOption.defaultMessage": " ",
    "app.utils.add-filter": "Ajouter un filtre",
    "app.utils.close-label": "Fermer",
    "app.utils.defaultMessage": " ",
    "app.utils.duplicate": "Dupliquer",
    "app.utils.edit": "Modifier",
    "app.utils.delete": "Supprimer",
    "app.utils.errors.file-too-big.message": "Le fichier est trop lourd",
    "app.utils.filter-value": "Valeur du filtre",
    "app.utils.filters": "Filtres",
    "app.utils.notify.data-loaded": "{target} est chargée",
    "app.utils.placeholder.defaultMessage": " ",
    "app.utils.publish": "Publier",
    "app.utils.select-all": "Tout sélectionner",
    "app.utils.select-field": "Sélectionner un champ",
    "app.utils.select-filter": "Sélectionner un filtre",
    "app.utils.unpublish": "Annuler la publication",
    "app.utils.ready-to-publish": "Prêt à publier",
    "app.utils.already-published": "Déjà publié",
    clearLabel: clearLabel,
    "coming.soon": "Ce contenu est actuellement en construction et sera de retour dans quelques semaines !",
    "component.Input.error.validation.integer": "La valeur doit être un nombre entier",
    "components.AutoReloadBlocker.description": "Démarrez Strapi avec l'une des commandes suivantes:",
    "components.AutoReloadBlocker.header": "L'autoReload doit être activé pour ce plugin.",
    "components.ErrorBoundary.title": "Une erreur est survenue...",
    "components.FilterOptions.FILTER_TYPES.$contains": "contient",
    "components.FilterOptions.FILTER_TYPES.$containsi": "contient (insensible à la casse)",
    "components.FilterOptions.FILTER_TYPES.$endsWith": "termine par",
    "components.FilterOptions.FILTER_TYPES.$endsWithi": "termine par (insensible à la casse)",
    "components.FilterOptions.FILTER_TYPES.$eq": "est",
    "components.FilterOptions.FILTER_TYPES.$eqi": "est (insensible à la casse)",
    "components.FilterOptions.FILTER_TYPES.$gt": "est plus grand que",
    "components.FilterOptions.FILTER_TYPES.$gte": "est plus grand ou égal à",
    "components.FilterOptions.FILTER_TYPES.$lt": "est plus petit que",
    "components.FilterOptions.FILTER_TYPES.$lte": "est plus petit ou égal à",
    "components.FilterOptions.FILTER_TYPES.$ne": "n'est pas",
    "components.FilterOptions.FILTER_TYPES.$nei": "n'est pas (insensible à la casse)",
    "components.FilterOptions.FILTER_TYPES.$notContains": "ne contient pas",
    "components.FilterOptions.FILTER_TYPES.$notContainsi": "ne contient pas (insensible à la casse)",
    "components.FilterOptions.FILTER_TYPES.$notNull": "n'est pas nul",
    "components.FilterOptions.FILTER_TYPES.$null": "est nul",
    "components.FilterOptions.FILTER_TYPES.$startsWith": "commence par",
    "components.FilterOptions.FILTER_TYPES.$startsWithi": "commence par (insensible à la casse)",
    "components.Input.error.attribute.key.taken": "Cette valeur existe déjà",
    "components.Input.error.attribute.sameKeyAndName": "Ne peuvent pas être égaux",
    "components.Input.error.attribute.taken": "Ce champ existe déjà",
    "components.Input.error.contain.lowercase": "Le mot de passe doit contenir au moins une lettre minuscule",
    "components.Input.error.contain.number": "Le mot de passe doit contenir au moins un chiffre",
    "components.Input.error.contain.uppercase": "Le mot de passe doit contenir au moins une lettre majuscule",
    "components.Input.error.contentTypeName.taken": "Ce nom existe déjà",
    "components.Input.error.custom-error": "{errorMessage} ",
    "components.Input.error.password.noMatch": "Le mot de passe ne correspond pas",
    "components.Input.error.validation.email": "Le format n'est pas de type e-mail",
    "components.Input.error.validation.json": "Le format JSON n'est pas respecté",
    "components.Input.error.validation.max": "La valeur est trop grande {max}.",
    "components.Input.error.validation.maxLength": "La valeur est trop longue {max}.",
    "components.Input.error.validation.min": "La valeur est trop basse {min}.",
    "components.Input.error.validation.minLength": "La valeur est trop courte {min}.",
    "components.Input.error.validation.minSupMax": "Ne peut pas être plus grand.",
    "components.Input.error.validation.regex": "La valeur ne correspond pas au format attendu.",
    "components.Input.error.validation.required": "Ce champ est obligatoire.",
    "components.Input.error.validation.unique": "Cette valeur est déjà prise",
    "components.InputSelect.option.placeholder": "Choisissez ici",
    "components.ListRow.empty": "Il n'y a pas de données à afficher.",
    "components.NotAllowedInput.text": "Vous n'êtes pas autorisé à voir ce champ",
    "components.OverlayBlocker.description": "Vous utilisez une fonctionnalité qui nécessite le redémarrage du server. Merci d'attendre que celui-ci ait redémarré.",
    "components.OverlayBlocker.description.serverError": "Le serveur aurait déjà du redémarrer, vous devriez regarder les messages dans le terminal.",
    "components.OverlayBlocker.title": "Le serveur est en train de redémarrer",
    "components.OverlayBlocker.title.serverError": "Le serveur aurait déjà du redémarrer",
    "components.PageFooter.select": "entrées par page",
    "components.ProductionBlocker.description": "Pour des raisons de sécurité il est désactivé dans les autres environnements.",
    "components.ProductionBlocker.header": "Ce plugin est disponible uniquement en développement.",
    "components.Search.placeholder": "Rechercher...",
    "components.TableHeader.sort": "Trier par {label}",
    "components.Wysiwyg.ToggleMode.markdown-mode": "Mode Markdown",
    "components.Wysiwyg.ToggleMode.preview-mode": "Mode Aperçu",
    "components.Wysiwyg.collapse": "Fermer",
    "components.Wysiwyg.selectOptions.H1": "Titre H1",
    "components.Wysiwyg.selectOptions.H2": "Titre H2",
    "components.Wysiwyg.selectOptions.H3": "Titre H3",
    "components.Wysiwyg.selectOptions.H4": "Titre H4",
    "components.Wysiwyg.selectOptions.H5": "Titre H5",
    "components.Wysiwyg.selectOptions.H6": "Titre H6",
    "components.Wysiwyg.selectOptions.title": "Ajouter un titre",
    "components.WysiwygBottomControls.charactersIndicators": "caractères",
    "components.WysiwygBottomControls.fullscreen": "Plein écran",
    "components.WysiwygBottomControls.uploadFiles": "Ajouter des fichiers en les 'glissant-déposant', {browse}, ou en les collant depuis le presse-papier",
    "components.WysiwygBottomControls.uploadFiles.browse": "en les selectionnant",
    "components.pagination.go-to": "Aller à la page {page}",
    "components.pagination.go-to-next": "Aller à la page suivante",
    "components.pagination.go-to-previous": "Aller à la page précédente",
    "components.pagination.remaining-links": "Et {number} autres liens",
    "components.popUpWarning.button.cancel": "Non, annuler",
    "components.popUpWarning.button.confirm": "Oui, confirmer",
    "components.popUpWarning.message": "Etes-vous sure de vouloir le supprimer ?",
    "components.popUpWarning.title": "Merci de confirmer",
    dark: dark,
    "form.button.continue": "Continuer",
    "global.search": "Rechercher",
    "global.actions": "Actions",
    "global.auditLogs": "Journaux d'audit",
    "global.back": "Retour",
    "global.cancel": "Annuler",
    "global.change-password": "Modifier le mot de passe",
    "global.content-manager": "Gestion du contenu",
    "global.continue": "Continuer",
    "global.delete": "Supprimer",
    "global.delete-target": "Supprimer {target}",
    "global.description": "Description",
    "global.details": "Détails",
    "global.disabled": "Désactivé",
    "global.documentation": "Documentation",
    "global.enabled": "Activé",
    "global.finish": "Terminer",
    "global.marketplace": "Marketplace",
    "global.name": "Nom",
    "global.none": "Aucun",
    "global.password": "Mot de passe",
    "global.plugins": "Plugins",
    "global.profile": "Profil",
    "global.reset-password": "Réinitialiser le mot de passe",
    "global.roles": "Rôles",
    "global.save": "Enregistrer",
    "global.see-more": "Voir plus",
    "global.select": "Sélectionner",
    "global.select-all-entries": "Sélectionner toutes les entrées",
    "global.settings": "Paramètres",
    "global.type": "Type",
    "global.users": "Utilisateurs",
    light: light,
    "form.button.done": "Terminer",
    "global.prompt.unsaved": "Êtes-vous sûr de vouloir quitter cette page? Toutes vos modifications seront perdues",
    "notification.contentType.relations.conflict": "Le Type de Contenu à des relations qui rentrent en conflit",
    "notification.default.title": "Information:",
    "notification.error": "Une erreur est survenue",
    "notification.error.layout": "Impossible de récupérer le layout de l'admin",
    "notification.form.error.fields": "Le formulaire contient des erreurs",
    "notification.form.success.fields": "Modifications enregistrées",
    "notification.link-copied": "Lien copié dans le presse-papier",
    "notification.permission.not-allowed-read": "Vous n'êtes pas autorisé à voir ce document",
    "notification.success.delete": "Cet élément a été supprimé",
    "notification.success.saved": "Sauvegardé",
    "notification.success.title": "Succès :",
    "notification.version.update.message": "Une nouvelle version de Strapi est disponible !",
    "notification.warning.title": "Attention :",
    "notification.warning.404": "404 - Introuvable",
    or: or,
    "request.error.model.unknown": "Le model n'existe pas",
    selectButtonTitle: selectButtonTitle,
    skipToContent: skipToContent,
    submit: submit,
    "components.Blocks.modifiers.bold": "Gras",
    "components.Blocks.modifiers.italic": "Italique",
    "components.Blocks.modifiers.underline": "Souligné",
    "components.Blocks.modifiers.strikethrough": "Barré",
    "components.Blocks.modifiers.code": "Code",
    "components.Blocks.link": "Lien",
    "components.Blocks.expand": "Agrandir",
    "components.Blocks.collapse": "Rétrécir",
    "components.Blocks.popover.text": "Texte",
    "components.Blocks.popover.text.placeholder": "Entrez le texte du lien",
    "components.Blocks.popover.link": "Lien",
    "components.Blocks.popover.link.placeholder": "Coller un lien",
    "components.Blocks.popover.link.error": "Merci d'entrer un lien valide",
    "components.Blocks.popover.remove": "Supprimer",
    "components.Blocks.popover.edit": "Modifier",
    "components.Blocks.blocks.text": "Texte",
    "components.Blocks.blocks.heading1": "Titre 1",
    "components.Blocks.blocks.heading2": "Titre 2",
    "components.Blocks.blocks.heading3": "Titre 3",
    "components.Blocks.blocks.heading4": "Titre 4",
    "components.Blocks.blocks.heading5": "Titre 5",
    "components.Blocks.blocks.heading6": "Titre 6",
    "components.Blocks.blocks.code": "Bloc de code",
    "components.Blocks.blocks.quote": "Citation",
    "components.Blocks.blocks.image": "Image",
    "components.Blocks.blocks.unorderedList": "Liste à puces",
    "components.Blocks.blocks.orderedList": "Liste numérotée"
};

export { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, fr as default, light, noPreview, or, selectButtonTitle, skipToContent, submit };
//# sourceMappingURL=fr.json.mjs.map
