{"version": 3, "file": "transferTokens.mjs", "sources": ["../../../../../admin/src/services/transferTokens.ts"], "sourcesContent": ["import * as TransferTokens from '../../../shared/contracts/transfer';\n\nimport { adminApi } from './api';\n\nconst transferTokenService = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['TransferToken'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      regenerateToken: builder.mutation<TransferTokens.TokenRegenerate.Response['data'], string>({\n        query: (url) => ({\n          method: 'POST',\n          url: `${url}/regenerate`,\n        }),\n        transformResponse: (response: TransferTokens.TokenRegenerate.Response) => response.data,\n      }),\n      getTransferTokens: builder.query<TransferTokens.TokenList.Response['data'], void>({\n        query: () => ({\n          url: '/admin/transfer/tokens',\n          method: 'GET',\n        }),\n        transformResponse: (response: TransferTokens.TokenList.Response) => response.data,\n        providesTags: (res, _err) => [\n          ...(res?.map(({ id }) => ({ type: 'TransferToken' as const, id })) ?? []),\n          { type: 'TransferToken' as const, id: 'LIST' },\n        ],\n      }),\n      getTransferToken: builder.query<\n        TransferTokens.TokenGetById.Response['data'],\n        TransferTokens.TokenGetById.Params['id']\n      >({\n        query: (id) => `/admin/transfer/tokens/${id}`,\n        transformResponse: (response: TransferTokens.TokenGetById.Response) => response.data,\n        providesTags: (res, _err, id) => [{ type: 'TransferToken' as const, id }],\n      }),\n      createTransferToken: builder.mutation<\n        TransferTokens.TokenCreate.Response['data'],\n        TransferTokens.TokenCreate.Request['body']\n      >({\n        query: (body) => ({\n          url: '/admin/transfer/tokens',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: TransferTokens.TokenCreate.Response) => response.data,\n        invalidatesTags: [{ type: 'TransferToken' as const, id: 'LIST' }],\n      }),\n      deleteTransferToken: builder.mutation<\n        TransferTokens.TokenRevoke.Response['data'],\n        TransferTokens.TokenRevoke.Params['id']\n      >({\n        query: (id) => ({\n          url: `/admin/transfer/tokens/${id}`,\n          method: 'DELETE',\n        }),\n        transformResponse: (response: TransferTokens.TokenRevoke.Response) => response.data,\n        invalidatesTags: (_res, _err, id) => [{ type: 'TransferToken' as const, id }],\n      }),\n      updateTransferToken: builder.mutation<\n        TransferTokens.TokenUpdate.Response['data'],\n        TransferTokens.TokenUpdate.Params & TransferTokens.TokenUpdate.Request['body']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/transfer/tokens/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (response: TransferTokens.TokenUpdate.Response) => response.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'TransferToken' as const, id }],\n      }),\n    }),\n    overrideExisting: false,\n  });\n\nconst {\n  useGetTransferTokensQuery,\n  useGetTransferTokenQuery,\n  useCreateTransferTokenMutation,\n  useDeleteTransferTokenMutation,\n  useUpdateTransferTokenMutation,\n  useRegenerateTokenMutation,\n} = transferTokenService;\n\nexport {\n  useGetTransferTokensQuery,\n  useGetTransferTokenQuery,\n  useCreateTransferTokenMutation,\n  useDeleteTransferTokenMutation,\n  useUpdateTransferTokenMutation,\n  useRegenerateTokenMutation,\n};\n"], "names": ["transferTokenService", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "regenerateToken", "mutation", "query", "url", "method", "transformResponse", "response", "data", "getTransferTokens", "providesTags", "res", "_err", "map", "id", "type", "getTransferToken", "createTransferToken", "body", "invalidatesTags", "deleteTransferToken", "_res", "updateTransferToken", "overrideExisting", "useGetTransferTokensQuery", "useGetTransferTokenQuery", "useCreateTransferTokenMutation", "useDeleteTransferTokenMutation", "useUpdateTransferTokenMutation", "useRegenerateTokenMutation"], "mappings": ";;AAIA,MAAMA,oBAAAA,GAAuBC,QAC1BC,CAAAA,gBAAgB,CAAC;IAChBC,WAAa,EAAA;AAAC,QAAA;AAAgB;AAChC,CAAA,CAAA,CACCC,eAAe,CAAC;IACfC,SAAW,EAAA,CAACC,WAAa;YACvBC,eAAiBD,EAAAA,OAAAA,CAAQE,QAAQ,CAA0D;gBACzFC,KAAO,EAAA,CAACC,OAAS;wBACfC,MAAQ,EAAA,MAAA;AACRD,wBAAAA,GAAAA,EAAK,CAAC,EAAEA,GAAI,CAAA,WAAW;qBACzB,CAAA;gBACAE,iBAAmB,EAAA,CAACC,QAAsDA,GAAAA,QAAAA,CAASC;AACrF,aAAA,CAAA;YACAC,iBAAmBT,EAAAA,OAAAA,CAAQG,KAAK,CAAkD;AAChFA,gBAAAA,KAAAA,EAAO,KAAO;wBACZC,GAAK,EAAA,wBAAA;wBACLC,MAAQ,EAAA;qBACV,CAAA;gBACAC,iBAAmB,EAAA,CAACC,QAAgDA,GAAAA,QAAAA,CAASC,IAAI;gBACjFE,YAAc,EAAA,CAACC,KAAKC,IAAS,GAAA;AACvBD,wBAAAA,GAAAA,GAAAA,EAAKE,IAAI,CAAC,EAAEC,EAAE,EAAE,IAAM;gCAAEC,IAAM,EAAA,eAAA;AAA0BD,gCAAAA;AAAG,6BAAA,MAAO,EAAE;AACxE,wBAAA;4BAAEC,IAAM,EAAA,eAAA;4BAA0BD,EAAI,EAAA;AAAO;AAC9C;AACH,aAAA,CAAA;YACAE,gBAAkBhB,EAAAA,OAAAA,CAAQG,KAAK,CAG7B;AACAA,gBAAAA,KAAAA,EAAO,CAACW,EAAO,GAAA,CAAC,uBAAuB,EAAEA,GAAG,CAAC;gBAC7CR,iBAAmB,EAAA,CAACC,QAAmDA,GAAAA,QAAAA,CAASC,IAAI;gBACpFE,YAAc,EAAA,CAACC,GAAKC,EAAAA,IAAAA,EAAME,EAAO,GAAA;AAAC,wBAAA;4BAAEC,IAAM,EAAA,eAAA;AAA0BD,4BAAAA;AAAG;AAAE;AAC3E,aAAA,CAAA;YACAG,mBAAqBjB,EAAAA,OAAAA,CAAQE,QAAQ,CAGnC;gBACAC,KAAO,EAAA,CAACe,QAAU;wBAChBd,GAAK,EAAA,wBAAA;wBACLC,MAAQ,EAAA,MAAA;wBACRG,IAAMU,EAAAA;qBACR,CAAA;gBACAZ,iBAAmB,EAAA,CAACC,QAAkDA,GAAAA,QAAAA,CAASC,IAAI;gBACnFW,eAAiB,EAAA;AAAC,oBAAA;wBAAEJ,IAAM,EAAA,eAAA;wBAA0BD,EAAI,EAAA;AAAO;AAAE;AACnE,aAAA,CAAA;YACAM,mBAAqBpB,EAAAA,OAAAA,CAAQE,QAAQ,CAGnC;gBACAC,KAAO,EAAA,CAACW,MAAQ;AACdV,wBAAAA,GAAAA,EAAK,CAAC,uBAAuB,EAAEU,EAAAA,CAAG,CAAC;wBACnCT,MAAQ,EAAA;qBACV,CAAA;gBACAC,iBAAmB,EAAA,CAACC,QAAkDA,GAAAA,QAAAA,CAASC,IAAI;gBACnFW,eAAiB,EAAA,CAACE,IAAMT,EAAAA,IAAAA,EAAME,EAAO,GAAA;AAAC,wBAAA;4BAAEC,IAAM,EAAA,eAAA;AAA0BD,4BAAAA;AAAG;AAAE;AAC/E,aAAA,CAAA;YACAQ,mBAAqBtB,EAAAA,OAAAA,CAAQE,QAAQ,CAGnC;AACAC,gBAAAA,KAAAA,EAAO,CAAC,EAAEW,EAAE,EAAE,GAAGI,IAAAA,EAAM,IAAM;AAC3Bd,wBAAAA,GAAAA,EAAK,CAAC,uBAAuB,EAAEU,EAAAA,CAAG,CAAC;wBACnCT,MAAQ,EAAA,KAAA;wBACRG,IAAMU,EAAAA;qBACR,CAAA;gBACAZ,iBAAmB,EAAA,CAACC,QAAkDA,GAAAA,QAAAA,CAASC,IAAI;AACnFW,gBAAAA,eAAAA,EAAiB,CAACE,IAAMT,EAAAA,IAAAA,EAAM,EAAEE,EAAE,EAAE,GAAK;AAAC,wBAAA;4BAAEC,IAAM,EAAA,eAAA;AAA0BD,4BAAAA;AAAG;AAAE;AACnF,aAAA;SACF,CAAA;IACAS,gBAAkB,EAAA;AACpB,CAAA,CAAA;AAEF,MAAM,EACJC,yBAAyB,EACzBC,wBAAwB,EACxBC,8BAA8B,EAC9BC,8BAA8B,EAC9BC,8BAA8B,EAC9BC,0BAA0B,EAC3B,GAAGnC;;;;"}