!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("react"),require("scheduler"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","scheduler","react-dom"],r):r((e||self).useContextSelector={},e.React,e.scheduler,e.ReactDOM)}(this,function(e,r,t,n){var u=Symbol(),o=Symbol(),c="undefined"==typeof window||/ServerSideRendering/.test(window.navigator&&window.navigator.userAgent)?r.useEffect:r.useLayoutEffect,i=t.unstable_runWithPriority?function(e){return t.unstable_runWithPriority(t.unstable_NormalPriority,e)}:function(e){return e()},s=function(e){return e};function f(e,t){var n=r.useContext(e)[u];if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!n)throw new Error("useContextSelector requires special context");var o=n.v.current,i=n.n.current,s=n.l,f=t(o),a=r.useReducer(function(e,r){if(!r)return[o,f];if("p"in r)throw r.p;if(r.n===i)return Object.is(e[1],f)?e:[o,f];try{if("v"in r){if(Object.is(e[0],r.v))return e;var n=t(r.v);return Object.is(e[1],n)?e:[r.v,n]}}catch(e){}return[].concat(e)},[o,f]),d=a[0],l=a[1];return Object.is(d[1],f)||l(),c(function(){return s.add(l),function(){s.delete(l)}},[s]),d[1]}e.BridgeProvider=function(e){var t=e.value,n=e.children,u=e.context[o];if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!u)throw new Error("BridgeProvider requires special context");return r.createElement(u,{value:t},n)},e.createContext=function(e){var t,s,f=r.createContext(((t={})[u]={v:{current:e},n:{current:-1},l:new Set,u:function(e){return e()}},t));return f[o]=f.Provider,f.Provider=(s=f.Provider,function(e){var t=e.value,o=e.children,f=r.useRef(t),a=r.useRef(0),d=r.useState(null),l=d[0],v=d[1];l&&(l(t),v(null));var p=r.useRef();if(!p.current){var h,x=new Set;p.current=((h={})[u]={v:f,n:a,l:x,u:function(e,r){n.unstable_batchedUpdates(function(){a.current+=1;var t={n:a.current};null!=r&&r.suspense&&(t.n*=-1,t.p=new Promise(function(e){v(function(){return function(r){t.v=r,delete t.p,e(r)}})})),x.forEach(function(e){return e(t)}),e()})}},h)}return c(function(){f.current=t,a.current+=1,i(function(){p.current[u].l.forEach(function(e){e({n:a.current,v:t})})})},[t]),r.createElement(s,{value:p.current},o)}),delete f.Consumer,f},e.useBridgeValue=function(e){var t=r.useContext(e);if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!t[u])throw new Error("useBridgeValue requires special context");return t},e.useContext=function(e){return f(e,s)},e.useContextSelector=f,e.useContextUpdate=function(e){var t=r.useContext(e)[u];if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!t)throw new Error("useContextUpdate requires special context");return t.u}});
//# sourceMappingURL=index.umd.js.map
