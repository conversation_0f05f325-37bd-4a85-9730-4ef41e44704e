{"version": 3, "file": "webhooks.mjs", "sources": ["../../../../../server/src/routes/webhooks.ts"], "sourcesContent": ["export default [\n  {\n    method: 'GET',\n    path: '/webhooks',\n    handler: 'webhooks.listWebhooks',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.read'] } },\n      ],\n    },\n  },\n  {\n    method: 'POST',\n    path: '/webhooks',\n    handler: 'webhooks.createWebhook',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.create'] } },\n      ],\n    },\n  },\n  {\n    method: 'GET',\n    path: '/webhooks/:id',\n    handler: 'webhooks.getWebhook',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.read'] } },\n      ],\n    },\n  },\n  {\n    method: 'PUT',\n    path: '/webhooks/:id',\n    handler: 'webhooks.updateWebhook',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.update'] } },\n      ],\n    },\n  },\n  {\n    method: 'DELETE',\n    path: '/webhooks/:id',\n    handler: 'webhooks.deleteWebhook',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.delete'] } },\n      ],\n    },\n  },\n  {\n    method: 'POST',\n    path: '/webhooks/batch-delete',\n    handler: 'webhooks.deleteWebhooks',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.delete'] } },\n      ],\n    },\n  },\n  {\n    method: 'POST',\n    path: '/webhooks/:id/trigger',\n    handler: 'webhooks.triggerWebhook',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::webhooks.update'] } },\n      ],\n    },\n  },\n];\n"], "names": ["method", "path", "handler", "config", "policies", "name", "actions"], "mappings": "AAAA,eAAe;AACb,IAAA;QACEA,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,WAAA;QACNC,OAAS,EAAA,uBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAuB;AAAC;AAAE;AAChF;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,WAAA;QACNC,OAAS,EAAA,wBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAyB;AAAC;AAAE;AAClF;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,eAAA;QACNC,OAAS,EAAA,qBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAuB;AAAC;AAAE;AAChF;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,eAAA;QACNC,OAAS,EAAA,wBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAyB;AAAC;AAAE;AAClF;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,QAAA;QACRC,IAAM,EAAA,eAAA;QACNC,OAAS,EAAA,wBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAyB;AAAC;AAAE;AAClF;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,wBAAA;QACNC,OAAS,EAAA,yBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAyB;AAAC;AAAE;AAClF;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,uBAAA;QACNC,OAAS,EAAA,yBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAyB;AAAC;AAAE;AAClF;AACH;AACF;CACD;;;;"}