{"version": 3, "file": "usePersistentState.mjs", "sources": ["../../../../../admin/src/hooks/usePersistentState.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\n\nconst usePersistentState = <T>(key: string, defaultValue: T) => {\n  const [value, setValue] = useState<T>(() => {\n    const stickyValue = window.localStorage.getItem(key);\n\n    if (stickyValue !== null) {\n      try {\n        return JSON.parse(stickyValue);\n      } catch {\n        // JSON.parse fails when the stored value is a primitive\n        return stickyValue;\n      }\n    }\n\n    return defaultValue;\n  });\n\n  useEffect(() => {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  }, [key, value]);\n\n  return [value, setValue] as const;\n};\n\nexport { usePersistentState };\n"], "names": ["usePersistentState", "key", "defaultValue", "value", "setValue", "useState", "stickyValue", "window", "localStorage", "getItem", "JSON", "parse", "useEffect", "setItem", "stringify"], "mappings": ";;AAEMA,MAAAA,kBAAAA,GAAqB,CAAIC,GAAaC,EAAAA,YAAAA,GAAAA;AAC1C,IAAA,MAAM,CAACC,KAAAA,EAAOC,QAAS,CAAA,GAAGC,QAAY,CAAA,IAAA;AACpC,QAAA,MAAMC,WAAcC,GAAAA,MAAAA,CAAOC,YAAY,CAACC,OAAO,CAACR,GAAAA,CAAAA;AAEhD,QAAA,IAAIK,gBAAgB,IAAM,EAAA;YACxB,IAAI;gBACF,OAAOI,IAAAA,CAAKC,KAAK,CAACL,WAAAA,CAAAA;AACpB,aAAA,CAAE,OAAM;;gBAEN,OAAOA,WAAAA;AACT;AACF;QAEA,OAAOJ,YAAAA;AACT,KAAA,CAAA;IAEAU,SAAU,CAAA,IAAA;AACRL,QAAAA,MAAAA,CAAOC,YAAY,CAACK,OAAO,CAACZ,GAAKS,EAAAA,IAAAA,CAAKI,SAAS,CAACX,KAAAA,CAAAA,CAAAA;KAC/C,EAAA;AAACF,QAAAA,GAAAA;AAAKE,QAAAA;AAAM,KAAA,CAAA;IAEf,OAAO;AAACA,QAAAA,KAAAA;AAAOC,QAAAA;AAAS,KAAA;AAC1B;;;;"}