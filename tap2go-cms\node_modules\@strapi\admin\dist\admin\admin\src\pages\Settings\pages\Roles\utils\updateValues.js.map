{"version": 3, "file": "updateValues.js", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Roles/utils/updateValues.ts"], "sourcesContent": ["import { isObject } from '../../../../../utils/objects';\n\n/**\n * Sets all the none object values of an object to the given one\n * It preserves the shape of the object, it only modifies the leafs\n * of an object.\n * This utility is very helpful when dealing with parent<>children checkboxes\n */\nconst updateValues = (obj: object, valueToSet: boolean, isFieldUpdate = false): object => {\n  return Object.keys(obj).reduce((acc, current) => {\n    const currentValue = obj[current as keyof object];\n\n    if (current === 'conditions' && !isFieldUpdate) {\n      // @ts-expect-error – TODO: type better\n      acc[current] = currentValue;\n\n      return acc;\n    }\n\n    if (isObject(currentValue)) {\n      return { ...acc, [current]: updateValues(currentValue, valueToSet, current === 'fields') };\n    }\n\n    // @ts-expect-error – TODO: type better\n    acc[current] = valueToSet;\n\n    return acc;\n  }, {});\n};\n\nexport { updateValues };\n"], "names": ["updateValues", "obj", "valueToSet", "isFieldUpdate", "Object", "keys", "reduce", "acc", "current", "currentValue", "isObject"], "mappings": ";;;;AAEA;;;;;AAKC,UACKA,YAAe,GAAA,CAACC,GAAaC,EAAAA,UAAAA,EAAqBC,gBAAgB,KAAK,GAAA;AAC3E,IAAA,OAAOC,OAAOC,IAAI,CAACJ,KAAKK,MAAM,CAAC,CAACC,GAAKC,EAAAA,OAAAA,GAAAA;QACnC,MAAMC,YAAAA,GAAeR,GAAG,CAACO,OAAwB,CAAA;QAEjD,IAAIA,OAAAA,KAAY,YAAgB,IAAA,CAACL,aAAe,EAAA;;YAE9CI,GAAG,CAACC,QAAQ,GAAGC,YAAAA;YAEf,OAAOF,GAAAA;AACT;AAEA,QAAA,IAAIG,iBAASD,YAAe,CAAA,EAAA;YAC1B,OAAO;AAAE,gBAAA,GAAGF,GAAG;AAAE,gBAAA,CAACC,OAAQ,GAAER,YAAaS,CAAAA,YAAAA,EAAcP,YAAYM,OAAY,KAAA,QAAA;AAAU,aAAA;AAC3F;;QAGAD,GAAG,CAACC,QAAQ,GAAGN,UAAAA;QAEf,OAAOK,GAAAA;AACT,KAAA,EAAG,EAAC,CAAA;AACN;;;;"}