{"version": 3, "file": "validation.mjs", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Users/<USER>/validation.ts"], "sourcesContent": ["import * as yup from 'yup';\n\nimport { translatedErrors } from '../../../../../utils/translatedErrors';\n\n/**\n * @description This needs wrapping in `yup.object().shape()` before use.\n */\nconst COMMON_USER_SCHEMA = {\n  firstname: yup.string().trim().required({\n    id: translatedErrors.required.id,\n    defaultMessage: 'This field is required',\n  }),\n  lastname: yup.string().nullable(),\n  email: yup.string().email(translatedErrors.email).lowercase().required({\n    id: translatedErrors.required.id,\n    defaultMessage: 'This field is required',\n  }),\n  username: yup\n    .string()\n    .transform((value) => (value === '' ? undefined : value))\n    .nullable(),\n  password: yup\n    .string()\n    .transform((value) => (value === '' || value === null ? undefined : value))\n    .nullable()\n    .min(8, {\n      ...translatedErrors.minLength,\n      values: { min: 8 },\n    })\n    .test(\n      'max-bytes',\n      {\n        id: 'components.Input.error.contain.maxBytes',\n        defaultMessage: 'Password must be less than 73 bytes',\n      },\n      function (value) {\n        if (!value) return true;\n        return new TextEncoder().encode(value).length <= 72;\n      }\n    )\n    .matches(/[a-z]/, {\n      id: 'components.Input.error.contain.lowercase',\n      defaultMessage: 'Password must contain at least one lowercase character',\n    })\n    .matches(/[A-Z]/, {\n      id: 'components.Input.error.contain.uppercase',\n      defaultMessage: 'Password must contain at least one uppercase character',\n    })\n    .matches(/\\d/, {\n      id: 'components.Input.error.contain.number',\n      defaultMessage: 'Password must contain at least one number',\n    }),\n  confirmPassword: yup\n    .string()\n    .transform((value) => (value === '' ? null : value))\n    .nullable()\n    .min(8, {\n      ...translatedErrors.minLength,\n      values: { min: 8 },\n    })\n    .oneOf([yup.ref('password'), null], {\n      id: 'components.Input.error.password.noMatch',\n      defaultMessage: 'Passwords must match',\n    })\n    .when('password', (password, passSchema) => {\n      return password\n        ? passSchema\n            .required({\n              id: translatedErrors.required.id,\n              defaultMessage: 'This field is required',\n            })\n            .nullable()\n        : passSchema;\n    }),\n};\n\nexport { COMMON_USER_SCHEMA };\n"], "names": ["COMMON_USER_SCHEMA", "firstname", "yup", "string", "trim", "required", "id", "translatedErrors", "defaultMessage", "lastname", "nullable", "email", "lowercase", "username", "transform", "value", "undefined", "password", "min", "<PERSON><PERSON><PERSON><PERSON>", "values", "test", "TextEncoder", "encode", "length", "matches", "confirmPassword", "oneOf", "ref", "when", "passSchema"], "mappings": ";;;AAIA;;AAEC,UACKA,kBAAqB,GAAA;AACzBC,IAAAA,SAAAA,EAAWC,IAAIC,MAAM,EAAA,CAAGC,IAAI,EAAA,CAAGC,QAAQ,CAAC;QACtCC,EAAIC,EAAAA,WAAAA,CAAiBF,QAAQ,CAACC,EAAE;QAChCE,cAAgB,EAAA;AAClB,KAAA,CAAA;IACAC,QAAUP,EAAAA,GAAAA,CAAIC,MAAM,EAAA,CAAGO,QAAQ,EAAA;IAC/BC,KAAOT,EAAAA,GAAAA,CAAIC,MAAM,EAAA,CAAGQ,KAAK,CAACJ,WAAiBI,CAAAA,KAAK,CAAEC,CAAAA,SAAS,EAAGP,CAAAA,QAAQ,CAAC;QACrEC,EAAIC,EAAAA,WAAAA,CAAiBF,QAAQ,CAACC,EAAE;QAChCE,cAAgB,EAAA;AAClB,KAAA,CAAA;IACAK,QAAUX,EAAAA,GAAAA,CACPC,MAAM,EAAA,CACNW,SAAS,CAAC,CAACC,KAAAA,GAAWA,KAAU,KAAA,EAAA,GAAKC,SAAYD,GAAAA,KAAAA,CAAAA,CACjDL,QAAQ,EAAA;AACXO,IAAAA,QAAAA,EAAUf,IACPC,MAAM,EAAA,CACNW,SAAS,CAAC,CAACC,KAAWA,GAAAA,KAAAA,KAAU,EAAMA,IAAAA,KAAAA,KAAU,OAAOC,SAAYD,GAAAA,KAAAA,CAAAA,CACnEL,QAAQ,EACRQ,CAAAA,GAAG,CAAC,CAAG,EAAA;AACN,QAAA,GAAGX,YAAiBY,SAAS;QAC7BC,MAAQ,EAAA;YAAEF,GAAK,EAAA;AAAE;KAElBG,CAAAA,CAAAA,IAAI,CACH,WACA,EAAA;QACEf,EAAI,EAAA,yCAAA;QACJE,cAAgB,EAAA;AAClB,KAAA,EACA,SAAUO,KAAK,EAAA;QACb,IAAI,CAACA,OAAO,OAAO,IAAA;AACnB,QAAA,OAAO,IAAIO,WAAcC,EAAAA,CAAAA,MAAM,CAACR,KAAAA,CAAAA,CAAOS,MAAM,IAAI,EAAA;KAGpDC,CAAAA,CAAAA,OAAO,CAAC,OAAS,EAAA;QAChBnB,EAAI,EAAA,0CAAA;QACJE,cAAgB,EAAA;KAEjBiB,CAAAA,CAAAA,OAAO,CAAC,OAAS,EAAA;QAChBnB,EAAI,EAAA,0CAAA;QACJE,cAAgB,EAAA;KAEjBiB,CAAAA,CAAAA,OAAO,CAAC,IAAM,EAAA;QACbnB,EAAI,EAAA,uCAAA;QACJE,cAAgB,EAAA;AAClB,KAAA,CAAA;AACFkB,IAAAA,eAAAA,EAAiBxB,GACdC,CAAAA,MAAM,EACNW,CAAAA,SAAS,CAAC,CAACC,KAAAA,GAAWA,KAAU,KAAA,EAAA,GAAK,OAAOA,KAC5CL,CAAAA,CAAAA,QAAQ,EACRQ,CAAAA,GAAG,CAAC,CAAG,EAAA;AACN,QAAA,GAAGX,YAAiBY,SAAS;QAC7BC,MAAQ,EAAA;YAAEF,GAAK,EAAA;AAAE;AACnB,KAAA,CAAA,CACCS,KAAK,CAAC;AAACzB,QAAAA,GAAAA,CAAI0B,GAAG,CAAC,UAAA,CAAA;AAAa,QAAA;KAAK,EAAE;QAClCtB,EAAI,EAAA,yCAAA;QACJE,cAAgB,EAAA;AAClB,KAAA,CAAA,CACCqB,IAAI,CAAC,UAAY,EAAA,CAACZ,QAAUa,EAAAA,UAAAA,GAAAA;QAC3B,OAAOb,QAAAA,GACHa,UACGzB,CAAAA,QAAQ,CAAC;YACRC,EAAIC,EAAAA,WAAAA,CAAiBF,QAAQ,CAACC,EAAE;YAChCE,cAAgB,EAAA;AAClB,SAAA,CAAA,CACCE,QAAQ,EACXoB,GAAAA,UAAAA;AACN,KAAA;AACJ;;;;"}