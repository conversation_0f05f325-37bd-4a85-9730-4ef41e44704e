"use strict";Object.defineProperty(exports,"__esModule",{value:true});var t=require("@ucast/mongo2js");function n(t){return Array.isArray(t)?t:[t]}function e(t,n,e){let r=t;let o=n;if(-1!==n.indexOf(".")){const e=n.split(".");o=e.pop();r=e.reduce(((t,n)=>{t[n]=t[n]||{};return t[n]}),t)}r[o]=e}Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function r(t,n,e,r){const o={};const c=t.rulesFor(n,e);for(let t=0;t<c.length;t++){const n=c[t];const e=n.inverted?"$and":"$or";if(!n.conditions)if(n.inverted)break;else{delete o[e];return o}else{o[e]=o[e]||[];o[e].push(r(n))}}return o.$or?o:null}function o(n){if(!n.ast)throw new Error(`Ability rule "${JSON.stringify(n)}" does not have "ast" property. So, cannot be used to generate AST`);return n.inverted?new t.CompoundCondition("not",[n.ast]):n.ast}function c(n,e,c){const u=r(n,e,c,o);if(null===u)return null;if(!u.$and)return u.$or?t.buildOr(u.$or):t.buildAnd([]);if(u.$or)u.$and.push(t.buildOr(u.$or));return t.buildAnd(u.$and)}function u(t,n,r){return t.rulesFor(n,r).reduce(((t,n)=>{if(n.inverted||!n.conditions)return t;return Object.keys(n.conditions).reduce(((t,r)=>{const o=n.conditions[r];if(!o||o.constructor!==Object)e(t,r,o);return t}),t)}),{})}function s(t,n,e,r){const o=t.detectSubjectType(e);const c=t.possibleRulesFor(n,o);const u=new Set;const s=u.delete.bind(u);const i=u.add.bind(u);let f=c.length;while(f--){const t=c[f];if(t.matchesConditions(e)){const n=t.inverted?s:i;r.fieldsFrom(t).forEach(n)}}return Array.from(u)}const i=t=>Array.isArray(t)?t.join(","):t;function f(t,e){return t.map((t=>{const r=[i(t.action||t.actions),"function"===typeof e?n(t.subject).map(e).join(","):i(t.subject),t.conditions||0,t.inverted?1:0,t.fields?i(t.fields):0,t.reason||""];while(r.length>0&&!r[r.length-1])r.pop();return r}))}function l(t,n){return t.map((([t,e,r,o,c,u])=>{const s=e.split(",");const i={inverted:!!o,action:t.split(","),subject:"function"===typeof n?s.map(n):s};if(r)i.conditions=r;if(c)i.fields=c.split(",");if(u)i.reason=u;return i}))}exports.packRules=f;exports.permittedFieldsOf=s;exports.rulesToAST=c;exports.rulesToFields=u;exports.rulesToQuery=r;exports.unpackRules=l;
//# sourceMappingURL=extra.js.map
