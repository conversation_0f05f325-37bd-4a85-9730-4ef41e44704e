import { adminApi } from './api.mjs';

const authService = adminApi.enhanceEndpoints({
    addTagTypes: [
        'User',
        'Me',
        'ProvidersOptions'
    ]
}).injectEndpoints({
    endpoints: (builder)=>({
            /**
       * ME
       */ getMe: builder.query({
                query: ()=>({
                        method: 'GET',
                        url: '/admin/users/me'
                    }),
                transformResponse (res) {
                    return res.data;
                },
                providesTags: (res)=>res ? [
                        'Me',
                        {
                            type: 'User',
                            id: res.id
                        }
                    ] : [
                        'Me'
                    ]
            }),
            getMyPermissions: builder.query({
                query: ()=>({
                        method: 'GET',
                        url: '/admin/users/me/permissions'
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            updateMe: builder.mutation({
                query: (body)=>({
                        method: 'PUT',
                        url: '/admin/users/me',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                },
                invalidatesTags: [
                    'Me'
                ]
            }),
            /**
       * Permissions
       */ checkPermissions: builder.query({
                query: (permissions)=>({
                        method: 'POST',
                        url: '/admin/permissions/check',
                        data: permissions
                    })
            }),
            /**
       * Auth methods
       */ login: builder.mutation({
                query: (body)=>({
                        method: 'POST',
                        url: '/admin/login',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                },
                invalidatesTags: [
                    'Me'
                ]
            }),
            logout: builder.mutation({
                query: ()=>({
                        method: 'POST',
                        url: '/admin/logout'
                    })
            }),
            resetPassword: builder.mutation({
                query: (body)=>({
                        method: 'POST',
                        url: '/admin/reset-password',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            renewToken: builder.mutation({
                query: (body)=>({
                        method: 'POST',
                        url: '/admin/renew-token',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            getRegistrationInfo: builder.query({
                query: (registrationToken)=>({
                        url: '/admin/registration-info',
                        method: 'GET',
                        config: {
                            params: {
                                registrationToken
                            }
                        }
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            registerAdmin: builder.mutation({
                query: (body)=>({
                        method: 'POST',
                        url: '/admin/register-admin',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            registerUser: builder.mutation({
                query: (body)=>({
                        method: 'POST',
                        url: '/admin/register',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            forgotPassword: builder.mutation({
                query: (body)=>({
                        url: '/admin/forgot-password',
                        method: 'POST',
                        data: body
                    })
            }),
            isSSOLocked: builder.query({
                query: ()=>({
                        url: '/admin/providers/isSSOLocked',
                        method: 'GET'
                    }),
                transformResponse (res) {
                    return res.data;
                }
            }),
            getProviders: builder.query({
                query: ()=>({
                        url: '/admin/providers',
                        method: 'GET'
                    })
            }),
            getProviderOptions: builder.query({
                query: ()=>({
                        url: '/admin/providers/options',
                        method: 'GET'
                    }),
                transformResponse (res) {
                    return res.data;
                },
                providesTags: [
                    'ProvidersOptions'
                ]
            }),
            updateProviderOptions: builder.mutation({
                query: (body)=>({
                        url: '/admin/providers/options',
                        method: 'PUT',
                        data: body
                    }),
                transformResponse (res) {
                    return res.data;
                },
                invalidatesTags: [
                    'ProvidersOptions'
                ]
            })
        }),
    overrideExisting: false
});
const { useCheckPermissionsQuery, useLazyCheckPermissionsQuery, useGetMeQuery, useLoginMutation, useRenewTokenMutation, useLogoutMutation, useUpdateMeMutation, useResetPasswordMutation, useRegisterAdminMutation, useRegisterUserMutation, useGetRegistrationInfoQuery, useForgotPasswordMutation, useGetMyPermissionsQuery, useIsSSOLockedQuery, useGetProvidersQuery, useGetProviderOptionsQuery, useUpdateProviderOptionsMutation } = authService;

export { useCheckPermissionsQuery, useForgotPasswordMutation, useGetMeQuery, useGetMyPermissionsQuery, useGetProviderOptionsQuery, useGetProvidersQuery, useGetRegistrationInfoQuery, useIsSSOLockedQuery, useLazyCheckPermissionsQuery, useLoginMutation, useLogoutMutation, useRegisterAdminMutation, useRegisterUserMutation, useRenewTokenMutation, useResetPasswordMutation, useUpdateMeMutation, useUpdateProviderOptionsMutation };
//# sourceMappingURL=auth.mjs.map
