{"version": 3, "file": "users.js", "sources": ["../../../../../admin/src/services/users.ts"], "sourcesContent": ["import * as Permissions from '../../../shared/contracts/permissions';\nimport * as Roles from '../../../shared/contracts/roles';\nimport * as Users from '../../../shared/contracts/user';\n\nimport { adminApi } from './api';\n\nimport type { Data } from '@strapi/types';\n\nconst usersService = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['LicenseLimits', 'User', 'Role', 'RolePermissions'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      /**\n       * users\n       */\n      createUser: builder.mutation<Users.Create.Response['data'], Users.Create.Request['body']>({\n        query: (body) => ({\n          url: '/admin/users',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: Users.Create.Response) => response.data,\n        invalidatesTags: ['LicenseLimits', { type: 'User', id: 'LIST' }],\n      }),\n      updateUser: builder.mutation<\n        Users.Update.Response['data'],\n        Omit<Users.Update.Request['body'] & Users.Update.Params, 'blocked'>\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/users/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        invalidatesTags: (_res, _err, { id }) => [\n          { type: 'User', id },\n          { type: 'User', id: 'LIST' },\n        ],\n      }),\n      getUsers: builder.query<\n        {\n          users: Users.FindAll.Response['data']['results'];\n          pagination: Users.FindAll.Response['data']['pagination'] | null;\n        },\n        GetUsersParams\n      >({\n        query: ({ id, ...params } = {}) => ({\n          url: `/admin/users/${id ?? ''}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Users.FindAll.Response | Users.FindOne.Response) => {\n          let users: Users.FindAll.Response['data']['results'] = [];\n\n          if (res.data) {\n            if ('results' in res.data) {\n              if (Array.isArray(res.data.results)) {\n                users = res.data.results;\n              }\n            } else {\n              users = [res.data];\n            }\n          }\n\n          return {\n            users,\n            pagination: 'pagination' in res.data ? res.data.pagination : null,\n          };\n        },\n        providesTags: (res, _err, arg) => {\n          if (typeof arg === 'object' && 'id' in arg) {\n            return [{ type: 'User' as const, id: arg.id }];\n          } else {\n            return [\n              ...(res?.users.map(({ id }) => ({ type: 'User' as const, id })) ?? []),\n              { type: 'User' as const, id: 'LIST' },\n            ];\n          }\n        },\n      }),\n      deleteManyUsers: builder.mutation<\n        Users.DeleteMany.Response['data'],\n        Users.DeleteMany.Request['body']\n      >({\n        query: (body) => ({\n          url: '/admin/users/batch-delete',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (res: Users.DeleteMany.Response) => res.data,\n        invalidatesTags: ['LicenseLimits', { type: 'User', id: 'LIST' }],\n      }),\n      /**\n       * roles\n       */\n      createRole: builder.mutation<Roles.Create.Response['data'], Roles.Create.Request['body']>({\n        query: (body) => ({\n          url: '/admin/roles',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (res: Roles.Create.Response) => res.data,\n        invalidatesTags: [{ type: 'Role', id: 'LIST' }],\n      }),\n      getRoles: builder.query<Roles.FindRoles.Response['data'], GetRolesParams | void>({\n        query: ({ id, ...params } = {}) => ({\n          url: `/admin/roles/${id ?? ''}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Roles.FindRole.Response | Roles.FindRoles.Response) => {\n          let roles: Roles.FindRoles.Response['data'] = [];\n\n          if (res.data) {\n            if (Array.isArray(res.data)) {\n              roles = res.data;\n            } else {\n              roles = [res.data];\n            }\n          }\n\n          return roles;\n        },\n        providesTags: (res, _err, arg) => {\n          if (typeof arg === 'object' && 'id' in arg) {\n            return [{ type: 'Role' as const, id: arg.id }];\n          } else {\n            return [\n              ...(res?.map(({ id }) => ({ type: 'Role' as const, id })) ?? []),\n              { type: 'Role' as const, id: 'LIST' },\n            ];\n          }\n        },\n      }),\n      updateRole: builder.mutation<\n        Roles.Update.Response['data'],\n        Roles.Update.Request['body'] & Roles.Update.Request['params']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/roles/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (res: Roles.Create.Response) => res.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'Role' as const, id }],\n      }),\n      getRolePermissions: builder.query<\n        Roles.GetPermissions.Response['data'],\n        GetRolePermissionsParams\n      >({\n        query: ({ id, ...params }) => ({\n          url: `/admin/roles/${id}/permissions`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Roles.GetPermissions.Response) => res.data,\n        providesTags: (_res, _err, { id }) => [{ type: 'RolePermissions' as const, id }],\n      }),\n      updateRolePermissions: builder.mutation<\n        Roles.UpdatePermissions.Response['data'],\n        Roles.UpdatePermissions.Request['body'] & Roles.UpdatePermissions.Request['params']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/roles/${id}/permissions`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (res: Roles.UpdatePermissions.Response) => res.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'RolePermissions' as const, id }],\n      }),\n      /**\n       * Permissions\n       */\n      getRolePermissionLayout: builder.query<\n        Permissions.GetAll.Response['data'],\n        Permissions.GetAll.Request['params']\n      >({\n        query: (params) => ({\n          url: '/admin/permissions',\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Permissions.GetAll.Response) => res.data,\n      }),\n    }),\n    overrideExisting: false,\n  });\n\ntype GetUsersParams =\n  | Users.FindOne.Params\n  | (Users.FindAll.Request['query'] & { id?: never })\n  | void;\ntype GetRolesParams =\n  | Roles.FindRole.Request['params']\n  | (Roles.FindRoles.Request['query'] & { id?: never });\ninterface GetRolePermissionsParams {\n  id: Data.ID;\n}\n\nconst {\n  useCreateUserMutation,\n  useGetUsersQuery,\n  useUpdateUserMutation,\n  useDeleteManyUsersMutation,\n  useGetRolesQuery,\n  useCreateRoleMutation,\n  useUpdateRoleMutation,\n  useGetRolePermissionsQuery,\n  useGetRolePermissionLayoutQuery,\n  useUpdateRolePermissionsMutation,\n} = usersService;\n\nconst useAdminUsers = useGetUsersQuery;\n\nexport {\n  useUpdateUserMutation,\n  useGetRolesQuery,\n  useAdminUsers,\n  useDeleteManyUsersMutation,\n  useCreateUserMutation,\n  useGetRolePermissionsQuery,\n  useGetRolePermissionLayoutQuery,\n  useCreateRoleMutation,\n  useUpdateRolePermissionsMutation,\n  useUpdateRoleMutation,\n};\nexport type { GetRolesParams, GetUsersParams, GetRolePermissionsParams };\n"], "names": ["usersService", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "createUser", "mutation", "query", "body", "url", "method", "data", "transformResponse", "response", "invalidatesTags", "type", "id", "updateUser", "_res", "_err", "getUsers", "params", "config", "res", "users", "Array", "isArray", "results", "pagination", "providesTags", "arg", "map", "deleteManyUsers", "createRole", "getRoles", "roles", "updateRole", "getRolePermissions", "updateRolePermissions", "getRolePermissionLayout", "overrideExisting", "useCreateUserMutation", "useGetUsersQuery", "useUpdateUserMutation", "useDeleteManyUsersMutation", "useGetRolesQuery", "useCreateRoleMutation", "useUpdateRoleMutation", "useGetRolePermissionsQuery", "useGetRolePermissionLayoutQuery", "useUpdateRolePermissionsMutation", "useAdminUsers"], "mappings": ";;;;AAQA,MAAMA,YAAAA,GAAeC,YAClBC,CAAAA,gBAAgB,CAAC;IAChBC,WAAa,EAAA;AAAC,QAAA,eAAA;AAAiB,QAAA,MAAA;AAAQ,QAAA,MAAA;AAAQ,QAAA;AAAkB;AACnE,CAAA,CAAA,CACCC,eAAe,CAAC;IACfC,SAAW,EAAA,CAACC,WAAa;AACvB;;UAGAC,UAAAA,EAAYD,OAAQE,CAAAA,QAAQ,CAA8D;gBACxFC,KAAO,EAAA,CAACC,QAAU;wBAChBC,GAAK,EAAA,cAAA;wBACLC,MAAQ,EAAA,MAAA;wBACRC,IAAMH,EAAAA;qBACR,CAAA;gBACAI,iBAAmB,EAAA,CAACC,QAAoCA,GAAAA,QAAAA,CAASF,IAAI;gBACrEG,eAAiB,EAAA;AAAC,oBAAA,eAAA;AAAiB,oBAAA;wBAAEC,IAAM,EAAA,MAAA;wBAAQC,EAAI,EAAA;AAAO;AAAE;AAClE,aAAA,CAAA;YACAC,UAAYb,EAAAA,OAAAA,CAAQE,QAAQ,CAG1B;AACAC,gBAAAA,KAAAA,EAAO,CAAC,EAAES,EAAE,EAAE,GAAGR,IAAAA,EAAM,IAAM;AAC3BC,wBAAAA,GAAAA,EAAK,CAAC,aAAa,EAAEO,EAAAA,CAAG,CAAC;wBACzBN,MAAQ,EAAA,KAAA;wBACRC,IAAMH,EAAAA;qBACR,CAAA;AACAM,gBAAAA,eAAAA,EAAiB,CAACI,IAAMC,EAAAA,IAAAA,EAAM,EAAEH,EAAE,EAAE,GAAK;AACvC,wBAAA;4BAAED,IAAM,EAAA,MAAA;AAAQC,4BAAAA;AAAG,yBAAA;AACnB,wBAAA;4BAAED,IAAM,EAAA,MAAA;4BAAQC,EAAI,EAAA;AAAO;AAC5B;AACH,aAAA,CAAA;YACAI,QAAUhB,EAAAA,OAAAA,CAAQG,KAAK,CAMrB;gBACAA,KAAO,EAAA,CAAC,EAAES,EAAE,EAAE,GAAGK,QAAQ,GAAG,EAAE,IAAM;AAClCZ,wBAAAA,GAAAA,EAAK,CAAC,aAAa,EAAEO,EAAAA,IAAM,GAAG,CAAC;wBAC/BN,MAAQ,EAAA,KAAA;wBACRY,MAAQ,EAAA;AACND,4BAAAA;AACF;qBACF,CAAA;AACAT,gBAAAA,iBAAAA,EAAmB,CAACW,GAAAA,GAAAA;AAClB,oBAAA,IAAIC,QAAmD,EAAE;oBAEzD,IAAID,GAAAA,CAAIZ,IAAI,EAAE;wBACZ,IAAI,SAAA,IAAaY,GAAIZ,CAAAA,IAAI,EAAE;AACzB,4BAAA,IAAIc,MAAMC,OAAO,CAACH,IAAIZ,IAAI,CAACgB,OAAO,CAAG,EAAA;gCACnCH,KAAQD,GAAAA,GAAAA,CAAIZ,IAAI,CAACgB,OAAO;AAC1B;yBACK,MAAA;4BACLH,KAAQ,GAAA;AAACD,gCAAAA,GAAAA,CAAIZ;AAAK,6BAAA;AACpB;AACF;oBAEA,OAAO;AACLa,wBAAAA,KAAAA;wBACAI,UAAY,EAAA,YAAA,IAAgBL,IAAIZ,IAAI,GAAGY,IAAIZ,IAAI,CAACiB,UAAU,GAAG;AAC/D,qBAAA;AACF,iBAAA;gBACAC,YAAc,EAAA,CAACN,KAAKJ,IAAMW,EAAAA,GAAAA,GAAAA;AACxB,oBAAA,IAAI,OAAOA,GAAAA,KAAQ,QAAY,IAAA,IAAA,IAAQA,GAAK,EAAA;wBAC1C,OAAO;AAAC,4BAAA;gCAAEf,IAAM,EAAA,MAAA;AAAiBC,gCAAAA,EAAAA,EAAIc,IAAId;AAAG;AAAE,yBAAA;qBACzC,MAAA;wBACL,OAAO;AACDO,4BAAAA,GAAAA,GAAAA,EAAKC,MAAMO,GAAI,CAAA,CAAC,EAAEf,EAAE,EAAE,IAAM;oCAAED,IAAM,EAAA,MAAA;AAAiBC,oCAAAA;AAAG,iCAAA,MAAO,EAAE;AACrE,4BAAA;gCAAED,IAAM,EAAA,MAAA;gCAAiBC,EAAI,EAAA;AAAO;AACrC,yBAAA;AACH;AACF;AACF,aAAA,CAAA;YACAgB,eAAiB5B,EAAAA,OAAAA,CAAQE,QAAQ,CAG/B;gBACAC,KAAO,EAAA,CAACC,QAAU;wBAChBC,GAAK,EAAA,2BAAA;wBACLC,MAAQ,EAAA,MAAA;wBACRC,IAAMH,EAAAA;qBACR,CAAA;gBACAI,iBAAmB,EAAA,CAACW,GAAmCA,GAAAA,GAAAA,CAAIZ,IAAI;gBAC/DG,eAAiB,EAAA;AAAC,oBAAA,eAAA;AAAiB,oBAAA;wBAAEC,IAAM,EAAA,MAAA;wBAAQC,EAAI,EAAA;AAAO;AAAE;AAClE,aAAA,CAAA;AACA;;UAGAiB,UAAAA,EAAY7B,OAAQE,CAAAA,QAAQ,CAA8D;gBACxFC,KAAO,EAAA,CAACC,QAAU;wBAChBC,GAAK,EAAA,cAAA;wBACLC,MAAQ,EAAA,MAAA;wBACRC,IAAMH,EAAAA;qBACR,CAAA;gBACAI,iBAAmB,EAAA,CAACW,GAA+BA,GAAAA,GAAAA,CAAIZ,IAAI;gBAC3DG,eAAiB,EAAA;AAAC,oBAAA;wBAAEC,IAAM,EAAA,MAAA;wBAAQC,EAAI,EAAA;AAAO;AAAE;AACjD,aAAA,CAAA;YACAkB,QAAU9B,EAAAA,OAAAA,CAAQG,KAAK,CAA0D;gBAC/EA,KAAO,EAAA,CAAC,EAAES,EAAE,EAAE,GAAGK,QAAQ,GAAG,EAAE,IAAM;AAClCZ,wBAAAA,GAAAA,EAAK,CAAC,aAAa,EAAEO,EAAAA,IAAM,GAAG,CAAC;wBAC/BN,MAAQ,EAAA,KAAA;wBACRY,MAAQ,EAAA;AACND,4BAAAA;AACF;qBACF,CAAA;AACAT,gBAAAA,iBAAAA,EAAmB,CAACW,GAAAA,GAAAA;AAClB,oBAAA,IAAIY,QAA0C,EAAE;oBAEhD,IAAIZ,GAAAA,CAAIZ,IAAI,EAAE;AACZ,wBAAA,IAAIc,KAAMC,CAAAA,OAAO,CAACH,GAAAA,CAAIZ,IAAI,CAAG,EAAA;AAC3BwB,4BAAAA,KAAAA,GAAQZ,IAAIZ,IAAI;yBACX,MAAA;4BACLwB,KAAQ,GAAA;AAACZ,gCAAAA,GAAAA,CAAIZ;AAAK,6BAAA;AACpB;AACF;oBAEA,OAAOwB,KAAAA;AACT,iBAAA;gBACAN,YAAc,EAAA,CAACN,KAAKJ,IAAMW,EAAAA,GAAAA,GAAAA;AACxB,oBAAA,IAAI,OAAOA,GAAAA,KAAQ,QAAY,IAAA,IAAA,IAAQA,GAAK,EAAA;wBAC1C,OAAO;AAAC,4BAAA;gCAAEf,IAAM,EAAA,MAAA;AAAiBC,gCAAAA,EAAAA,EAAIc,IAAId;AAAG;AAAE,yBAAA;qBACzC,MAAA;wBACL,OAAO;AACDO,4BAAAA,GAAAA,GAAAA,EAAKQ,IAAI,CAAC,EAAEf,EAAE,EAAE,IAAM;oCAAED,IAAM,EAAA,MAAA;AAAiBC,oCAAAA;AAAG,iCAAA,MAAO,EAAE;AAC/D,4BAAA;gCAAED,IAAM,EAAA,MAAA;gCAAiBC,EAAI,EAAA;AAAO;AACrC,yBAAA;AACH;AACF;AACF,aAAA,CAAA;YACAoB,UAAYhC,EAAAA,OAAAA,CAAQE,QAAQ,CAG1B;AACAC,gBAAAA,KAAAA,EAAO,CAAC,EAAES,EAAE,EAAE,GAAGR,IAAAA,EAAM,IAAM;AAC3BC,wBAAAA,GAAAA,EAAK,CAAC,aAAa,EAAEO,EAAAA,CAAG,CAAC;wBACzBN,MAAQ,EAAA,KAAA;wBACRC,IAAMH,EAAAA;qBACR,CAAA;gBACAI,iBAAmB,EAAA,CAACW,GAA+BA,GAAAA,GAAAA,CAAIZ,IAAI;AAC3DG,gBAAAA,eAAAA,EAAiB,CAACI,IAAMC,EAAAA,IAAAA,EAAM,EAAEH,EAAE,EAAE,GAAK;AAAC,wBAAA;4BAAED,IAAM,EAAA,MAAA;AAAiBC,4BAAAA;AAAG;AAAE;AAC1E,aAAA,CAAA;YACAqB,kBAAoBjC,EAAAA,OAAAA,CAAQG,KAAK,CAG/B;AACAA,gBAAAA,KAAAA,EAAO,CAAC,EAAES,EAAE,EAAE,GAAGK,MAAAA,EAAQ,IAAM;AAC7BZ,wBAAAA,GAAAA,EAAK,CAAC,aAAa,EAAEO,EAAAA,CAAG,YAAY,CAAC;wBACrCN,MAAQ,EAAA,KAAA;wBACRY,MAAQ,EAAA;AACND,4BAAAA;AACF;qBACF,CAAA;gBACAT,iBAAmB,EAAA,CAACW,GAAuCA,GAAAA,GAAAA,CAAIZ,IAAI;AACnEkB,gBAAAA,YAAAA,EAAc,CAACX,IAAMC,EAAAA,IAAAA,EAAM,EAAEH,EAAE,EAAE,GAAK;AAAC,wBAAA;4BAAED,IAAM,EAAA,iBAAA;AAA4BC,4BAAAA;AAAG;AAAE;AAClF,aAAA,CAAA;YACAsB,qBAAuBlC,EAAAA,OAAAA,CAAQE,QAAQ,CAGrC;AACAC,gBAAAA,KAAAA,EAAO,CAAC,EAAES,EAAE,EAAE,GAAGR,IAAAA,EAAM,IAAM;AAC3BC,wBAAAA,GAAAA,EAAK,CAAC,aAAa,EAAEO,EAAAA,CAAG,YAAY,CAAC;wBACrCN,MAAQ,EAAA,KAAA;wBACRC,IAAMH,EAAAA;qBACR,CAAA;gBACAI,iBAAmB,EAAA,CAACW,GAA0CA,GAAAA,GAAAA,CAAIZ,IAAI;AACtEG,gBAAAA,eAAAA,EAAiB,CAACI,IAAMC,EAAAA,IAAAA,EAAM,EAAEH,EAAE,EAAE,GAAK;AAAC,wBAAA;4BAAED,IAAM,EAAA,iBAAA;AAA4BC,4BAAAA;AAAG;AAAE;AACrF,aAAA,CAAA;AACA;;UAGAuB,uBAAAA,EAAyBnC,OAAQG,CAAAA,KAAK,CAGpC;gBACAA,KAAO,EAAA,CAACc,UAAY;wBAClBZ,GAAK,EAAA,oBAAA;wBACLC,MAAQ,EAAA,KAAA;wBACRY,MAAQ,EAAA;AACND,4BAAAA;AACF;qBACF,CAAA;gBACAT,iBAAmB,EAAA,CAACW,GAAqCA,GAAAA,GAAAA,CAAIZ;AAC/D,aAAA;SACF,CAAA;IACA6B,gBAAkB,EAAA;AACpB,CAAA,CAAA;AAaI,MAAA,EACJC,qBAAqB,EACrBC,gBAAgB,EAChBC,qBAAqB,EACrBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,EACrBC,qBAAqB,EACrBC,0BAA0B,EAC1BC,+BAA+B,EAC/BC,gCAAgC,EACjC,GAAGpD;AAEJ,MAAMqD,aAAgBT,GAAAA;;;;;;;;;;;;;"}