{"version": 3, "file": "utils.js", "sources": ["../../../../../../../server/src/services/permission/sections-builder/utils.ts"], "sourcesContent": ["import { curry, matchesProperty, pick } from 'lodash/fp';\nimport type { Internal, Struct } from '@strapi/types';\n\nconst isOfKind = (kind: unknown) => matchesProperty('kind', kind);\n\nconst resolveContentType = (uid: Internal.UID.ContentType): Struct.ContentTypeSchema => {\n  return strapi.contentTypes[uid];\n};\n\nconst isNotInSubjects = (subjects: any) => (uid: unknown) =>\n  !subjects.find((subject: any) => subject.uid === uid);\n\nconst hasProperty = curry((property: unknown, subject: any) => {\n  return !!subject.properties.find((prop: any) => prop.value === property);\n});\n\nconst getValidOptions = pick(['applyToProperties']);\n\nconst toSubjectTemplate = (ct: any) => ({\n  uid: ct.uid,\n  label: ct.info.singularName,\n  properties: [],\n});\n\nexport {\n  isOfKind,\n  resolveContentType,\n  isNotInSubjects,\n  hasProperty,\n  getValidOptions,\n  toSubjectTemplate,\n};\n"], "names": ["isOfKind", "kind", "matchesProperty", "resolveContentType", "uid", "strapi", "contentTypes", "isNotInSubjects", "subjects", "find", "subject", "hasProperty", "curry", "property", "properties", "prop", "value", "getValidOptions", "pick", "toSubjectTemplate", "ct", "label", "info", "singularName"], "mappings": ";;;;AAGA,MAAMA,QAAW,GAAA,CAACC,IAAkBC,GAAAA,kBAAAA,CAAgB,MAAQD,EAAAA,IAAAA;AAE5D,MAAME,qBAAqB,CAACC,GAAAA,GAAAA;IAC1B,OAAOC,MAAAA,CAAOC,YAAY,CAACF,GAAI,CAAA;AACjC;AAEA,MAAMG,eAAkB,GAAA,CAACC,QAAkB,GAAA,CAACJ,GAC1C,GAAA,CAACI,QAASC,CAAAA,IAAI,CAAC,CAACC,OAAiBA,GAAAA,OAAAA,CAAQN,GAAG,KAAKA,GAAAA;AAE7CO,MAAAA,WAAAA,GAAcC,QAAM,CAAA,CAACC,QAAmBH,EAAAA,OAAAA,GAAAA;IAC5C,OAAO,CAAC,CAACA,OAAAA,CAAQI,UAAU,CAACL,IAAI,CAAC,CAACM,IAAAA,GAAcA,IAAKC,CAAAA,KAAK,KAAKH,QAAAA,CAAAA;AACjE,CAAA;AAEA,MAAMI,kBAAkBC,OAAK,CAAA;AAAC,IAAA;AAAoB,CAAA;AAE5CC,MAAAA,iBAAAA,GAAoB,CAACC,EAAAA,IAAa;AACtChB,QAAAA,GAAAA,EAAKgB,GAAGhB,GAAG;QACXiB,KAAOD,EAAAA,EAAAA,CAAGE,IAAI,CAACC,YAAY;AAC3BT,QAAAA,UAAAA,EAAY;KACd;;;;;;;;;"}