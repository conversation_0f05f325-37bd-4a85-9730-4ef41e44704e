import { jsx, jsxs } from 'react/jsx-runtime';
import { Badge, Flex, Typography } from '@strapi/design-system';
import { Lightning } from '@strapi/icons';
import { styled } from 'styled-components';

const GradientBadge = styled(Badge)`
  background: linear-gradient(
    90deg,
    ${({ theme })=>theme.colors.primary600} 0%,
    ${({ theme })=>theme.colors.alternative600} 121.48%
  );
  padding: 0.4rem 1rem;
`;
const GradientBadgeWithIcon = ({ label })=>{
    return /*#__PURE__*/ jsx(GradientBadge, {
        children: /*#__PURE__*/ jsxs(Flex, {
            gap: 1,
            alignItems: "center",
            children: [
                /*#__PURE__*/ jsx(Lightning, {
                    width: 16,
                    height: 16,
                    fill: "neutral0"
                }),
                /*#__PURE__*/ jsx(Typography, {
                    textColor: "neutral0",
                    children: label
                })
            ]
        })
    });
};

export { GradientBadgeWithIcon as GradientBadge };
//# sourceMappingURL=GradientBadge.mjs.map
