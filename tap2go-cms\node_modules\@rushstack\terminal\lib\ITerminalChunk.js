"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See LICENSE in the project root for license information.
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalChunkKind = void 0;
/**
 * Specifies the kind of data represented by a {@link ITerminalChunk} object.
 * @public
 */
var TerminalChunkKind;
(function (TerminalChunkKind) {
    /**
     * Indicates a `ITerminalChunk` object representing `stdout` console output.
     */
    TerminalChunkKind["Stdout"] = "O";
    /**
     * Indicates a `ITerminalChunk` object representing `stderr` console output.
     */
    TerminalChunkKind["Stderr"] = "E";
})(TerminalChunkKind || (exports.TerminalChunkKind = TerminalChunkKind = {}));
//# sourceMappingURL=ITerminalChunk.js.map