{"version": 3, "file": "useMarketplaceData.js", "sources": ["../../../../../../../admin/src/pages/Marketplace/hooks/useMarketplaceData.ts"], "sourcesContent": ["import { useNotifyAT } from '@strapi/design-system';\nimport * as qs from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useQuery } from 'react-query';\n\nimport { useNotification } from '../../../features/Notifications';\n\nimport type { MarketplacePageQuery, NpmPackageType, TabQuery } from '../MarketplacePage';\n\nconst MARKETPLACE_API_URL = 'https://market-api.strapi.io';\n\ninterface UseMarketplaceDataParams {\n  npmPackageType: NpmPackageType;\n  debouncedSearch: string;\n  query?: MarketplacePageQuery;\n  tabQuery: TabQuery;\n  strapiVersion?: string | null;\n}\n\ntype Collections =\n  | 'Verified'\n  | 'Made by the community'\n  | 'Made by Strapi'\n  | 'Made by official partners';\n\ntype Categories = 'Custom fields' | 'Deployment' | 'Monitoring';\n\ntype FilterTypes = 'categories' | 'collections';\n\ninterface Plugin {\n  id: string;\n  attributes: {\n    name: string;\n    description: string;\n    slug: string;\n    npmPackageName: string;\n    npmPackageUrl: string;\n    npmDownloads: number;\n    repositoryUrl: string;\n    githubStars: number;\n    logo: {\n      url: string;\n    };\n    developerName: string;\n    validated: boolean;\n    madeByStrapi: boolean;\n    strapiCompatibility: string;\n    submissionDate: string;\n    collections: Collections[];\n    categories: Categories[];\n    strapiVersion: string;\n    screenshots: Array<{\n      url: string;\n    }>;\n  };\n}\n\ninterface Provider {\n  id: string;\n  attributes: {\n    name: string;\n    description: string;\n    slug: string;\n    npmPackageName: string;\n    npmPackageUrl: string;\n    npmDownloads: number;\n    repositoryUrl: string;\n    githubStars: number;\n    pluginName: string;\n    logo: {\n      url: string;\n    };\n    developerName: string;\n    validated: boolean;\n    madeByStrapi: boolean;\n    strapiCompatibility: string;\n    strapiVersion?: never;\n    submissionDate: string;\n    collections: Collections[];\n  };\n}\n\ninterface MarketplaceMeta {\n  collections: Record<Collections, number>;\n  pagination: {\n    page: number;\n    pageSize: number;\n    pageCount: number;\n    total: number;\n  };\n}\n\ninterface MarketplaceResponse<TData extends Plugin | Provider> {\n  data: TData[];\n  meta: TData extends Provider\n    ? MarketplaceMeta\n    : MarketplaceMeta & { categories: Record<Categories, number> };\n}\n\nfunction useMarketplaceData({\n  npmPackageType,\n  debouncedSearch,\n  query,\n  tabQuery,\n  strapiVersion,\n}: UseMarketplaceDataParams) {\n  const { notifyStatus } = useNotifyAT();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const marketplaceTitle = formatMessage({\n    id: 'global.marketplace',\n    defaultMessage: 'Marketplace',\n  });\n\n  const notifyMarketplaceLoad = () => {\n    notifyStatus(\n      formatMessage(\n        {\n          id: 'app.utils.notify.data-loaded',\n          defaultMessage: 'The {target} has loaded',\n        },\n        { target: marketplaceTitle }\n      )\n    );\n  };\n\n  const paginationParams = {\n    page: query?.page || 1,\n    pageSize: query?.pageSize || 24,\n  };\n\n  const pluginParams = {\n    ...tabQuery.plugin,\n    pagination: paginationParams,\n    search: debouncedSearch,\n    version: strapiVersion,\n  };\n\n  const { data: pluginsResponse, status: pluginsStatus } = useQuery(\n    ['marketplace', 'plugins', pluginParams],\n    async () => {\n      try {\n        const queryString = qs.stringify(pluginParams);\n        const res = await fetch(`${MARKETPLACE_API_URL}/plugins?${queryString}`);\n\n        if (!res.ok) {\n          throw new Error('Failed to fetch marketplace plugins.');\n        }\n\n        const data = (await res.json()) as MarketplaceResponse<Plugin>;\n        return data;\n      } catch (error) {\n        // silence\n      }\n\n      return null;\n    },\n    {\n      onSuccess() {\n        notifyMarketplaceLoad();\n      },\n      onError() {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n        });\n      },\n    }\n  );\n\n  const providerParams = {\n    ...tabQuery.provider,\n    pagination: paginationParams,\n    search: debouncedSearch,\n    version: strapiVersion,\n  };\n\n  const { data: providersResponse, status: providersStatus } = useQuery(\n    ['marketplace', 'providers', providerParams],\n    async () => {\n      const queryString = qs.stringify(providerParams);\n      const res = await fetch(`${MARKETPLACE_API_URL}/providers?${queryString}`);\n\n      if (!res.ok) {\n        throw new Error('Failed to fetch marketplace providers.');\n      }\n\n      const data = (await res.json()) as MarketplaceResponse<Provider>;\n\n      return data;\n    },\n    {\n      onSuccess() {\n        notifyMarketplaceLoad();\n      },\n      onError() {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n        });\n      },\n    }\n  );\n\n  const npmPackageTypeResponse = npmPackageType === 'plugin' ? pluginsResponse : providersResponse;\n\n  const possibleCollections = npmPackageTypeResponse?.meta.collections ?? {};\n  const possibleCategories = pluginsResponse?.meta.categories ?? {};\n\n  const { pagination } = npmPackageTypeResponse?.meta ?? {};\n\n  return {\n    pluginsResponse,\n    providersResponse,\n    pluginsStatus,\n    providersStatus,\n    possibleCollections,\n    possibleCategories,\n    pagination,\n  };\n}\n\nexport { useMarketplaceData };\nexport type {\n  MarketplaceResponse,\n  Plugin,\n  Provider,\n  MarketplaceMeta,\n  Collections,\n  Categories,\n  FilterTypes,\n  UseMarketplaceDataParams,\n};\n"], "names": ["MARKETPLACE_API_URL", "useMarketplaceData", "npmPackageType", "debouncedSearch", "query", "tab<PERSON><PERSON>y", "strapiVersion", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "formatMessage", "useIntl", "toggleNotification", "useNotification", "marketplaceTitle", "id", "defaultMessage", "notifyMarketplaceLoad", "target", "paginationParams", "page", "pageSize", "pluginParams", "plugin", "pagination", "search", "version", "data", "pluginsResponse", "status", "pluginsStatus", "useQuery", "queryString", "qs", "stringify", "res", "fetch", "ok", "Error", "json", "error", "onSuccess", "onError", "type", "message", "providerParams", "provider", "providersResponse", "providersStatus", "npmPackageTypeResponse", "possibleCollections", "meta", "collections", "possibleCategories", "categories"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,MAAMA,mBAAsB,GAAA,8BAAA;AA0F5B,SAASC,kBAAAA,CAAmB,EAC1BC,cAAc,EACdC,eAAe,EACfC,KAAK,EACLC,QAAQ,EACRC,aAAa,EACY,EAAA;IACzB,MAAM,EAAEC,YAAY,EAAE,GAAGC,wBAAAA,EAAAA;IACzB,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;IAC1B,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,6BAAAA,EAAAA;AAC/B,IAAA,MAAMC,mBAAmBJ,aAAc,CAAA;QACrCK,EAAI,EAAA,oBAAA;QACJC,cAAgB,EAAA;AAClB,KAAA,CAAA;AAEA,IAAA,MAAMC,qBAAwB,GAAA,IAAA;AAC5BT,QAAAA,YAAAA,CACEE,aACE,CAAA;YACEK,EAAI,EAAA,8BAAA;YACJC,cAAgB,EAAA;SAElB,EAAA;YAAEE,MAAQJ,EAAAA;AAAiB,SAAA,CAAA,CAAA;AAGjC,KAAA;AAEA,IAAA,MAAMK,gBAAmB,GAAA;AACvBC,QAAAA,IAAAA,EAAMf,OAAOe,IAAQ,IAAA,CAAA;AACrBC,QAAAA,QAAAA,EAAUhB,OAAOgB,QAAY,IAAA;AAC/B,KAAA;AAEA,IAAA,MAAMC,YAAe,GAAA;AACnB,QAAA,GAAGhB,SAASiB,MAAM;QAClBC,UAAYL,EAAAA,gBAAAA;QACZM,MAAQrB,EAAAA,eAAAA;QACRsB,OAASnB,EAAAA;AACX,KAAA;IAEA,MAAM,EAAEoB,MAAMC,eAAe,EAAEC,QAAQC,aAAa,EAAE,GAAGC,mBACvD,CAAA;AAAC,QAAA,aAAA;AAAe,QAAA,SAAA;AAAWT,QAAAA;KAAa,EACxC,UAAA;QACE,IAAI;YACF,MAAMU,WAAAA,GAAcC,aAAGC,CAAAA,SAAS,CAACZ,YAAAA,CAAAA;YACjC,MAAMa,GAAAA,GAAM,MAAMC,KAAM,CAAA,CAAC,EAAEnC,mBAAoB,CAAA,SAAS,EAAE+B,WAAAA,CAAY,CAAC,CAAA;YAEvE,IAAI,CAACG,GAAIE,CAAAA,EAAE,EAAE;AACX,gBAAA,MAAM,IAAIC,KAAM,CAAA,sCAAA,CAAA;AAClB;YAEA,MAAMX,IAAAA,GAAQ,MAAMQ,GAAAA,CAAII,IAAI,EAAA;YAC5B,OAAOZ,IAAAA;AACT,SAAA,CAAE,OAAOa,KAAO,EAAA;;AAEhB;QAEA,OAAO,IAAA;KAET,EAAA;AACEC,QAAAA,SAAAA,CAAAA,GAAAA;AACExB,YAAAA,qBAAAA,EAAAA;AACF,SAAA;AACAyB,QAAAA,OAAAA,CAAAA,GAAAA;YACE9B,kBAAmB,CAAA;gBACjB+B,IAAM,EAAA,QAAA;AACNC,gBAAAA,OAAAA,EAASlC,aAAc,CAAA;oBAAEK,EAAI,EAAA,oBAAA;oBAAsBC,cAAgB,EAAA;AAAmB,iBAAA;AACxF,aAAA,CAAA;AACF;AACF,KAAA,CAAA;AAGF,IAAA,MAAM6B,cAAiB,GAAA;AACrB,QAAA,GAAGvC,SAASwC,QAAQ;QACpBtB,UAAYL,EAAAA,gBAAAA;QACZM,MAAQrB,EAAAA,eAAAA;QACRsB,OAASnB,EAAAA;AACX,KAAA;IAEA,MAAM,EAAEoB,MAAMoB,iBAAiB,EAAElB,QAAQmB,eAAe,EAAE,GAAGjB,mBAC3D,CAAA;AAAC,QAAA,aAAA;AAAe,QAAA,WAAA;AAAac,QAAAA;KAAe,EAC5C,UAAA;QACE,MAAMb,WAAAA,GAAcC,aAAGC,CAAAA,SAAS,CAACW,cAAAA,CAAAA;QACjC,MAAMV,GAAAA,GAAM,MAAMC,KAAM,CAAA,CAAC,EAAEnC,mBAAoB,CAAA,WAAW,EAAE+B,WAAAA,CAAY,CAAC,CAAA;QAEzE,IAAI,CAACG,GAAIE,CAAAA,EAAE,EAAE;AACX,YAAA,MAAM,IAAIC,KAAM,CAAA,wCAAA,CAAA;AAClB;QAEA,MAAMX,IAAAA,GAAQ,MAAMQ,GAAAA,CAAII,IAAI,EAAA;QAE5B,OAAOZ,IAAAA;KAET,EAAA;AACEc,QAAAA,SAAAA,CAAAA,GAAAA;AACExB,YAAAA,qBAAAA,EAAAA;AACF,SAAA;AACAyB,QAAAA,OAAAA,CAAAA,GAAAA;YACE9B,kBAAmB,CAAA;gBACjB+B,IAAM,EAAA,QAAA;AACNC,gBAAAA,OAAAA,EAASlC,aAAc,CAAA;oBAAEK,EAAI,EAAA,oBAAA;oBAAsBC,cAAgB,EAAA;AAAmB,iBAAA;AACxF,aAAA,CAAA;AACF;AACF,KAAA,CAAA;IAGF,MAAMiC,sBAAAA,GAAyB9C,cAAmB,KAAA,QAAA,GAAWyB,eAAkBmB,GAAAA,iBAAAA;AAE/E,IAAA,MAAMG,mBAAsBD,GAAAA,sBAAAA,EAAwBE,IAAKC,CAAAA,WAAAA,IAAe,EAAC;AACzE,IAAA,MAAMC,kBAAqBzB,GAAAA,eAAAA,EAAiBuB,IAAKG,CAAAA,UAAAA,IAAc,EAAC;AAEhE,IAAA,MAAM,EAAE9B,UAAU,EAAE,GAAGyB,sBAAAA,EAAwBE,QAAQ,EAAC;IAExD,OAAO;AACLvB,QAAAA,eAAAA;AACAmB,QAAAA,iBAAAA;AACAjB,QAAAA,aAAAA;AACAkB,QAAAA,eAAAA;AACAE,QAAAA,mBAAAA;AACAG,QAAAA,kBAAAA;AACA7B,QAAAA;AACF,KAAA;AACF;;;;"}