{"version": 3, "file": "useInjectReducer.mjs", "sources": ["../../../../../admin/src/hooks/useInjectReducer.ts"], "sourcesContent": ["import { useEffect } from 'react';\n\nimport { Reducer } from '@reduxjs/toolkit';\n\nimport { useTypedStore } from '../core/store/hooks';\n\n/**\n * @public\n * @description Inject a new reducer into the global redux-store.\n * @example\n * ```tsx\n * import { reducer } from './local-store';\n *\n * const MyPlugin = () => {\n *  useInjectReducer(\"plugin\", reducer);\n * }\n * ```\n */\nexport function useInjectReducer(namespace: string, reducer: Reducer) {\n  const store = useTypedStore();\n\n  useEffect(() => {\n    store.injectReducer(namespace, reducer);\n  }, [store, namespace, reducer]);\n}\n"], "names": ["useInjectReducer", "namespace", "reducer", "store", "useTypedStore", "useEffect", "injectReducer"], "mappings": ";;;AAMA;;;;;;;;;;;AAWC,IACM,SAASA,gBAAiBC,CAAAA,SAAiB,EAAEC,OAAgB,EAAA;AAClE,IAAA,MAAMC,KAAQC,GAAAA,aAAAA,EAAAA;IAEdC,SAAU,CAAA,IAAA;QACRF,KAAMG,CAAAA,aAAa,CAACL,SAAWC,EAAAA,OAAAA,CAAAA;KAC9B,EAAA;AAACC,QAAAA,KAAAA;AAAOF,QAAAA,SAAAA;AAAWC,QAAAA;AAAQ,KAAA,CAAA;AAChC;;;;"}