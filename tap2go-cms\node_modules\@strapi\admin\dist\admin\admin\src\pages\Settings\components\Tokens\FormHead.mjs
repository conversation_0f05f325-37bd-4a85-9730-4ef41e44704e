import { jsx, jsxs } from 'react/jsx-runtime';
import * as React from 'react';
import { Flex, Tooltip, Button, Dialog } from '@strapi/design-system';
import { EyeStriked, Eye, Check, ArrowClockwise } from '@strapi/icons';
import { useIntl } from 'react-intl';
import { ConfirmDialog } from '../../../../components/ConfirmDialog.mjs';
import { Layouts } from '../../../../components/Layouts/Layout.mjs';
import { BackButton } from '../../../../features/BackButton.mjs';
import { useNotification } from '../../../../features/Notifications.mjs';
import { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler.mjs';
import { useRegenerateTokenMutation } from '../../../../services/transferTokens.mjs';

const Regenerate = ({ onRegenerate, url })=>{
    const { formatMessage } = useIntl();
    const [showConfirmDialog, setShowConfirmDialog] = React.useState(false);
    const [isLoadingConfirmation, setIsLoadingConfirmation] = React.useState(false);
    const { toggleNotification } = useNotification();
    const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
    const [regenerateToken] = useRegenerateTokenMutation();
    const regenerateData = async ()=>{
        try {
            const res = await regenerateToken(url);
            if ('error' in res) {
                toggleNotification({
                    type: 'danger',
                    message: formatAPIError(res.error)
                });
                return;
            }
            if (onRegenerate) {
                onRegenerate(res.data.accessKey);
            }
        } catch (error) {
            toggleNotification({
                type: 'danger',
                message: formatMessage({
                    id: 'notification.error',
                    defaultMessage: 'Something went wrong'
                })
            });
        } finally{
            setIsLoadingConfirmation(false);
        }
    };
    const handleConfirmRegeneration = async ()=>{
        regenerateData();
        setShowConfirmDialog(false);
    };
    return /*#__PURE__*/ jsxs(Dialog.Root, {
        open: showConfirmDialog,
        onOpenChange: setShowConfirmDialog,
        children: [
            /*#__PURE__*/ jsx(Dialog.Trigger, {
                children: /*#__PURE__*/ jsx(Button, {
                    startIcon: /*#__PURE__*/ jsx(ArrowClockwise, {}),
                    type: "button",
                    size: "S",
                    variant: "tertiary",
                    onClick: ()=>setShowConfirmDialog(true),
                    name: "regenerate",
                    children: formatMessage({
                        id: 'Settings.tokens.regenerate',
                        defaultMessage: 'Regenerate'
                    })
                })
            }),
            /*#__PURE__*/ jsx(ConfirmDialog, {
                title: formatMessage({
                    id: 'Settings.tokens.RegenerateDialog.title',
                    defaultMessage: 'Regenerate token'
                }),
                endAction: /*#__PURE__*/ jsx(Button, {
                    startIcon: /*#__PURE__*/ jsx(ArrowClockwise, {}),
                    loading: isLoadingConfirmation,
                    onClick: handleConfirmRegeneration,
                    children: formatMessage({
                        id: 'Settings.tokens.Button.regenerate',
                        defaultMessage: 'Regenerate'
                    })
                }),
                children: formatMessage({
                    id: 'Settings.tokens.popUpWarning.message',
                    defaultMessage: 'Are you sure you want to regenerate this token?'
                })
            })
        ]
    });
};
const FormHead = ({ title, token, setToken, toggleToken, showToken, canShowToken, canEditInputs, canRegenerate, isSubmitting, regenerateUrl })=>{
    const { formatMessage } = useIntl();
    const handleRegenerate = (newKey)=>{
        setToken({
            ...token,
            accessKey: newKey
        });
        toggleToken?.();
    };
    return /*#__PURE__*/ jsx(Layouts.Header, {
        title: token?.name || formatMessage(title),
        primaryAction: canEditInputs ? /*#__PURE__*/ jsxs(Flex, {
            gap: 2,
            children: [
                canRegenerate && token?.id && /*#__PURE__*/ jsx(Regenerate, {
                    onRegenerate: handleRegenerate,
                    url: `${regenerateUrl}${token?.id ?? ''}`
                }),
                token?.id && toggleToken && /*#__PURE__*/ jsx(Tooltip, {
                    label: !canShowToken && formatMessage({
                        id: 'Settings.tokens.encryptionKeyMissing',
                        defaultMessage: 'In order to view the token, you need a valid encryption key in the admin configuration'
                    }),
                    children: /*#__PURE__*/ jsx(Button, {
                        type: "button",
                        startIcon: showToken ? /*#__PURE__*/ jsx(EyeStriked, {}) : /*#__PURE__*/ jsx(Eye, {}),
                        variant: "secondary",
                        onClick: ()=>toggleToken?.(),
                        disabled: !canShowToken,
                        children: formatMessage({
                            id: 'Settings.tokens.viewToken',
                            defaultMessage: 'View token'
                        })
                    })
                }),
                /*#__PURE__*/ jsx(Button, {
                    disabled: isSubmitting,
                    loading: isSubmitting,
                    startIcon: /*#__PURE__*/ jsx(Check, {}),
                    type: "submit",
                    size: "S",
                    children: formatMessage({
                        id: 'global.save',
                        defaultMessage: 'Save'
                    })
                })
            ]
        }) : canRegenerate && token?.id && /*#__PURE__*/ jsx(Regenerate, {
            onRegenerate: handleRegenerate,
            url: `${regenerateUrl}${token?.id ?? ''}`
        }),
        navigationAction: /*#__PURE__*/ jsx(BackButton, {}),
        ellipsis: true
    });
};

export { FormHead };
//# sourceMappingURL=FormHead.mjs.map
