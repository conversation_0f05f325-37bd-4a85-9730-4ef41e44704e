{"version": 3, "file": "usePermissionsDataManager.js", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Roles/hooks/usePermissionsDataManager.ts"], "sourcesContent": ["import { createContext } from '@radix-ui/react-context';\n\nimport { Condition } from '../../../../../../../shared/contracts/permissions';\n\nimport type {\n  OnChangeCollectionTypeGlobalActionCheckboxAction,\n  OnChangeCollectionTypeRowLeftCheckboxAction,\n  OnChangeConditionsAction,\n  State,\n} from '../components/Permissions';\n\n// Note: I had to guess most of these types based on the name and usage, but I actually don't\n// know if they are correct, because the usage is very generic. Feel free to correct them if\n// they create problems.\nexport interface PermissionsDataManagerContextValue extends Pick<State, 'modifiedData'> {\n  availableConditions: Condition[];\n  onChangeCollectionTypeLeftActionRowCheckbox: (\n    pathToCollectionType: OnChangeCollectionTypeRowLeftCheckboxAction['pathToCollectionType'],\n    propertyName: OnChangeCollectionTypeRowLeftCheckboxAction['propertyName'],\n    rowName: OnChangeCollectionTypeRowLeftCheckboxAction['rowName'],\n    value: OnChangeCollectionTypeRowLeftCheckboxAction['value']\n  ) => void;\n  onChangeConditions: (conditions: OnChangeConditionsAction['conditions']) => void;\n  onChangeSimpleCheckbox: (event: { target: { name: string; value: boolean } }) => void;\n  onChangeParentCheckbox: (event: { target: { name: string; value: boolean } }) => void;\n  onChangeCollectionTypeGlobalActionCheckbox: (\n    collectionTypeKind: OnChangeCollectionTypeGlobalActionCheckboxAction['collectionTypeKind'],\n    actionId: OnChangeCollectionTypeGlobalActionCheckboxAction['actionId'],\n    value: OnChangeCollectionTypeGlobalActionCheckboxAction['value']\n  ) => void;\n}\n\nconst [PermissionsDataManagerProvider, usePermissionsDataManagerContext] =\n  createContext<PermissionsDataManagerContextValue>('PermissionsDataManager');\n\nexport const usePermissionsDataManager = () =>\n  usePermissionsDataManagerContext('usePermissionsDataManager');\n\nexport { PermissionsDataManagerProvider };\n"], "names": ["PermissionsDataManagerProvider", "usePermissionsDataManagerContext", "createContext", "usePermissionsDataManager"], "mappings": ";;;;AAgCA,MAAM,CAACA,8BAAAA,EAAgCC,gCAAiC,CAAA,GACtEC,0BAAkD,CAAA,wBAAA;AAEvCC,MAAAA,yBAAAA,GAA4B,IACvCF,gCAAAA,CAAiC,2BAA6B;;;;;"}