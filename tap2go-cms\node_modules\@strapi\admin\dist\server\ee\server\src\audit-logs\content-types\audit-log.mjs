const auditLog = {
    schema: {
        kind: 'collectionType',
        collectionName: 'strapi_audit_logs',
        info: {
            singularName: 'audit-log',
            pluralName: 'audit-logs',
            displayName: 'Audit Log'
        },
        options: {
            timestamps: false
        },
        pluginOptions: {
            'content-manager': {
                visible: false
            },
            'content-type-builder': {
                visible: false
            }
        },
        attributes: {
            action: {
                type: 'string',
                required: true
            },
            date: {
                type: 'datetime',
                required: true
            },
            user: {
                type: 'relation',
                relation: 'oneToOne',
                target: 'admin::user'
            },
            payload: {
                type: 'json'
            }
        }
    }
};

export { auditLog };
//# sourceMappingURL=audit-log.mjs.map
