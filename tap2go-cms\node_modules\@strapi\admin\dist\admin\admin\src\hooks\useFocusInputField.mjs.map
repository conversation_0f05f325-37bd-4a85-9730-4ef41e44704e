{"version": 3, "file": "useFocusInputField.mjs", "sources": ["../../../../../admin/src/hooks/useFocusInputField.ts"], "sourcesContent": ["import { Ref, useEffect, useMemo, useState } from 'react';\n\nimport { useLocation } from 'react-router-dom';\n\n/**\n * @description Given the name of an input field (this does not need to be the name you pass as a prop to the DOM element),\n * when the query param `field` matches the name the field will be focused & scrolled into the center of the view.\n * Uses a callback ref to set the field to ensure asynchronous rendering of inputs does not cause issues e.g. CodeMirror.EditView\n *\n * @example\n * ```tsx\n * const fieldRef = useFocusInputField('name');\n *\n * return (\n *  <input ref={fieldRef} />\n * );\n * ```\n */\nconst useFocusInputField = <T extends HTMLElement>(name: string): Ref<T> => {\n  const { search: searchString } = useLocation();\n  const search = useMemo(() => new URLSearchParams(searchString), [searchString]);\n\n  /**\n   * TODO: remove union and just use `HTMLElement`\n   *\n   * Realistically, it will only be an `HTMLElement` but `TextInput` in the design-system\n   * has an imperativeHandle we can't remove until v2 of the design-system.\n   */\n  const [field, setField] = useState<HTMLElement | null>(null);\n\n  useEffect(() => {\n    if (search.has('field') && search.get('field') === name && field) {\n      field.focus();\n      field.scrollIntoView({\n        block: 'center',\n      });\n    }\n  }, [search, name, field]);\n\n  return setField;\n};\n\nexport { useFocusInputField };\n"], "names": ["useFocusInputField", "name", "search", "searchString", "useLocation", "useMemo", "URLSearchParams", "field", "set<PERSON><PERSON>", "useState", "useEffect", "has", "get", "focus", "scrollIntoView", "block"], "mappings": ";;;AAIA;;;;;;;;;;;;;IAcA,MAAMA,qBAAqB,CAAwBC,IAAAA,GAAAA;AACjD,IAAA,MAAM,EAAEC,MAAAA,EAAQC,YAAY,EAAE,GAAGC,WAAAA,EAAAA;AACjC,IAAA,MAAMF,MAASG,GAAAA,OAAAA,CAAQ,IAAM,IAAIC,gBAAgBH,YAAe,CAAA,EAAA;AAACA,QAAAA;AAAa,KAAA,CAAA;AAE9E;;;;;AAKC,MACD,MAAM,CAACI,KAAOC,EAAAA,QAAAA,CAAS,GAAGC,QAA6B,CAAA,IAAA,CAAA;IAEvDC,SAAU,CAAA,IAAA;QACR,IAAIR,MAAAA,CAAOS,GAAG,CAAC,OAAA,CAAA,IAAYT,OAAOU,GAAG,CAAC,OAAaX,CAAAA,KAAAA,IAAAA,IAAQM,KAAO,EAAA;AAChEA,YAAAA,KAAAA,CAAMM,KAAK,EAAA;AACXN,YAAAA,KAAAA,CAAMO,cAAc,CAAC;gBACnBC,KAAO,EAAA;AACT,aAAA,CAAA;AACF;KACC,EAAA;AAACb,QAAAA,MAAAA;AAAQD,QAAAA,IAAAA;AAAMM,QAAAA;AAAM,KAAA,CAAA;IAExB,OAAOC,QAAAA;AACT;;;;"}