var Analytics = "통계";
var Documentation = "도큐멘테이션";
var Email = "이메일";
var Password = "비밀번호";
var Provider = "프로바이더(provider)";
var ResetPasswordToken = "비밀번호 토큰 재설정";
var Role = "역할";
var Username = "사용자 이름(Username)";
var Users = "사용자";
var anErrorOccurred = "에러가 발생했습니다. 잠시 후에 다시 시도해주세요.";
var clearLabel = "초기화";
var or = "또는";
var skipToContent = "콘텐츠로 건너뛰기";
var submit = "등록";
var ko = {
    Analytics: Analytics,
    "Auth.components.Oops.text": "계정이 정지되었습니다.",
    "Auth.components.Oops.text.admin": "오류가 있는 경우 관리자에게 문의해주세요.",
    "Auth.components.Oops.title": "앗...",
    "Auth.form.button.forgot-password": "메일 보내기",
    "Auth.form.button.go-home": "홈으로",
    "Auth.form.button.login": "로그인",
    "Auth.form.button.login.providers.error": "선택한 프로바이더로 로그인할 수 없습니다.",
    "Auth.form.button.login.strapi": "Strapi 계정으로 로그인",
    "Auth.form.button.password-recovery": "비밀번호 복원",
    "Auth.form.button.register": "등록",
    "Auth.form.confirmPassword.label": "비밀번호 확인",
    "Auth.form.currentPassword.label": "기존 비밀번호",
    "Auth.form.email.label": "이메일",
    "Auth.form.email.placeholder": "<EMAIL>",
    "Auth.form.error.blocked": "관리자에 의해 접근이 제한된 계정입니다.",
    "Auth.form.error.code.provide": "유효하지 않은 코드입니다.",
    "Auth.form.error.confirmed": "이메일 인증이 필요합니다.",
    "Auth.form.error.email.invalid": "유효하지 않은 이메일입니다.",
    "Auth.form.error.email.provide": "이메일을 입력해 주세요.",
    "Auth.form.error.email.taken": "이미 사용 중인 이메일입니다.",
    "Auth.form.error.invalid": "입력한 내용이 유효하지 않습니다.",
    "Auth.form.error.params.provide": "유효하지 않은 파라미터입니다.",
    "Auth.form.error.password.format": "비밀번호에 `$` 문자를 세 번 이상 포함 할 수 없습니다.",
    "Auth.form.error.password.local": "비밀번호를 설정하지 않았습니다. 다른 방법으로 로그인 하세요.",
    "Auth.form.error.password.matching": "비밀번호가 일치하지 않습니다.",
    "Auth.form.error.password.provide": "비밀번호를 입력해 주세요.",
    "Auth.form.error.ratelimit": "요청이 너무 많습니다. 잠시 후 다시 시도해주세요.",
    "Auth.form.error.user.not-exist": "이메일이 없습니다.",
    "Auth.form.error.username.taken": "이미 사용 중인 아이디입니다.",
    "Auth.form.firstname.label": "이름",
    "Auth.form.firstname.placeholder": "e.g. Kai",
    "Auth.form.forgot-password.email.label": "메일 주소를 입력하세요.",
    "Auth.form.forgot-password.email.label.success": "메일을 보냈습니다.",
    "Auth.form.lastname.label": "성",
    "Auth.form.lastname.placeholder": "e.g. Doe",
    "Auth.form.password.hide-password": "비밀번호 숨기기",
    "Auth.form.password.hint": "암호는 대문자 1자, 소문자 1자, 숫자 1자를 포함한 8자 이상의 문자를 입력해주세요.",
    "Auth.form.password.show-password": "비밀번호 표시",
    "Auth.form.register.news.label": "새 기능과 향후 개선 사항에 대한 최신 정보를 계속 제공해주세요 (선택시 {terms}과 {policy}에 동의하는 걸로 간주됩니다).",
    "Auth.form.register.subtitle": "인증 정보는 관리자 패널에서 자신을 인증하는 데만 사용됩니다. 저장된 모든 데이터는 사용자의 데이터베이스에 저장됩니다.",
    "Auth.form.rememberMe.label": "로그인 상태 저장",
    "Auth.form.username.label": "아이디",
    "Auth.form.username.placeholder": "KaiDoe",
    "Auth.form.welcome.subtitle": "Strapi 계정으로 로그인하세요.",
    "Auth.form.welcome.title": "안녕하세요!",
    "Auth.link.forgot-password": "비밀번호 재설정",
    "Auth.link.ready": "로그인 하시겠습니까?",
    "Auth.link.signin": "로그인",
    "Auth.link.signin.account": "이미 계정이 있으신가요?",
    "Auth.login.sso.divider": "Or login with",
    "Auth.login.sso.loading": "프로바이더를 불러오는 중...",
    "Auth.login.sso.subtitle": "SSO를 통해 로그인합니다.",
    "Auth.privacy-policy-agreement.policy": "개인정보 보호정책",
    "Auth.privacy-policy-agreement.terms": "약관",
    "Content Manager": "콘텐츠 관리",
    "Content Type Builder": "콘텐츠 타입 빌더",
    Documentation: Documentation,
    Email: Email,
    "Files Upload": "파일 업로드",
    "HomePage.head.title": "홈페이지",
    "HomePage.roadmap": "로드맵 보기",
    "HomePage.welcome.congrats": "축하합니다!",
    "HomePage.welcome.congrats.content": "첫번째 관리자로 로그인하셨습니다. Strapi의 강력한 기능을 확인하시려면,",
    "HomePage.welcome.congrats.content.bold": "we recommend you to create your first Collection-Type.",
    "Media Library": "미디어 라이브러리",
    "New entry": "새 항목",
    Password: Password,
    Provider: Provider,
    ResetPasswordToken: ResetPasswordToken,
    Role: Role,
    "Roles & Permissions": "역할(Roles) & 권한(Permissions)",
    "Roles.ListPage.notification.delete-all-not-allowed": "Some roles could not be deleted since they are associated with users",
    "Roles.ListPage.notification.delete-not-allowed": "A role cannot be deleted if associated with users",
    "Roles.RoleRow.select-all": "Select {name} for bulk actions",
    "Roles.components.List.empty.withSearch": "There is no role corresponding to the search ({search})...",
    "Settings.PageTitle": "Settings - {name}",
    "Settings.apiTokens.addFirstToken": "첫 API Token을 만들어보세요.",
    "Settings.apiTokens.addNewToken": "새 API Token 만들기",
    "Settings.tokens.copy.editMessage": "보안상의 이유로 토큰은 한 번만 볼 수 있습니다.",
    "Settings.tokens.copy.editTitle": "이 토큰은 더 이상 액세스할 수 없습니다.",
    "Settings.tokens.copy.lastWarning": "이 토큰을 복사해두세요. 다시 볼 수 없습니다!",
    "Settings.apiTokens.create": "항목 추가",
    "Settings.apiTokens.description": "API 사용을 위해 생성된 토큰 목록입니다.",
    "Settings.apiTokens.emptyStateLayout": "아직 콘텐츠가 없습니다.",
    "Settings.tokens.notification.copied": "토큰이 클립보드에 복사되었습니다.",
    "Settings.apiTokens.title": "API 토큰",
    "Settings.tokens.types.full-access": "전체 액세스",
    "Settings.tokens.types.read-only": "읽기 전용",
    "Settings.application.description": "프로젝트 세부 정보",
    "Settings.application.edition-title": "현재 에디션",
    "Settings.application.link-pricing": "모든 가격 정책 보기",
    "Settings.application.link-upgrade": "어드민 패널 업그레이드",
    "Settings.application.node-version": "node version",
    "Settings.application.strapi-version": "strapi version",
    "Settings.application.title": "어플리케이션",
    "Settings.error": "에러",
    "Settings.global": "글로벌 설정",
    "Settings.permissions": "어드민 패널",
    "Settings.permissions.category": "{category} 사용 권한 설정",
    "Settings.permissions.category.plugins": "{category} 플러그인 사용 권한 설정",
    "Settings.permissions.conditions.anytime": "Anytime",
    "Settings.permissions.conditions.apply": "Apply",
    "Settings.permissions.conditions.can": "Can",
    "Settings.permissions.conditions.conditions": "Define conditions",
    "Settings.permissions.conditions.links": "Links",
    "Settings.permissions.conditions.no-actions": "You first need to select actions (create, read, update, ...) before defining conditions on them.",
    "Settings.permissions.conditions.none-selected": "Anytime",
    "Settings.permissions.conditions.or": "OR",
    "Settings.permissions.conditions.when": "When",
    "Settings.permissions.select-all-by-permission": "Select all {label} permissions",
    "Settings.permissions.select-by-permission": "Select {label} permission",
    "Settings.permissions.users.create": "새 사용자 추가",
    "Settings.permissions.users.email": "이메일",
    "Settings.permissions.users.firstname": "이름",
    "Settings.permissions.users.lastname": "성",
    "Settings.permissions.users.form.sso": "Connect with SSO",
    "Settings.permissions.users.form.sso.description": "When enabled (ON), users can login via SSO",
    "Settings.permissions.users.listview.header.subtitle": "{number, plural, =0 {# 명} one {# 명} other {# 명}}의 사용자를 찾았습니다.",
    "Settings.permissions.users.tabs.label": "Tabs Permissions",
    "Settings.profile.form.notify.data.loaded": "사용자 프로필 정보를 불러왔습니다.",
    "Settings.profile.form.section.experience.clear.select": "Clear the interface language selected",
    "Settings.profile.form.section.experience.interfaceLanguage": "인터페이스 언어",
    "Settings.profile.form.section.experience.interfaceLanguage.hint": "선택한 언어로 인터페이스의 언어가 변경됩니다.",
    "Settings.profile.form.section.experience.interfaceLanguageHelp": "선택하면 이 계정에서만 인터페이스 언어가 변경됩니다. 팀에서 다른 언어를 사용할 수 있도록 하려면 이 {documentation}를 참조해주세요.",
    "Settings.profile.form.section.experience.title": "사용자 경험",
    "Settings.profile.form.section.head.title": "사용자 프로필",
    "Settings.profile.form.section.profile.page.title": "Profile page",
    "Settings.roles.create.description": "역할에 부여된 권한을 정의합니다.",
    "Settings.roles.create.title": "Create a role",
    "Settings.roles.created": "Role created",
    "Settings.roles.edit.title": "역할 수정",
    "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# 명} one {# 명} other {# 명}}의 사용자가 이 역할을 가지고 있습니다.",
    "Settings.roles.form.created": "Created",
    "Settings.roles.form.description": "역할에 대한 이름과 설명",
    "Settings.roles.form.permission.property-label": "{label} permissions",
    "Settings.roles.form.permissions.attributesPermissions": "Fields permissions",
    "Settings.roles.form.permissions.create": "생성",
    "Settings.roles.form.permissions.delete": "삭제",
    "Settings.roles.form.permissions.publish": "발행",
    "Settings.roles.form.permissions.read": "조회",
    "Settings.roles.form.permissions.update": "수정",
    "Settings.roles.list.button.add": "새 역할 추가",
    "Settings.roles.list.description": "역할 목록",
    "Settings.roles.title.singular": "역할",
    "Settings.sso.description": "Single Sign-On 기능에 대한 설정을 구성합니다.",
    "Settings.sso.form.defaultRole.description": "새 사용자는 선택한 역할에 연결됩니다.",
    "Settings.sso.form.defaultRole.description-not-allowed": "어드민 역할을 보려면 권한이 필요합니다.",
    "Settings.sso.form.defaultRole.label": "기본 역할",
    "Settings.sso.form.registration.description": "계정이 없으면 SSO 로그인 시 새 사용자를 생성합니다.",
    "Settings.sso.form.registration.label": "자동 회원가입",
    "Settings.sso.title": "Single Sign-On",
    "Settings.webhooks.create": "웹훅 만들기",
    "Settings.webhooks.create.header": "새 헤더 만들기",
    "Settings.webhooks.created": "웹훅이 생성되었습니다.",
    "Settings.webhooks.event.publish-tooltip": "이 이벤트는 초안/발행 시스템이 활성화된 콘텐츠에 대해서만 존재합니다.",
    "Settings.webhooks.events.create": "생성",
    "Settings.webhooks.events.update": "수정",
    "Settings.webhooks.form.events": "이벤트",
    "Settings.webhooks.form.headers": "헤더",
    "Settings.webhooks.form.url": "Url",
    "Settings.webhooks.headers.remove": "Remove header row {number}",
    "Settings.webhooks.key": "Key",
    "Settings.webhooks.list.button.add": "새 웹훅 만들기",
    "Settings.webhooks.list.description": "POST 변경 알림을 가져옵니다.",
    "Settings.webhooks.list.empty.description": "첫 웹훅을 만들어보세요.",
    "Settings.webhooks.list.empty.link": "설명서 보기",
    "Settings.webhooks.list.empty.title": "아직 웹훅이 없습니다.",
    "Settings.webhooks.list.th.actions": "actions",
    "Settings.webhooks.list.th.status": "status",
    "Settings.webhooks.singular": "웹훅",
    "Settings.webhooks.title": "웹훅",
    "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# 개의 에셋이} other {# 개의 에셋이}} 선택됨",
    "Settings.webhooks.trigger": "Trigger",
    "Settings.webhooks.trigger.cancel": "Cancel trigger",
    "Settings.webhooks.trigger.pending": "Pending…",
    "Settings.webhooks.trigger.save": "Please save to trigger",
    "Settings.webhooks.trigger.success": "Success!",
    "Settings.webhooks.trigger.success.label": "Trigger succeeded",
    "Settings.webhooks.trigger.test": "Test-trigger",
    "Settings.webhooks.trigger.title": "Save before Trigger",
    "Settings.webhooks.value": "Value",
    Username: Username,
    Users: Users,
    "Users & Permissions": "사용자 & 권한(Permissions)",
    "Users.components.List.empty": "사용자가 없습니다.",
    "Users.components.List.empty.withFilters": "적용된 필터와 일치하는 사용자가 없습니다.",
    "Users.components.List.empty.withSearch": "({search}) 검색 결과와 일치하는 사용자가 없습니다.",
    "admin.pages.MarketPlacePage.head": "마켓플레이스 - 플러그인",
    "admin.pages.MarketPlacePage.submit.plugin.link": "플러그인 제출",
    "admin.pages.MarketPlacePage.subtitle": "Strapi에서 더 많은 것을 해보세요.",
    anErrorOccurred: anErrorOccurred,
    "app.component.CopyToClipboard.label": "클립보드 복사",
    "app.component.search.label": "Search for {target}",
    "app.component.table.duplicate": "Duplicate {target}",
    "app.component.table.edit": "Edit {target}",
    "app.component.table.select.one-entry": "Select {target}",
    "app.components.BlockLink.blog": "블로그",
    "app.components.BlockLink.blog.content": "Strapi와 생태계에 대한 최신 뉴스를 읽어보세요.",
    "app.components.BlockLink.code": "코드 샘플",
    "app.components.BlockLink.code.content": "실제 프로젝트를 테스트하여 학습합니다.",
    "app.components.BlockLink.documentation.content": "필수 개념, 가이드 및 지침을 살펴보세요.",
    "app.components.BlockLink.tutorial": "튜토리얼",
    "app.components.BlockLink.tutorial.content": "단계별 지침에 따라 Strapi를 사용하고 커스터마이징 해보세요.",
    "app.components.Button.cancel": "취소",
    "app.components.Button.confirm": "확인",
    "app.components.Button.reset": "리셋",
    "app.components.ComingSoonPage.comingSoon": "Coming soon",
    "app.components.ConfirmDialog.title": "확인",
    "app.components.DownloadInfo.download": "다운로드 중...",
    "app.components.DownloadInfo.text": "조금만 기다려 주세요.",
    "app.components.EmptyAttributes.title": "아직 생성된 필드가 없습니다.",
    "app.components.EmptyStateLayout.content-document": "아직 콘텐츠가 없습니다.",
    "app.components.EmptyStateLayout.content-permissions": "해당 콘텐츠에 액세스할 수 있는 권한이 없습니다",
    "app.components.HomePage.button.blog": "블로그 보기",
    "app.components.HomePage.community": "커뮤니티를 찾아보세요!",
    "app.components.HomePage.community.content": "다양한 채널에서 Strapi 팀원, 콘트리뷰터 및 개발자들과 토론해보세요.",
    "app.components.HomePage.create": "첫 콘텐츠 타입 만들기",
    "app.components.HomePage.roadmap": "로드맵 보기",
    "app.components.HomePage.welcome": "환영합니다 👋",
    "app.components.HomePage.welcome.again": "반갑습니다 👋",
    "app.components.HomePage.welcomeBlock.content": "축하드립니다! 첫 번째 관리자로 로그인하셨습니다. Strapi가 제공하는 강력한 기능을 알아보려면 첫 번째 콘텐츠 유형을 만들어보세요!",
    "app.components.HomePage.welcomeBlock.content.again": "Strapi에 대한 최신 뉴스를 자유롭게 읽어보세요. 저희는 여러분의 피드백을 바탕으로 제품을 개선하기 위해 최선을 다하고 있습니다.",
    "app.components.HomePage.welcomeBlock.content.issues": "이슈",
    "app.components.HomePage.welcomeBlock.content.raise": ", ",
    "app.components.ImgPreview.hint": "파일을 끌어 놓거나 {browse} 하세요.",
    "app.components.ImgPreview.hint.browse": "선택",
    "app.components.InputFile.newFile": "파일 추가",
    "app.components.InputFileDetails.open": "새 탭으로 열기",
    "app.components.InputFileDetails.originalName": "원래 파일 이름:",
    "app.components.InputFileDetails.remove": "파일 삭제",
    "app.components.InputFileDetails.size": "크기:",
    "app.components.InstallPluginPage.Download.description": "플러그인을 다운로드하여 설치하는 데 몇 초 정도 걸릴 수 있습니다.",
    "app.components.InstallPluginPage.Download.title": "다운로드 중...",
    "app.components.InstallPluginPage.description": "빠르고 간단하게 기능을 확장해 보세요.",
    "app.components.LeftMenu.collapse": "Collapse the navbar",
    "app.components.LeftMenu.expand": "Expand the navbar",
    "app.components.LeftMenu.logout": "로그아웃",
    "app.components.LeftMenu.navbrand.title": "Strapi 대시보드",
    "app.components.LeftMenu.navbrand.workplace": "작업 공간",
    "app.components.LeftMenu.trialCountdown": "테스트 기간이 {date}에 종료됩니다.",
    "app.components.LeftMenuFooter.help": "도움말",
    "app.components.LeftMenuFooter.poweredBy": "Powered by ",
    "app.components.LeftMenuLinkContainer.collectionTypes": "콜렉션 타입",
    "app.components.LeftMenuLinkContainer.configuration": "환경설정",
    "app.components.LeftMenuLinkContainer.general": "일반",
    "app.components.LeftMenuLinkContainer.noPluginsInstalled": "설치된 플러그인이 없습니다.",
    "app.components.LeftMenuLinkContainer.plugins": "플러그인",
    "app.components.LeftMenuLinkContainer.singleTypes": "싱글 타입",
    "app.components.ListPluginsPage.deletePlugin.description": "플러그인을 제거하는 데 몇 초 정도 걸릴 수 있습니다.",
    "app.components.ListPluginsPage.deletePlugin.title": "제거하는 중",
    "app.components.ListPluginsPage.description": "이 프로젝트에 설치된 플러그인 목록입니다.",
    "app.components.ListPluginsPage.head.title": "플러그인 목록",
    "app.components.Logout.logout": "로그아웃",
    "app.components.Logout.profile": "프로필",
    "app.components.MarketplaceBanner": "Discover plugins built by the community, and many more awesome things to kickstart your project, on Strapi Awesome.",
    "app.components.MarketplaceBanner.image.alt": "a strapi rocket logo",
    "app.components.MarketplaceBanner.link": "지금 확인해보기",
    "app.components.NotFoundPage.back": "홈으로 돌아가기",
    "app.components.NotFoundPage.description": "찾을 수 없는 페이지입니다.",
    "app.components.Official": "공식",
    "app.components.Onboarding.help.button": "도움말",
    "app.components.Onboarding.label.completed": "% 완료",
    "app.components.Onboarding.title": "동영상 시청하기",
    "app.components.PluginCard.Button.label.download": "다운로드",
    "app.components.PluginCard.Button.label.install": "설치됨",
    "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "autoReload 기능을 사용하지 않도록 설정해야 합니다. `yarn develop`로 앱을 시작하세요.",
    "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "알겠습니다!",
    "app.components.PluginCard.PopUpWarning.install.impossible.environment": "보안상의 이유로 개발 환경에서만 플러그인을 다운로드할 수 있습니다.",
    "app.components.PluginCard.PopUpWarning.install.impossible.title": "다운로드 불가",
    "app.components.PluginCard.compatible": "이 애플리케이션에 호환됩니다.",
    "app.components.PluginCard.compatibleCommunity": "션뮤니티에 호환됩니다.",
    "app.components.PluginCard.more-details": "[더보기]",
    "app.components.ToggleCheckbox.off-label": "False",
    "app.components.ToggleCheckbox.on-label": "True",
    "app.components.Users.MagicLink.connect": "이 링크를 가입할 사용자에게 보내주세요.",
    "app.components.Users.MagicLink.connect.sso": "이 링크를 가입할 사용자에게 보내주세요. SSO 프로바이더를 통해 처음 로그인할 수 있습니다.",
    "app.components.Users.ModalCreateBody.block-title.details": "상세 정보",
    "app.components.Users.ModalCreateBody.block-title.roles": "사용자 역할",
    "app.components.Users.ModalCreateBody.block-title.roles.description": "사용자는 하나 이상의 역할을 가질 수 있습니다.",
    "app.components.Users.SortPicker.button-label": "정렬 기준",
    "app.components.Users.SortPicker.sortby.email_asc": "이메일 (A to Z)",
    "app.components.Users.SortPicker.sortby.email_desc": "이메일 (Z to A)",
    "app.components.Users.SortPicker.sortby.firstname_asc": "이름 (A to Z)",
    "app.components.Users.SortPicker.sortby.firstname_desc": "이름 (Z to A)",
    "app.components.Users.SortPicker.sortby.lastname_asc": "성 (A to Z)",
    "app.components.Users.SortPicker.sortby.lastname_desc": "성 (Z to A)",
    "app.components.Users.SortPicker.sortby.username_asc": "Username (A to Z)",
    "app.components.Users.SortPicker.sortby.username_desc": "Username (Z to A)",
    "app.components.listPlugins.button": "새로운 플러그인 추가하기",
    "app.components.listPlugins.title.none": "설치된 플러그인이 없습니다.",
    "app.components.listPluginsPage.deletePlugin.error": "플러그인을 제거하는데 에러가 발생했습니다.",
    "app.containers.App.notification.error.init": "API 요청 중에 에러가 발생했습니다.",
    "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "링크를 받지 못했다면 관리자에게 문의해주세요.",
    "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "비밀번호 복구 링크를 받는 데 몇 분 정도 걸릴 수 있습니다.",
    "app.containers.AuthPage.ForgotPasswordSuccess.title": "Email sent",
    "app.containers.Users.EditPage.form.active.label": "활성",
    "app.containers.Users.EditPage.header.label": "{name} 수정",
    "app.containers.Users.EditPage.header.label-loading": "사용자 수정",
    "app.containers.Users.EditPage.roles-bloc-title": "Attributed roles",
    "app.containers.Users.ModalForm.footer.button-success": "사용자 생성",
    "app.links.configure-view": "보기 설정",
    "app.static.links.cheatsheet": "CheatSheet",
    "app.utils.SelectOption.defaultMessage": " ",
    "app.utils.add-filter": "필터 추가",
    "app.utils.close-label": "닫기",
    "app.utils.defaultMessage": " ",
    "app.utils.duplicate": "복사",
    "app.utils.edit": "수정",
    "app.utils.errors.file-too-big.message": "파일이 너무 큽니다",
    "app.utils.filter-value": "필터 값",
    "app.utils.filters": "필터",
    "app.utils.notify.data-loaded": "{target}을 불러왔습니다.",
    "app.utils.placeholder.defaultMessage": " ",
    "app.utils.publish": "발행",
    "app.utils.select-all": "전체 선택",
    "app.utils.select-field": "필드 선택",
    "app.utils.select-filter": "필터 선택",
    "app.utils.unpublish": "발행 취소",
    clearLabel: clearLabel,
    "coming.soon": "주",
    "component.Input.error.validation.integer": "값은 정수여야 합니다.",
    "components.AutoReloadBlocker.description": "다음 명령어 중 하나를 사용하여 Strapi를 실행합니다:",
    "components.AutoReloadBlocker.header": "이 플러그인은 리로드 기능이 필요합니다.",
    "components.ErrorBoundary.title": "에러가 발생했습니다.",
    "components.FilterOptions.FILTER_TYPES.$contains": "포함",
    "components.FilterOptions.FILTER_TYPES.$containsi": "포함(대소문자 구분 안 함)",
    "components.FilterOptions.FILTER_TYPES.$endsWith": "로 끝나다",
    "components.FilterOptions.FILTER_TYPES.$endsWithi": "다음으로 끝남(case insensitive)",
    "components.FilterOptions.FILTER_TYPES.$eq": "같음",
    "components.FilterOptions.FILTER_TYPES.$eqi": "같음(대소문자 구분 안 함)",
    "components.FilterOptions.FILTER_TYPES.$gt": "is greater than",
    "components.FilterOptions.FILTER_TYPES.$gte": "is greater than or equal to",
    "components.FilterOptions.FILTER_TYPES.$lt": "is lower than",
    "components.FilterOptions.FILTER_TYPES.$lte": "is lower than or equal to",
    "components.FilterOptions.FILTER_TYPES.$ne": "같지 않음",
    "components.FilterOptions.FILTER_TYPES.$nei": "같지 않음(case insensitive)",
    "components.FilterOptions.FILTER_TYPES.$notContains": "포함되어 있지 않다",
    "components.FilterOptions.FILTER_TYPES.$notContainsi": "포함하지 않음(대소문자 구분 안 함)",
    "components.FilterOptions.FILTER_TYPES.$notNull": "is not null",
    "components.FilterOptions.FILTER_TYPES.$null": "is null",
    "components.FilterOptions.FILTER_TYPES.$startsWith": "로 시작",
    "components.FilterOptions.FILTER_TYPES.$startsWithi": "로 시작(대소문자 구분 안 함)",
    "components.Input.error.attribute.key.taken": "이미 사용중인 키입니다.",
    "components.Input.error.attribute.sameKeyAndName": "같은 값을 사용할 수 없습니다.",
    "components.Input.error.attribute.taken": "이미 사용중인 이름입니다.",
    "components.Input.error.contain.lowercase": "비밀번호는 소문자 하나를 반드시 포함해야 합니다.",
    "components.Input.error.contain.number": "비밀번호는 숫자 하나를 반드시 포함해야 합니다.",
    "components.Input.error.contain.uppercase": "비밀번호는 대문자 하나를 반드시 포함해야 합니다.",
    "components.Input.error.contentTypeName.taken": "이미 사용중인 이름입니다.",
    "components.Input.error.custom-error": "{errorMessage} ",
    "components.Input.error.password.noMatch": "비밀번호가 일치하지 않습니다.",
    "components.Input.error.validation.email": "올바른 이메일 주소가 아닙니다.",
    "components.Input.error.validation.json": "JSON 형식이 아닙니다.",
    "components.Input.error.validation.max": "입력한 내용이 너무 큽니다 {max}.",
    "components.Input.error.validation.maxLength": "입력한 내용이 너무 깁니다 {max}.",
    "components.Input.error.validation.min": "입력한 내용이 너무 작습니다 {min}.",
    "components.Input.error.validation.minLength": "입력한 내용이 너무 짧습니다 {min}.",
    "components.Input.error.validation.minSupMax": "이 보다 더 클 수 없습니다.",
    "components.Input.error.validation.regex": "입력한 내용이 형식에 맞지 않습니다.",
    "components.Input.error.validation.required": "내용을 입력해 주세요.",
    "components.Input.error.validation.unique": "이 값은 이미 사용중입니다.",
    "components.InputSelect.option.placeholder": "선택해 주세요.",
    "components.ListRow.empty": "데이터가 없습니다.",
    "components.NotAllowedInput.text": "이 필드를 볼 수 있는 권한이 없습니다.",
    "components.OverlayBlocker.description": "이 기능은 서버를 재시작해야 합니다. 서버가 시작될 때까지 잠시만 기다려주세요.",
    "components.OverlayBlocker.description.serverError": "서버가 재시작되지 않았습니다. 터미널에서 로그를 확인하십시오.",
    "components.OverlayBlocker.title": "재시작하고 있습니다...",
    "components.OverlayBlocker.title.serverError": "재시작 시간이 예상보다 오래 걸리고 있습니다.",
    "components.PageFooter.select": "항목 수 / 페이지",
    "components.ProductionBlocker.description": "이 플러그인은 안전을 위해 다른 환경에서 사용할 수 없습니다.",
    "components.ProductionBlocker.header": "이 플러그인은 개발 모드에서만 사용할 수 있습니다.",
    "components.Search.placeholder": "검색...",
    "components.TableHeader.sort": "Sort on {label}",
    "components.Wysiwyg.ToggleMode.markdown-mode": "마크다운 모드",
    "components.Wysiwyg.ToggleMode.preview-mode": "미리보기 모드",
    "components.Wysiwyg.collapse": "병합",
    "components.Wysiwyg.selectOptions.H1": "제목 H1",
    "components.Wysiwyg.selectOptions.H2": "제목 H2",
    "components.Wysiwyg.selectOptions.H3": "제목 H3",
    "components.Wysiwyg.selectOptions.H4": "제목 H4",
    "components.Wysiwyg.selectOptions.H5": "제목 H5",
    "components.Wysiwyg.selectOptions.H6": "제목 H6",
    "components.Wysiwyg.selectOptions.title": "제목",
    "components.WysiwygBottomControls.charactersIndicators": "문자 표시기",
    "components.WysiwygBottomControls.fullscreen": "전체화면",
    "components.WysiwygBottomControls.uploadFiles": "파일을 끌어 놓으세요. 혹은 클립보드에서 붙혀넣거나 {browse} 하세요.",
    "components.WysiwygBottomControls.uploadFiles.browse": "선택",
    "components.pagination.go-to": "{page} 페이지로",
    "components.pagination.go-to-next": "다음 페이지",
    "components.pagination.go-to-previous": "이전 페이지",
    "components.pagination.remaining-links": "And {number} other links",
    "components.popUpWarning.button.cancel": "아니요, 취소합니다.",
    "components.popUpWarning.button.confirm": "네, 확인했습니다.",
    "components.popUpWarning.message": "삭제하시겠습니까?",
    "components.popUpWarning.title": "확인",
    "form.button.done": "확인",
    "global.prompt.unsaved": "이 페이지를 떠나시겠습니까? 모든 변경 사항이 없어집니다.",
    "notification.contentType.relations.conflict": "콘텐츠 타입에 충돌하는 릴레이션(conflict relation)이 있습니다.",
    "notification.default.title": "정보 알림:",
    "notification.error": "에러가 발생했습니다.",
    "notification.error.layout": "레이아웃을 가져올 수 없습니다.",
    "notification.form.error.fields": "잘못 입력된 필드가 존재합니다.",
    "notification.form.success.fields": "변경 사항이 저장되었습니다.",
    "notification.link-copied": "링크가 클립보드에 복사되었습니다.",
    "notification.permission.not-allowed-read": "이 문서를 볼 수 있는 권한이 없습니다.",
    "notification.success.delete": "항목이 삭제되었습니다.",
    "notification.success.saved": "저장되었습니다.",
    "notification.success.title": "성공 알림:",
    "notification.version.update.message": "Strapi 새 버전이 출시되었습니다!",
    "notification.warning.title": "경고 알림:",
    or: or,
    "request.error.model.unknown": "모델이 없습니다.",
    skipToContent: skipToContent,
    submit: submit
};

export { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, ko as default, or, skipToContent, submit };
//# sourceMappingURL=ko.json.mjs.map
