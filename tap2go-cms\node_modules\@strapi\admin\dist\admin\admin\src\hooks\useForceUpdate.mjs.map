{"version": 3, "file": "useForceUpdate.mjs", "sources": ["../../../../../admin/src/hooks/useForceUpdate.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useIsMounted } from './useIsMounted';\n\n/**\n * @internal\n * @description Return a function that re-renders this component, if still mounted\n * @warning DO NOT USE EXCEPT SPECIAL CASES.\n */\nconst useForceUpdate = () => {\n  const [tick, update] = React.useState<number>();\n  const isMounted = useIsMounted();\n\n  const forceUpdate = React.useCallback(() => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  }, [isMounted, update]);\n\n  return [tick, forceUpdate] as const;\n};\n\nexport { useForceUpdate };\n"], "names": ["useForceUpdate", "tick", "update", "React", "useState", "isMounted", "useIsMounted", "forceUpdate", "useCallback", "current", "Math", "random"], "mappings": ";;;AAIA;;;;AAIC,UACKA,cAAiB,GAAA,IAAA;AACrB,IAAA,MAAM,CAACC,IAAAA,EAAMC,MAAO,CAAA,GAAGC,MAAMC,QAAQ,EAAA;AACrC,IAAA,MAAMC,SAAYC,GAAAA,YAAAA,EAAAA;IAElB,MAAMC,WAAAA,GAAcJ,KAAMK,CAAAA,WAAW,CAAC,IAAA;QACpC,IAAIH,SAAAA,CAAUI,OAAO,EAAE;AACrBP,YAAAA,MAAAA,CAAOQ,KAAKC,MAAM,EAAA,CAAA;AACpB;KACC,EAAA;AAACN,QAAAA,SAAAA;AAAWH,QAAAA;AAAO,KAAA,CAAA;IAEtB,OAAO;AAACD,QAAAA,IAAAA;AAAMM,QAAAA;AAAY,KAAA;AAC5B;;;;"}