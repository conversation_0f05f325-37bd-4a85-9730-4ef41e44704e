{"version": 3, "file": "updateConditionsToFalse.mjs", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Roles/utils/updateConditionsToFalse.ts"], "sourcesContent": ["import has from 'lodash/has';\nimport omit from 'lodash/omit';\n\nimport { isObject } from '../../../../../utils/objects';\n\nimport { createArrayOfValues } from './createArrayOfValues';\n/**\n * Changes all the conditions leaf when the properties are all falsy\n */\nconst updateConditionsToFalse = (obj: object): object => {\n  return Object.keys(obj).reduce((acc, current) => {\n    // @ts-expect-error – TODO: type better\n    const currentValue = obj[current];\n\n    if (isObject(currentValue) && !has(currentValue, 'conditions')) {\n      return { ...acc, [current]: updateConditionsToFalse(currentValue) };\n    }\n\n    if (isObject(currentValue) && has(currentValue, 'conditions')) {\n      const isActionEnabled = createArrayOfValues(omit(currentValue, 'conditions')).some(\n        (val) => val\n      );\n\n      if (!isActionEnabled) {\n        // @ts-expect-error – TODO: type better\n        const updatedConditions = Object.keys(currentValue.conditions).reduce((acc1, current) => {\n          // @ts-expect-error – TODO: type better\n          acc1[current] = false;\n\n          return acc1;\n        }, {});\n\n        return { ...acc, [current]: { ...currentValue, conditions: updatedConditions } };\n      }\n    }\n\n    // @ts-expect-error – TODO: type better\n    acc[current] = currentValue;\n\n    return acc;\n  }, {});\n};\n\nexport { updateConditionsToFalse };\n"], "names": ["updateConditionsToFalse", "obj", "Object", "keys", "reduce", "acc", "current", "currentValue", "isObject", "has", "isActionEnabled", "createArrayOfValues", "omit", "some", "val", "updatedConditions", "conditions", "acc1"], "mappings": ";;;;;AAMA;;IAGA,MAAMA,0BAA0B,CAACC,GAAAA,GAAAA;AAC/B,IAAA,OAAOC,OAAOC,IAAI,CAACF,KAAKG,MAAM,CAAC,CAACC,GAAKC,EAAAA,OAAAA,GAAAA;;QAEnC,MAAMC,YAAAA,GAAeN,GAAG,CAACK,OAAQ,CAAA;AAEjC,QAAA,IAAIE,QAASD,CAAAA,YAAAA,CAAAA,IAAiB,CAACE,GAAAA,CAAIF,cAAc,YAAe,CAAA,EAAA;YAC9D,OAAO;AAAE,gBAAA,GAAGF,GAAG;gBAAE,CAACC,OAAAA,GAAUN,uBAAwBO,CAAAA,YAAAA;AAAc,aAAA;AACpE;AAEA,QAAA,IAAIC,QAASD,CAAAA,YAAAA,CAAAA,IAAiBE,GAAIF,CAAAA,YAAAA,EAAc,YAAe,CAAA,EAAA;YAC7D,MAAMG,eAAAA,GAAkBC,oBAAoBC,IAAKL,CAAAA,YAAAA,EAAc,eAAeM,IAAI,CAChF,CAACC,GAAQA,GAAAA,GAAAA,CAAAA;AAGX,YAAA,IAAI,CAACJ,eAAiB,EAAA;;gBAEpB,MAAMK,iBAAAA,GAAoBb,MAAOC,CAAAA,IAAI,CAACI,YAAAA,CAAaS,UAAU,CAAEZ,CAAAA,MAAM,CAAC,CAACa,IAAMX,EAAAA,OAAAA,GAAAA;;oBAE3EW,IAAI,CAACX,QAAQ,GAAG,KAAA;oBAEhB,OAAOW,IAAAA;AACT,iBAAA,EAAG,EAAC,CAAA;gBAEJ,OAAO;AAAE,oBAAA,GAAGZ,GAAG;AAAE,oBAAA,CAACC,UAAU;AAAE,wBAAA,GAAGC,YAAY;wBAAES,UAAYD,EAAAA;AAAkB;AAAE,iBAAA;AACjF;AACF;;QAGAV,GAAG,CAACC,QAAQ,GAAGC,YAAAA;QAEf,OAAOF,GAAAA;AACT,KAAA,EAAG,EAAC,CAAA;AACN;;;;"}