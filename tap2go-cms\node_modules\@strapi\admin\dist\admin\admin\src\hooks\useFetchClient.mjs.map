{"version": 3, "file": "useFetchClient.mjs", "sources": ["../../../../../admin/src/hooks/useFetchClient.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { getFetchClient } from '../utils/getFetchClient';\n\n/**\n * @public\n * @description This is an abstraction around the native fetch exposed by a hook. It provides a simple interface to handle API calls\n * to the Strapi backend.\n * It handles request cancellations inside the hook with an {@link https://developer.mozilla.org/en-US/docs/Web/API/AbortController} AbortController.\n * This is typically triggered when the component is unmounted so all the requests that it is currently making are aborted.\n * The expected URL style includes either a protocol (such as HTTP or HTTPS) or a relative URL. The URLs with domain and path but not protocol are not allowed (ex: `www.example.com`).\n * @example\n * ```tsx\n * import * as React from 'react';\n * import { useFetchClient } from '@strapi/admin/admin';\n *\n * const MyComponent = () => {\n *   const [items, setItems] = React.useState([]);\n *   const { get } = useFetchClient();\n *   const requestURL = \"/some-endpoint\";\n *\n *   const handleGetData = async () => {\n *     const { data } = await get(requestURL);\n *     setItems(data.items);\n *   };\n *\n *   return (\n *    <div>\n *      <div>\n *       {\n *         items && items.map(item => <h2 key={item.uuid}>{item.name}</h2>))\n *       }\n *     </div>\n *    </div>\n *   );\n * };\n * ```\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/AbortController} AbortController.\n */\nconst useFetchClient = () => {\n  const controller = React.useRef<AbortController | null>(null);\n\n  if (controller.current === null) {\n    controller.current = new AbortController();\n  }\n\n  React.useEffect(() => {\n    return () => {\n      controller.current!.abort();\n    };\n  }, []);\n\n  return React.useMemo(\n    () =>\n      getFetchClient({\n        signal: controller.current!.signal,\n      }),\n    []\n  );\n};\n\nexport { useFetchClient };\n"], "names": ["useFetchClient", "controller", "React", "useRef", "current", "AbortController", "useEffect", "abort", "useMemo", "getFetchClient", "signal"], "mappings": ";;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCC,UACKA,cAAiB,GAAA,IAAA;IACrB,MAAMC,UAAAA,GAAaC,KAAMC,CAAAA,MAAM,CAAyB,IAAA,CAAA;IAExD,IAAIF,UAAAA,CAAWG,OAAO,KAAK,IAAM,EAAA;QAC/BH,UAAWG,CAAAA,OAAO,GAAG,IAAIC,eAAAA,EAAAA;AAC3B;AAEAH,IAAAA,KAAAA,CAAMI,SAAS,CAAC,IAAA;QACd,OAAO,IAAA;YACLL,UAAWG,CAAAA,OAAO,CAAEG,KAAK,EAAA;AAC3B,SAAA;AACF,KAAA,EAAG,EAAE,CAAA;AAEL,IAAA,OAAOL,KAAMM,CAAAA,OAAO,CAClB,IACEC,cAAe,CAAA;YACbC,MAAQT,EAAAA,UAAAA,CAAWG,OAAO,CAAEM;AAC9B,SAAA,CAAA,EACF,EAAE,CAAA;AAEN;;;;"}