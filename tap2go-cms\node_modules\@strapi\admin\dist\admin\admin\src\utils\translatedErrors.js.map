{"version": 3, "file": "translatedErrors.js", "sources": ["../../../../../admin/src/utils/translatedErrors.ts"], "sourcesContent": ["/**\n * Object containing error messages for input validations.\n * @property {Object} email - Error message for invalid email.\n * @property {string} email.id - Identifier for the error message.\n * @property {string} email.defaultMessage - Default error message for invalid email.\n * @property {Object} json - Error message for invalid JSON format.\n * @property {string} json.id - Identifier for the error message.\n * @property {string} json.defaultMessage - Default error message for invalid JSON format.\n * @property {Object} lowercase - Error message for non-lowercase string.\n * @property {string} lowercase.id - Identifier for the error message.\n * @property {string} lowercase.defaultMessage - Default error message for non-lowercase string.\n * @property {Object} max - Error message for value exceeding maximum.\n * @property {string} max.id - Identifier for the error message.\n * @property {string} max.defaultMessage - Default error message for value exceeding maximum.\n * @property {Object} maxLength - Error message for string length exceeding maximum.\n * @property {string} maxLength.id - Identifier for the error message.\n * @property {string} maxLength.defaultMessage - Default error message for string length exceeding maximum.\n * @property {Object} min - Error message for value less than minimum.\n * @property {string} min.id - Identifier for the error message.\n * @property {string} min.defaultMessage - Default error message for value less than minimum.\n * @property {Object} minLength - Error message for string length less than minimum.\n * @property {string} minLength.id - Identifier for the error message.\n * @property {string} minLength.defaultMessage - Default error message for string length less than minimum.\n * @property {Object} regex - Error message for value not matching regex pattern.\n * @property {string} regex.id - Identifier for the error message.\n * @property {string} regex.defaultMessage - Default error message for value not matching regex pattern.\n * @property {Object} required - Error message for required value.\n * @property {string} required.id - Identifier for the error message.\n * @property {string} required.defaultMessage - Default error message for required value.\n * @property {Object} string - Error message for non-string value.\n * @property {string} string.id - Identifier for the error message.\n * @property {string} string.defaultMessage - Default error message for non-string value.\n * @property {Object} unique - Error message for non-unique value.\n * @property {string} unique.id - Identifier for the error message.\n * @property {string} unique.defaultMessage - Default error message for non-unique value.\n * @property {Object} integer - Error message for non-integer value.\n * @property {string} integer.id - Identifier for the error message.\n * @property {string} integer.defaultMessage - Default error message for non-integer value.\n */\n\nconst errorsTrads = {\n  email: {\n    id: 'components.Input.error.validation.email',\n    defaultMessage: 'This is not a valid email.',\n  },\n  json: {\n    id: 'components.Input.error.validation.json',\n    defaultMessage: \"This doesn't match the JSON format\",\n  },\n  lowercase: {\n    id: 'components.Input.error.validation.lowercase',\n    defaultMessage: 'The value must be a lowercase string',\n  },\n  max: {\n    id: 'components.Input.error.validation.max',\n    defaultMessage: 'The value is too high (max: {max}).',\n  },\n  maxLength: {\n    id: 'components.Input.error.validation.maxLength',\n    defaultMessage: 'The value is too long (max: {max}).',\n  },\n  min: {\n    id: 'components.Input.error.validation.min',\n    defaultMessage: 'The value is too low (min: {min}).',\n  },\n  minLength: {\n    id: 'components.Input.error.validation.minLength',\n    defaultMessage: 'The value is too short (min: {min}).',\n  },\n  regex: {\n    id: 'components.Input.error.validation.regex',\n    defaultMessage: 'The value does not match the regex.',\n  },\n  required: {\n    id: 'components.Input.error.validation.required',\n    defaultMessage: 'This value is required.',\n  },\n  string: {\n    id: 'components.Input.error.validation.string',\n    defaultMessage: 'This is not a valid string.',\n  },\n  unique: {\n    id: 'components.Input.error.validation.unique',\n    defaultMessage: 'This value is already used.',\n  },\n  integer: {\n    id: 'component.Input.error.validation.integer',\n    defaultMessage: 'The value must be an integer',\n  },\n} as const;\n\nexport { errorsTrads as translatedErrors };\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "email", "id", "defaultMessage", "json", "lowercase", "max", "max<PERSON><PERSON><PERSON>", "min", "<PERSON><PERSON><PERSON><PERSON>", "regex", "required", "string", "unique", "integer"], "mappings": ";;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCC,UAEKA,WAAc,GAAA;IAClBC,KAAO,EAAA;QACLC,EAAI,EAAA,yCAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAC,IAAM,EAAA;QACJF,EAAI,EAAA,wCAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAE,SAAW,EAAA;QACTH,EAAI,EAAA,6CAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAG,GAAK,EAAA;QACHJ,EAAI,EAAA,uCAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAI,SAAW,EAAA;QACTL,EAAI,EAAA,6CAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAK,GAAK,EAAA;QACHN,EAAI,EAAA,uCAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAM,SAAW,EAAA;QACTP,EAAI,EAAA,6CAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAO,KAAO,EAAA;QACLR,EAAI,EAAA,yCAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAQ,QAAU,EAAA;QACRT,EAAI,EAAA,4CAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAS,MAAQ,EAAA;QACNV,EAAI,EAAA,0CAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAU,MAAQ,EAAA;QACNX,EAAI,EAAA,0CAAA;QACJC,cAAgB,EAAA;AAClB,KAAA;IACAW,OAAS,EAAA;QACPZ,EAAI,EAAA,0CAAA;QACJC,cAAgB,EAAA;AAClB;AACF;;;;"}