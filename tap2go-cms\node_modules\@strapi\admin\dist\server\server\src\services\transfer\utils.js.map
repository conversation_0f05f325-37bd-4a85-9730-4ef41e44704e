{"version": 3, "file": "utils.js", "sources": ["../../../../../../server/src/services/transfer/utils.ts"], "sourcesContent": ["import { env } from '@strapi/utils';\nimport { getService } from '../../utils';\n\n/**\n * A valid transfer token salt must be a non-empty string defined in the Strapi config\n */\nconst hasValidTokenSalt = (): boolean => {\n  const salt = strapi.config.get('admin.transfer.token.salt', null) as string | null;\n\n  return typeof salt === 'string' && salt.length > 0;\n};\n\n/**\n * Checks whether data transfer features are enabled\n */\nconst isRemoteTransferEnabled = (): boolean => {\n  const { utils } = getService('transfer');\n\n  // TODO v6: Remove this warning\n  if (env.bool('STRAPI_DISABLE_REMOTE_DATA_TRANSFER') !== undefined) {\n    strapi.log.warn(\n      'STRAPI_DISABLE_REMOTE_DATA_TRANSFER is no longer supported. Instead, set transfer.remote.enabled to false in your server configuration'\n    );\n  }\n\n  return utils.hasValidTokenSalt() && strapi.config.get('server.transfer.remote.enabled');\n};\n\nexport { isRemoteTransferEnabled, hasValidTokenSalt };\n"], "names": ["hasValidTokenSalt", "salt", "strapi", "config", "get", "length", "isRemoteTransferEnabled", "utils", "getService", "env", "bool", "undefined", "log", "warn"], "mappings": ";;;;;AAGA;;AAEC,UACKA,iBAAoB,GAAA,IAAA;AACxB,IAAA,MAAMC,OAAOC,MAAOC,CAAAA,MAAM,CAACC,GAAG,CAAC,2BAA6B,EAAA,IAAA,CAAA;AAE5D,IAAA,OAAO,OAAOH,IAAAA,KAAS,QAAYA,IAAAA,IAAAA,CAAKI,MAAM,GAAG,CAAA;AACnD;AAEA;;AAEC,UACKC,uBAA0B,GAAA,IAAA;AAC9B,IAAA,MAAM,SAAEC,OAAK,EAAE,GAAGC,gBAAW,CAAA,UAAA,CAAA;;AAG7B,IAAA,IAAIC,SAAIC,CAAAA,IAAI,CAAC,qCAAA,CAAA,KAA2CC,SAAW,EAAA;QACjET,MAAOU,CAAAA,GAAG,CAACC,IAAI,CACb,wIAAA,CAAA;AAEJ;AAEA,IAAA,OAAON,QAAMP,iBAAiB,EAAA,IAAME,OAAOC,MAAM,CAACC,GAAG,CAAC,gCAAA,CAAA;AACxD;;;;;"}