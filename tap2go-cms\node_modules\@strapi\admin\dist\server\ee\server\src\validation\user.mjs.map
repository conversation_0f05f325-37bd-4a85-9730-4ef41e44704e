{"version": 3, "file": "user.mjs", "sources": ["../../../../../../ee/server/src/validation/user.ts"], "sourcesContent": ["import { yup, validateYupSchema } from '@strapi/utils';\nimport { schemas } from '../../../../server/src/validation/user';\n\nconst ssoUserCreationInputExtension = yup\n  .object()\n  .shape({\n    useSSORegistration: yup.boolean(),\n  })\n  .noUnknown();\n\nexport const validateUserCreationInput = (data: any) => {\n  let schema = schemas.userCreationSchema;\n\n  if (strapi.ee.features.isEnabled('sso')) {\n    schema = schema.concat(ssoUserCreationInputExtension);\n  }\n\n  return validateYupSchema(schema)(data);\n};\n\nexport default {\n  validateUserCreationInput,\n};\n"], "names": ["ssoUserCreationInputExtension", "yup", "object", "shape", "useSSORegistration", "boolean", "noUnknown", "validateUserCreationInput", "data", "schema", "schemas", "userCreationSchema", "strapi", "ee", "features", "isEnabled", "concat", "validateYupSchema"], "mappings": ";;;AAGA,MAAMA,6BAAgCC,GAAAA,GAAAA,CACnCC,MAAM,EAAA,CACNC,KAAK,CAAC;AACLC,IAAAA,kBAAAA,EAAoBH,IAAII,OAAO;AACjC,CAAA,CAAA,CACCC,SAAS,EAAA;AAEL,MAAMC,4BAA4B,CAACC,IAAAA,GAAAA;IACxC,IAAIC,MAAAA,GAASC,QAAQC,kBAAkB;AAEvC,IAAA,IAAIC,OAAOC,EAAE,CAACC,QAAQ,CAACC,SAAS,CAAC,KAAQ,CAAA,EAAA;QACvCN,MAASA,GAAAA,MAAAA,CAAOO,MAAM,CAAChB,6BAAAA,CAAAA;AACzB;AAEA,IAAA,OAAOiB,kBAAkBR,MAAQD,CAAAA,CAAAA,IAAAA,CAAAA;AACnC;;;;"}