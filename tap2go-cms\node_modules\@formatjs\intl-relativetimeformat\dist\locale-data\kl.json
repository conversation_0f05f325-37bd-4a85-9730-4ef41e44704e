{"data": {"kl": {"nu": ["latn"], "year": {"0": "this year", "1": "next year", "future": {"one": "om {0} ukioq", "other": "om {0} ukioq"}, "past": {"one": "for {0} ukioq siden", "other": "for {0} ukioq siden"}, "-1": "last year"}, "year-short": {"0": "this year", "1": "next year", "future": {"one": "om {0} ukioq", "other": "om {0} ukioq"}, "past": {"one": "for {0} ukioq siden", "other": "for {0} ukioq siden"}, "-1": "last year"}, "year-narrow": {"0": "this year", "1": "next year", "future": {"one": "om {0} ukioq", "other": "om {0} ukioq"}, "past": {"one": "for {0} ukioq siden", "other": "for {0} ukioq siden"}, "-1": "last year"}, "quarter": {"0": "this quarter", "1": "next quarter", "future": {"other": "+{0} Q"}, "past": {"other": "-{0} Q"}, "-1": "last quarter"}, "quarter-short": {"0": "this quarter", "1": "next quarter", "future": {"other": "+{0} Q"}, "past": {"other": "-{0} Q"}, "-1": "last quarter"}, "quarter-narrow": {"0": "this quarter", "1": "next quarter", "future": {"other": "+{0} Q"}, "past": {"other": "-{0} Q"}, "-1": "last quarter"}, "month": {"0": "this month", "1": "next month", "future": {"one": "om {0} qaammat", "other": "om {0} qaammat"}, "past": {"one": "for {0} qaammat siden", "other": "for {0} qaammat siden"}, "-1": "last month"}, "month-short": {"0": "this month", "1": "next month", "future": {"one": "om {0} qaammat", "other": "om {0} qaammat"}, "past": {"one": "for {0} qaammat siden", "other": "for {0} qaammat siden"}, "-1": "last month"}, "month-narrow": {"0": "this month", "1": "next month", "future": {"one": "om {0} qaammat", "other": "om {0} qaammat"}, "past": {"one": "for {0} qaammat siden", "other": "for {0} qaammat siden"}, "-1": "last month"}, "week": {"0": "this week", "1": "next week", "future": {"one": "om {0} sapa<PERSON><PERSON>-a<PERSON><PERSON>a", "other": "om {0} sapa<PERSON><PERSON>-a<PERSON><PERSON>a"}, "past": {"one": "for {0} sapa<PERSON><PERSON>-a<PERSON><PERSON><PERSON> siden", "other": "for {0} sapa<PERSON><PERSON>-a<PERSON><PERSON><PERSON> siden"}, "-1": "last week"}, "week-short": {"0": "this week", "1": "next week", "future": {"one": "om {0} sapa<PERSON><PERSON>-a<PERSON><PERSON>a", "other": "om {0} sapa<PERSON><PERSON>-a<PERSON><PERSON>a"}, "past": {"one": "for {0} sapa<PERSON><PERSON>-a<PERSON><PERSON><PERSON> siden", "other": "for {0} sapa<PERSON><PERSON>-a<PERSON><PERSON><PERSON> siden"}, "-1": "last week"}, "week-narrow": {"0": "this week", "1": "next week", "future": {"one": "om {0} sapa<PERSON><PERSON>-a<PERSON><PERSON>a", "other": "om {0} sapa<PERSON><PERSON>-a<PERSON><PERSON>a"}, "past": {"one": "for {0} sapa<PERSON><PERSON>-a<PERSON><PERSON><PERSON> siden", "other": "for {0} sapa<PERSON><PERSON>-a<PERSON><PERSON><PERSON> siden"}, "-1": "last week"}, "day": {"0": "today", "1": "tomorrow", "future": {"one": "om {0} ulloq unnu<PERSON>lu", "other": "om {0} ulloq unnu<PERSON>lu"}, "past": {"one": "for {0} ul<PERSON><PERSON> un<PERSON>n", "other": "for {0} ul<PERSON><PERSON> un<PERSON>n"}, "-1": "yesterday"}, "day-short": {"0": "today", "1": "tomorrow", "future": {"one": "om {0} ulloq unnu<PERSON>lu", "other": "om {0} ulloq unnu<PERSON>lu"}, "past": {"one": "for {0} ul<PERSON><PERSON> un<PERSON>n", "other": "for {0} ul<PERSON><PERSON> un<PERSON>n"}, "-1": "yesterday"}, "day-narrow": {"0": "today", "1": "tomorrow", "future": {"one": "om {0} ulloq unnu<PERSON>lu", "other": "om {0} ulloq unnu<PERSON>lu"}, "past": {"one": "for {0} ul<PERSON><PERSON> un<PERSON>n", "other": "for {0} ul<PERSON><PERSON> un<PERSON>n"}, "-1": "yesterday"}, "hour": {"0": "this hour", "future": {"one": "om {0} nalunaaquttap-akunnera", "other": "om {0} nalunaaquttap-akunnera"}, "past": {"one": "for {0} nalunaaquttap-akunnera siden", "other": "for {0} nalunaaquttap-akunnera siden"}}, "hour-short": {"0": "this hour", "future": {"one": "om {0} nalunaaquttap-akunnera", "other": "om {0} nalunaaquttap-akunnera"}, "past": {"one": "for {0} nalunaaquttap-akunnera siden", "other": "for {0} nalunaaquttap-akunnera siden"}}, "hour-narrow": {"0": "this hour", "future": {"one": "om {0} nalunaaquttap-akunnera", "other": "om {0} nalunaaquttap-akunnera"}, "past": {"one": "for {0} nalunaaquttap-akunnera siden", "other": "for {0} nalunaaquttap-akunnera siden"}}, "minute": {"0": "this minute", "future": {"one": "om {0} minutsi", "other": "om {0} minutsi"}, "past": {"one": "for {0} minutsi siden", "other": "for {0} minutsi siden"}}, "minute-short": {"0": "this minute", "future": {"one": "om {0} minutsi", "other": "om {0} minutsi"}, "past": {"one": "for {0} minutsi siden", "other": "for {0} minutsi siden"}}, "minute-narrow": {"0": "this minute", "future": {"one": "om {0} minutsi", "other": "om {0} minutsi"}, "past": {"one": "for {0} minutsi siden", "other": "for {0} minutsi siden"}}, "second": {"0": "now", "future": {"one": "om {0} sekundi", "other": "om {0} sekundi"}, "past": {"one": "for {0} sekundi siden", "other": "for {0} sekundi siden"}}, "second-short": {"0": "now", "future": {"one": "om {0} sekundi", "other": "om {0} sekundi"}, "past": {"one": "for {0} sekundi siden", "other": "for {0} sekundi siden"}}, "second-narrow": {"0": "now", "future": {"one": "om {0} sekundi", "other": "om {0} sekundi"}, "past": {"one": "for {0} sekundi siden", "other": "for {0} sekundi siden"}}}}, "availableLocales": ["kl"], "aliases": {}, "parentLocales": {}}