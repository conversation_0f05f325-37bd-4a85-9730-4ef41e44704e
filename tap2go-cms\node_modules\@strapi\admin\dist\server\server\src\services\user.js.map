{"version": 3, "file": "user.js", "sources": ["../../../../../server/src/services/user.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport _ from 'lodash';\nimport { defaults } from 'lodash/fp';\nimport { arrays, errors } from '@strapi/utils';\nimport type { Data } from '@strapi/types';\nimport { createUser, hasSuperAdminRole } from '../domain/user';\nimport type {\n  AdminUser,\n  AdminRole,\n  AdminUserCreationPayload,\n  SanitizedAdminUser,\n  SanitizedAdminRole,\n  AdminUserUpdatePayload,\n  // eslint-disable-next-line node/no-unpublished-import\n} from '../../../shared/contracts/shared';\nimport { password as passwordValidator } from '../validation/common-validators';\nimport { getService } from '../utils';\nimport constants from './constants';\n\nconst { SUPER_ADMIN_CODE } = constants;\n\nconst { ValidationError } = errors;\nconst sanitizeUserRoles = (role: AdminRole): SanitizedAdminRole =>\n  _.pick(role, ['id', 'name', 'description', 'code']);\n\n/**\n * Remove private user fields\n * @param  user - user to sanitize\n */\nconst sanitizeUser = (user: AdminUser): SanitizedAdminUser => {\n  return {\n    ..._.omit(user, ['password', 'resetPasswordToken', 'registrationToken', 'roles']),\n    roles: user.roles && user.roles.map(sanitizeUserRoles),\n  };\n};\n\n/**\n * Create and save a user in database\n * @param attributes A partial user object\n */\nconst create = async (\n  // isActive is added in the controller, it's not sent by the API.\n  attributes: Partial<AdminUserCreationPayload> & { isActive?: true }\n): Promise<AdminUser> => {\n  const userInfo = {\n    registrationToken: getService('token').createToken(),\n    ...attributes,\n  };\n\n  if (_.has(attributes, 'password')) {\n    userInfo.password = await getService('auth').hashPassword(attributes.password!);\n  }\n\n  const user = createUser(userInfo);\n\n  const createdUser = await strapi.db\n    .query('admin::user')\n    .create({ data: user, populate: ['roles'] });\n\n  getService('metrics').sendDidInviteUser();\n\n  strapi.eventHub.emit('user.create', { user: sanitizeUser(createdUser) });\n\n  return createdUser;\n};\n\n/**\n * Update a user in database\n * @param id query params to find the user to update\n * @param attributes A partial user object\n */\nconst updateById = async (\n  id: Data.ID,\n  attributes: Partial<AdminUserUpdatePayload>\n): Promise<AdminUser> => {\n  // Check at least one super admin remains\n  if (_.has(attributes, 'roles')) {\n    const lastAdminUser = await isLastSuperAdminUser(id);\n    const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n    const willRemoveSuperAdminRole = !arrays.includesString(attributes.roles!, superAdminRole.id);\n\n    if (lastAdminUser && willRemoveSuperAdminRole) {\n      throw new ValidationError('You must have at least one user with super admin role.');\n    }\n  }\n\n  // cannot disable last super admin\n  if (attributes.isActive === false) {\n    const lastAdminUser = await isLastSuperAdminUser(id);\n    if (lastAdminUser) {\n      throw new ValidationError('You must have at least one user with super admin role.');\n    }\n  }\n\n  // hash password if a new one is sent\n  if (_.has(attributes, 'password')) {\n    const hashedPassword = await getService('auth').hashPassword(attributes.password!);\n\n    const updatedUser = await strapi.db.query('admin::user').update({\n      where: { id },\n      data: {\n        ...attributes,\n        password: hashedPassword,\n      },\n      populate: ['roles'],\n    });\n\n    strapi.eventHub.emit('user.update', { user: sanitizeUser(updatedUser) });\n\n    return updatedUser;\n  }\n\n  const updatedUser = await strapi.db.query('admin::user').update({\n    where: { id },\n    data: attributes,\n    populate: ['roles'],\n  });\n\n  if (updatedUser) {\n    strapi.eventHub.emit('user.update', { user: sanitizeUser(updatedUser) });\n  }\n\n  return updatedUser;\n};\n\n/**\n * Reset a user password by email. (Used in admin:reset CLI)\n * @param email - user email\n * @param password - new password\n */\nconst resetPasswordByEmail = async (email: string, password: string) => {\n  const user = await strapi.db\n    .query('admin::user')\n    .findOne({ where: { email }, populate: ['roles'] });\n\n  if (!user) {\n    throw new Error(`User not found for email: ${email}`);\n  }\n\n  try {\n    await passwordValidator.validate(password);\n  } catch (error) {\n    throw new ValidationError(\n      'Invalid password. Expected a minimum of 8 characters with at least one number and one uppercase letter'\n    );\n  }\n\n  await updateById(user.id, { password });\n};\n\n/**\n * Check if a user is the last super admin\n * @param userId user's id to look for\n */\nconst isLastSuperAdminUser = async (userId: Data.ID): Promise<boolean> => {\n  const user = (await findOne(userId)) as AdminUser | null;\n  if (!user) return false;\n\n  const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n\n  return superAdminRole.usersCount === 1 && hasSuperAdminRole(user);\n};\n\n/**\n * Check if a user with specific attributes exists in the database\n * @param attributes A partial user object\n */\nconst exists = async (attributes = {} as unknown): Promise<boolean> => {\n  return (await strapi.db.query('admin::user').count({ where: attributes })) > 0;\n};\n\n/**\n * Returns a user registration info\n * @param registrationToken - a user registration token\n * @returns - Returns user email, firstname and lastname\n */\nconst findRegistrationInfo = async (\n  registrationToken: string\n): Promise<Pick<AdminUser, 'email' | 'firstname' | 'lastname'> | undefined> => {\n  const user = await strapi.db.query('admin::user').findOne({ where: { registrationToken } });\n\n  if (!user) {\n    return undefined;\n  }\n\n  return _.pick(user, ['email', 'firstname', 'lastname']);\n};\n\n/**\n * Registers a user based on a registrationToken and some informations to update\n * @param params\n * @param params.registrationToken registration token\n * @param params.userInfo user info\n */\nconst register = async ({\n  registrationToken,\n  userInfo,\n}: {\n  registrationToken: string;\n  userInfo: Partial<AdminUser>;\n}) => {\n  const matchingUser = await strapi.db\n    .query('admin::user')\n    .findOne({ where: { registrationToken } });\n\n  if (!matchingUser) {\n    throw new ValidationError('Invalid registration info');\n  }\n\n  return getService('user').updateById(matchingUser.id, {\n    password: userInfo.password,\n    firstname: userInfo.firstname,\n    lastname: userInfo.lastname,\n    registrationToken: null,\n    isActive: true,\n  });\n};\n\n/**\n * Find one user\n */\nconst findOne = async (id: Data.ID, populate = ['roles']) => {\n  return strapi.db.query('admin::user').findOne({ where: { id }, populate });\n};\n\n/**\n * Find one user by its email\n * @param email\n * @param populate\n * @returns\n */\nconst findOneByEmail = async (email: string, populate = []) => {\n  return strapi.db.query('admin::user').findOne({\n    where: { email: { $eqi: email } },\n    populate,\n  });\n};\n\n/** Find many users (paginated)\n * @param params\n */\nconst findPage = async (params = {}): Promise<unknown> => {\n  const query = strapi\n    .get('query-params')\n    .transform('admin::user', defaults({ populate: ['roles'] }, params));\n\n  return strapi.db.query('admin::user').findPage(query);\n};\n\n/** Delete a user\n * @param id id of the user to delete\n */\nconst deleteById = async (id: Data.ID): Promise<AdminUser | null> => {\n  // Check at least one super admin remains\n  const userToDelete: AdminUser | null = await strapi.db.query('admin::user').findOne({\n    where: { id },\n    populate: ['roles'],\n  });\n\n  if (!userToDelete) {\n    return null;\n  }\n\n  if (userToDelete) {\n    if (userToDelete.roles.some((r) => r.code === SUPER_ADMIN_CODE)) {\n      const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n      if (superAdminRole.usersCount === 1) {\n        throw new ValidationError('You must have at least one user with super admin role.');\n      }\n    }\n  }\n\n  const deletedUser = await strapi.db\n    .query('admin::user')\n    .delete({ where: { id }, populate: ['roles'] });\n\n  strapi.eventHub.emit('user.delete', { user: sanitizeUser(deletedUser) });\n\n  return deletedUser;\n};\n\n/** Delete a user\n * @param ids ids of the users to delete\n */\nconst deleteByIds = async (ids: (string | number)[]): Promise<AdminUser[]> => {\n  // Check at least one super admin remains\n  const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n  const nbOfSuperAdminToDelete = await strapi.db.query('admin::user').count({\n    where: {\n      id: ids,\n      roles: { id: superAdminRole.id },\n    },\n  });\n\n  if (superAdminRole.usersCount === nbOfSuperAdminToDelete) {\n    throw new ValidationError('You must have at least one user with super admin role.');\n  }\n\n  const deletedUsers = [] as AdminUser[];\n  for (const id of ids) {\n    const deletedUser = await strapi.db.query('admin::user').delete({\n      where: { id },\n      populate: ['roles'],\n    });\n\n    deletedUsers.push(deletedUser);\n  }\n\n  strapi.eventHub.emit('user.delete', {\n    users: deletedUsers.map((deletedUser) => sanitizeUser(deletedUser)),\n  });\n\n  return deletedUsers;\n};\n\n/** Count the users that don't have any associated roles\n */\nconst countUsersWithoutRole = async (): Promise<number> => {\n  return strapi.db.query('admin::user').count({\n    where: {\n      roles: {\n        id: { $null: true },\n      },\n    },\n  });\n};\n\n/**\n * Count the number of users based on search params\n * @param params params used for the query\n */\nconst count = async (where = {}): Promise<number> => {\n  return strapi.db.query('admin::user').count({ where });\n};\n\n/**\n * Assign some roles to several users\n */\nconst assignARoleToAll = async (roleId: Data.ID): Promise<void> => {\n  const users = await strapi.db.query('admin::user').findMany({\n    select: ['id'],\n    where: {\n      roles: { id: { $null: true } },\n    },\n  });\n\n  await Promise.all(\n    users.map((user) => {\n      return strapi.db.query('admin::user').update({\n        where: { id: user.id },\n        data: { roles: [roleId] },\n      });\n    })\n  );\n};\n\n/** Display a warning if some users don't have at least one role\n */\nconst displayWarningIfUsersDontHaveRole = async (): Promise<void> => {\n  const count = await countUsersWithoutRole();\n\n  if (count > 0) {\n    strapi.log.warn(`Some users (${count}) don't have any role.`);\n  }\n};\n\n/** Returns an array of interface languages currently used by users\n */\nconst getLanguagesInUse = async (): Promise<string[]> => {\n  const users = await strapi.db.query('admin::user').findMany({ select: ['preferedLanguage'] });\n\n  return users.map((user) => user.preferedLanguage || 'en');\n};\n\nexport default {\n  create,\n  updateById,\n  exists,\n  findRegistrationInfo,\n  register,\n  sanitizeUser,\n  findOne,\n  findOneByEmail,\n  findPage,\n  deleteById,\n  deleteByIds,\n  countUsersWithoutRole,\n  count,\n  assignARoleToAll,\n  displayWarningIfUsersDontHaveRole,\n  resetPasswordByEmail,\n  getLanguagesInUse,\n};\n"], "names": ["SUPER_ADMIN_CODE", "constants", "ValidationError", "errors", "sanitizeUserRoles", "role", "_", "pick", "sanitizeUser", "user", "omit", "roles", "map", "create", "attributes", "userInfo", "registrationToken", "getService", "createToken", "has", "password", "hashPassword", "createUser", "created<PERSON>ser", "strapi", "db", "query", "data", "populate", "sendDidInviteUser", "eventHub", "emit", "updateById", "id", "lastAdminUser", "isLastSuperAdminUser", "superAdminRole", "getSuperAdminWithUsersCount", "willRemoveSuperAdminRole", "arrays", "includesString", "isActive", "hashedPassword", "updatedUser", "update", "where", "resetPasswordByEmail", "email", "findOne", "Error", "passwordValidator", "validate", "error", "userId", "usersCount", "hasSuperAdminRole", "exists", "count", "findRegistrationInfo", "undefined", "register", "matchingUser", "firstname", "lastname", "findOneByEmail", "$eqi", "findPage", "params", "get", "transform", "defaults", "deleteById", "userToDelete", "some", "r", "code", "deletedUser", "delete", "deleteByIds", "ids", "nbOfSuperAdminToDelete", "deletedUsers", "push", "users", "countUsersWithoutRole", "$null", "assignARoleToAll", "roleId", "find<PERSON>any", "select", "Promise", "all", "displayWarningIfUsersDontHaveRole", "log", "warn", "getLanguagesInUse", "preferedLanguage"], "mappings": ";;;;;;;;;;AAmBA,MAAM,EAAEA,gBAAgB,EAAE,GAAGC,SAAAA;AAE7B,MAAM,EAAEC,eAAe,EAAE,GAAGC,YAAAA;AAC5B,MAAMC,oBAAoB,CAACC,IAAAA,GACzBC,CAAEC,CAAAA,IAAI,CAACF,IAAM,EAAA;AAAC,QAAA,IAAA;AAAM,QAAA,MAAA;AAAQ,QAAA,aAAA;AAAe,QAAA;AAAO,KAAA,CAAA;AAEpD;;;IAIA,MAAMG,eAAe,CAACC,IAAAA,GAAAA;IACpB,OAAO;QACL,GAAGH,CAAAA,CAAEI,IAAI,CAACD,IAAM,EAAA;AAAC,YAAA,UAAA;AAAY,YAAA,oBAAA;AAAsB,YAAA,mBAAA;AAAqB,YAAA;SAAQ,CAAC;AACjFE,QAAAA,KAAAA,EAAOF,KAAKE,KAAK,IAAIF,KAAKE,KAAK,CAACC,GAAG,CAACR,iBAAAA;AACtC,KAAA;AACF,CAAA;AAEA;;;IAIA,MAAMS,MAAS,GAAA;AAEbC,UAAAA,GAAAA;AAEA,IAAA,MAAMC,QAAW,GAAA;QACfC,iBAAmBC,EAAAA,gBAAAA,CAAW,SAASC,WAAW,EAAA;AAClD,QAAA,GAAGJ;AACL,KAAA;AAEA,IAAA,IAAIR,CAAEa,CAAAA,GAAG,CAACL,UAAAA,EAAY,UAAa,CAAA,EAAA;QACjCC,QAASK,CAAAA,QAAQ,GAAG,MAAMH,gBAAAA,CAAW,QAAQI,YAAY,CAACP,WAAWM,QAAQ,CAAA;AAC/E;AAEA,IAAA,MAAMX,OAAOa,iBAAWP,CAAAA,QAAAA,CAAAA;IAExB,MAAMQ,WAAAA,GAAc,MAAMC,MAAOC,CAAAA,EAAE,CAChCC,KAAK,CAAC,aACNb,CAAAA,CAAAA,MAAM,CAAC;QAAEc,IAAMlB,EAAAA,IAAAA;QAAMmB,QAAU,EAAA;AAAC,YAAA;AAAQ;AAAC,KAAA,CAAA;AAE5CX,IAAAA,gBAAAA,CAAW,WAAWY,iBAAiB,EAAA;AAEvCL,IAAAA,MAAAA,CAAOM,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAEtB,QAAAA,IAAAA,EAAMD,YAAae,CAAAA,WAAAA;AAAa,KAAA,CAAA;IAEtE,OAAOA,WAAAA;AACT,CAAA;AAEA;;;;IAKA,MAAMS,UAAa,GAAA,OACjBC,EACAnB,EAAAA,UAAAA,GAAAA;;AAGA,IAAA,IAAIR,CAAEa,CAAAA,GAAG,CAACL,UAAAA,EAAY,OAAU,CAAA,EAAA;QAC9B,MAAMoB,aAAAA,GAAgB,MAAMC,oBAAqBF,CAAAA,EAAAA,CAAAA;AACjD,QAAA,MAAMG,cAAiB,GAAA,MAAMnB,gBAAW,CAAA,MAAA,CAAA,CAAQoB,2BAA2B,EAAA;QAC3E,MAAMC,wBAAAA,GAA2B,CAACC,YAAOC,CAAAA,cAAc,CAAC1B,UAAWH,CAAAA,KAAK,EAAGyB,cAAAA,CAAeH,EAAE,CAAA;AAE5F,QAAA,IAAIC,iBAAiBI,wBAA0B,EAAA;AAC7C,YAAA,MAAM,IAAIpC,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AACF;;IAGA,IAAIY,UAAAA,CAAW2B,QAAQ,KAAK,KAAO,EAAA;QACjC,MAAMP,aAAAA,GAAgB,MAAMC,oBAAqBF,CAAAA,EAAAA,CAAAA;AACjD,QAAA,IAAIC,aAAe,EAAA;AACjB,YAAA,MAAM,IAAIhC,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AACF;;AAGA,IAAA,IAAII,CAAEa,CAAAA,GAAG,CAACL,UAAAA,EAAY,UAAa,CAAA,EAAA;AACjC,QAAA,MAAM4B,iBAAiB,MAAMzB,gBAAAA,CAAW,QAAQI,YAAY,CAACP,WAAWM,QAAQ,CAAA;QAEhF,MAAMuB,WAAAA,GAAc,MAAMnB,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAekB,CAAAA,CAAAA,MAAM,CAAC;YAC9DC,KAAO,EAAA;AAAEZ,gBAAAA;AAAG,aAAA;YACZN,IAAM,EAAA;AACJ,gBAAA,GAAGb,UAAU;gBACbM,QAAUsB,EAAAA;AACZ,aAAA;YACAd,QAAU,EAAA;AAAC,gBAAA;AAAQ;AACrB,SAAA,CAAA;AAEAJ,QAAAA,MAAAA,CAAOM,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAEtB,YAAAA,IAAAA,EAAMD,YAAamC,CAAAA,WAAAA;AAAa,SAAA,CAAA;QAEtE,OAAOA,WAAAA;AACT;IAEA,MAAMA,WAAAA,GAAc,MAAMnB,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAekB,CAAAA,CAAAA,MAAM,CAAC;QAC9DC,KAAO,EAAA;AAAEZ,YAAAA;AAAG,SAAA;QACZN,IAAMb,EAAAA,UAAAA;QACNc,QAAU,EAAA;AAAC,YAAA;AAAQ;AACrB,KAAA,CAAA;AAEA,IAAA,IAAIe,WAAa,EAAA;AACfnB,QAAAA,MAAAA,CAAOM,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAEtB,YAAAA,IAAAA,EAAMD,YAAamC,CAAAA,WAAAA;AAAa,SAAA,CAAA;AACxE;IAEA,OAAOA,WAAAA;AACT,CAAA;AAEA;;;;IAKA,MAAMG,oBAAuB,GAAA,OAAOC,KAAe3B,EAAAA,QAAAA,GAAAA;IACjD,MAAMX,IAAAA,GAAO,MAAMe,MAAOC,CAAAA,EAAE,CACzBC,KAAK,CAAC,aACNsB,CAAAA,CAAAA,OAAO,CAAC;QAAEH,KAAO,EAAA;AAAEE,YAAAA;AAAM,SAAA;QAAGnB,QAAU,EAAA;AAAC,YAAA;AAAQ;AAAC,KAAA,CAAA;AAEnD,IAAA,IAAI,CAACnB,IAAM,EAAA;AACT,QAAA,MAAM,IAAIwC,KAAM,CAAA,CAAC,0BAA0B,EAAEF,MAAM,CAAC,CAAA;AACtD;IAEA,IAAI;QACF,MAAMG,yBAAAA,CAAkBC,QAAQ,CAAC/B,QAAAA,CAAAA;AACnC,KAAA,CAAE,OAAOgC,KAAO,EAAA;AACd,QAAA,MAAM,IAAIlD,eACR,CAAA,wGAAA,CAAA;AAEJ;IAEA,MAAM8B,UAAAA,CAAWvB,IAAKwB,CAAAA,EAAE,EAAE;AAAEb,QAAAA;AAAS,KAAA,CAAA;AACvC,CAAA;AAEA;;;IAIA,MAAMe,uBAAuB,OAAOkB,MAAAA,GAAAA;IAClC,MAAM5C,IAAAA,GAAQ,MAAMuC,OAAQK,CAAAA,MAAAA,CAAAA;IAC5B,IAAI,CAAC5C,MAAM,OAAO,KAAA;AAElB,IAAA,MAAM2B,cAAiB,GAAA,MAAMnB,gBAAW,CAAA,MAAA,CAAA,CAAQoB,2BAA2B,EAAA;AAE3E,IAAA,OAAOD,cAAekB,CAAAA,UAAU,KAAK,CAAA,IAAKC,wBAAkB9C,CAAAA,IAAAA,CAAAA;AAC9D,CAAA;AAEA;;;AAGC,IACD,MAAM+C,MAAAA,GAAS,OAAO1C,UAAAA,GAAa,EAAa,GAAA;IAC9C,OAAQ,MAAMU,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAe+B,CAAAA,CAAAA,KAAK,CAAC;QAAEZ,KAAO/B,EAAAA;KAAiB,CAAA,GAAA,CAAA;AAC/E,CAAA;AAEA;;;;IAKA,MAAM4C,uBAAuB,OAC3B1C,iBAAAA,GAAAA;IAEA,MAAMP,IAAAA,GAAO,MAAMe,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAesB,CAAAA,CAAAA,OAAO,CAAC;QAAEH,KAAO,EAAA;AAAE7B,YAAAA;AAAkB;AAAE,KAAA,CAAA;AAEzF,IAAA,IAAI,CAACP,IAAM,EAAA;QACT,OAAOkD,SAAAA;AACT;IAEA,OAAOrD,CAAAA,CAAEC,IAAI,CAACE,IAAM,EAAA;AAAC,QAAA,OAAA;AAAS,QAAA,WAAA;AAAa,QAAA;AAAW,KAAA,CAAA;AACxD,CAAA;AAEA;;;;;AAKC,IACD,MAAMmD,QAAW,GAAA,OAAO,EACtB5C,iBAAiB,EACjBD,QAAQ,EAIT,GAAA;IACC,MAAM8C,YAAAA,GAAe,MAAMrC,MAAOC,CAAAA,EAAE,CACjCC,KAAK,CAAC,aACNsB,CAAAA,CAAAA,OAAO,CAAC;QAAEH,KAAO,EAAA;AAAE7B,YAAAA;AAAkB;AAAE,KAAA,CAAA;AAE1C,IAAA,IAAI,CAAC6C,YAAc,EAAA;AACjB,QAAA,MAAM,IAAI3D,eAAgB,CAAA,2BAAA,CAAA;AAC5B;AAEA,IAAA,OAAOe,iBAAW,MAAQe,CAAAA,CAAAA,UAAU,CAAC6B,YAAAA,CAAa5B,EAAE,EAAE;AACpDb,QAAAA,QAAAA,EAAUL,SAASK,QAAQ;AAC3B0C,QAAAA,SAAAA,EAAW/C,SAAS+C,SAAS;AAC7BC,QAAAA,QAAAA,EAAUhD,SAASgD,QAAQ;QAC3B/C,iBAAmB,EAAA,IAAA;QACnByB,QAAU,EAAA;AACZ,KAAA,CAAA;AACF,CAAA;AAEA;;AAEC,IACD,MAAMO,OAAAA,GAAU,OAAOf,EAAAA,EAAaL,QAAW,GAAA;AAAC,IAAA;AAAQ,CAAA,GAAA;AACtD,IAAA,OAAOJ,OAAOC,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAesB,OAAO,CAAC;QAAEH,KAAO,EAAA;AAAEZ,YAAAA;AAAG,SAAA;AAAGL,QAAAA;AAAS,KAAA,CAAA;AAC1E,CAAA;AAEA;;;;;AAKC,IACD,MAAMoC,cAAAA,GAAiB,OAAOjB,KAAAA,EAAenB,WAAW,EAAE,GAAA;AACxD,IAAA,OAAOJ,OAAOC,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAesB,OAAO,CAAC;QAC5CH,KAAO,EAAA;YAAEE,KAAO,EAAA;gBAAEkB,IAAMlB,EAAAA;AAAM;AAAE,SAAA;AAChCnB,QAAAA;AACF,KAAA,CAAA;AACF,CAAA;AAEA;;AAEC,IACD,MAAMsC,QAAAA,GAAW,OAAOC,MAAAA,GAAS,EAAE,GAAA;IACjC,MAAMzC,KAAAA,GAAQF,OACX4C,GAAG,CAAC,gBACJC,SAAS,CAAC,eAAeC,WAAS,CAAA;QAAE1C,QAAU,EAAA;AAAC,YAAA;AAAQ;KAAIuC,EAAAA,MAAAA,CAAAA,CAAAA;AAE9D,IAAA,OAAO3C,OAAOC,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAewC,QAAQ,CAACxC,KAAAA,CAAAA;AACjD,CAAA;AAEA;;IAGA,MAAM6C,aAAa,OAAOtC,EAAAA,GAAAA;;IAExB,MAAMuC,YAAAA,GAAiC,MAAMhD,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAesB,CAAAA,CAAAA,OAAO,CAAC;QAClFH,KAAO,EAAA;AAAEZ,YAAAA;AAAG,SAAA;QACZL,QAAU,EAAA;AAAC,YAAA;AAAQ;AACrB,KAAA,CAAA;AAEA,IAAA,IAAI,CAAC4C,YAAc,EAAA;QACjB,OAAO,IAAA;AACT;AAEA,IAAA,IAAIA,YAAc,EAAA;QAChB,IAAIA,YAAAA,CAAa7D,KAAK,CAAC8D,IAAI,CAAC,CAACC,CAAMA,GAAAA,CAAAA,CAAEC,IAAI,KAAK3E,gBAAmB,CAAA,EAAA;AAC/D,YAAA,MAAMoC,cAAiB,GAAA,MAAMnB,gBAAW,CAAA,MAAA,CAAA,CAAQoB,2BAA2B,EAAA;YAC3E,IAAID,cAAAA,CAAekB,UAAU,KAAK,CAAG,EAAA;AACnC,gBAAA,MAAM,IAAIpD,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AACF;AACF;IAEA,MAAM0E,WAAAA,GAAc,MAAMpD,MAAOC,CAAAA,EAAE,CAChCC,KAAK,CAAC,aACNmD,CAAAA,CAAAA,MAAM,CAAC;QAAEhC,KAAO,EAAA;AAAEZ,YAAAA;AAAG,SAAA;QAAGL,QAAU,EAAA;AAAC,YAAA;AAAQ;AAAC,KAAA,CAAA;AAE/CJ,IAAAA,MAAAA,CAAOM,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAEtB,QAAAA,IAAAA,EAAMD,YAAaoE,CAAAA,WAAAA;AAAa,KAAA,CAAA;IAEtE,OAAOA,WAAAA;AACT,CAAA;AAEA;;IAGA,MAAME,cAAc,OAAOC,GAAAA,GAAAA;;AAEzB,IAAA,MAAM3C,cAAiB,GAAA,MAAMnB,gBAAW,CAAA,MAAA,CAAA,CAAQoB,2BAA2B,EAAA;IAC3E,MAAM2C,sBAAAA,GAAyB,MAAMxD,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAe+B,CAAAA,CAAAA,KAAK,CAAC;QACxEZ,KAAO,EAAA;YACLZ,EAAI8C,EAAAA,GAAAA;YACJpE,KAAO,EAAA;AAAEsB,gBAAAA,EAAAA,EAAIG,eAAeH;AAAG;AACjC;AACF,KAAA,CAAA;IAEA,IAAIG,cAAAA,CAAekB,UAAU,KAAK0B,sBAAwB,EAAA;AACxD,QAAA,MAAM,IAAI9E,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AAEA,IAAA,MAAM+E,eAAe,EAAE;IACvB,KAAK,MAAMhD,MAAM8C,GAAK,CAAA;QACpB,MAAMH,WAAAA,GAAc,MAAMpD,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAemD,CAAAA,CAAAA,MAAM,CAAC;YAC9DhC,KAAO,EAAA;AAAEZ,gBAAAA;AAAG,aAAA;YACZL,QAAU,EAAA;AAAC,gBAAA;AAAQ;AACrB,SAAA,CAAA;AAEAqD,QAAAA,YAAAA,CAAaC,IAAI,CAACN,WAAAA,CAAAA;AACpB;AAEApD,IAAAA,MAAAA,CAAOM,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAClCoD,QAAAA,KAAAA,EAAOF,YAAarE,CAAAA,GAAG,CAAC,CAACgE,cAAgBpE,YAAaoE,CAAAA,WAAAA,CAAAA;AACxD,KAAA,CAAA;IAEA,OAAOK,YAAAA;AACT,CAAA;AAEA;AACC,IACD,MAAMG,qBAAwB,GAAA,UAAA;AAC5B,IAAA,OAAO5D,OAAOC,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAe+B,KAAK,CAAC;QAC1CZ,KAAO,EAAA;YACLlC,KAAO,EAAA;gBACLsB,EAAI,EAAA;oBAAEoD,KAAO,EAAA;AAAK;AACpB;AACF;AACF,KAAA,CAAA;AACF,CAAA;AAEA;;;AAGC,IACD,MAAM5B,KAAAA,GAAQ,OAAOZ,KAAAA,GAAQ,EAAE,GAAA;AAC7B,IAAA,OAAOrB,OAAOC,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAe+B,KAAK,CAAC;AAAEZ,QAAAA;AAAM,KAAA,CAAA;AACtD,CAAA;AAEA;;IAGA,MAAMyC,mBAAmB,OAAOC,MAAAA,GAAAA;IAC9B,MAAMJ,KAAAA,GAAQ,MAAM3D,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAe8D,CAAAA,CAAAA,QAAQ,CAAC;QAC1DC,MAAQ,EAAA;AAAC,YAAA;AAAK,SAAA;QACd5C,KAAO,EAAA;YACLlC,KAAO,EAAA;gBAAEsB,EAAI,EAAA;oBAAEoD,KAAO,EAAA;AAAK;AAAE;AAC/B;AACF,KAAA,CAAA;AAEA,IAAA,MAAMK,QAAQC,GAAG,CACfR,KAAMvE,CAAAA,GAAG,CAAC,CAACH,IAAAA,GAAAA;AACT,QAAA,OAAOe,OAAOC,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAekB,MAAM,CAAC;YAC3CC,KAAO,EAAA;AAAEZ,gBAAAA,EAAAA,EAAIxB,KAAKwB;AAAG,aAAA;YACrBN,IAAM,EAAA;gBAAEhB,KAAO,EAAA;AAAC4E,oBAAAA;AAAO;AAAC;AAC1B,SAAA,CAAA;AACF,KAAA,CAAA,CAAA;AAEJ,CAAA;AAEA;AACC,IACD,MAAMK,iCAAoC,GAAA,UAAA;AACxC,IAAA,MAAMnC,QAAQ,MAAM2B,qBAAAA,EAAAA;AAEpB,IAAA,IAAI3B,QAAQ,CAAG,EAAA;QACbjC,MAAOqE,CAAAA,GAAG,CAACC,IAAI,CAAC,CAAC,YAAY,EAAErC,KAAM,CAAA,sBAAsB,CAAC,CAAA;AAC9D;AACF,CAAA;AAEA;AACC,IACD,MAAMsC,iBAAoB,GAAA,UAAA;IACxB,MAAMZ,KAAAA,GAAQ,MAAM3D,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAe8D,CAAAA,CAAAA,QAAQ,CAAC;QAAEC,MAAQ,EAAA;AAAC,YAAA;AAAmB;AAAC,KAAA,CAAA;AAE3F,IAAA,OAAON,MAAMvE,GAAG,CAAC,CAACH,IAASA,GAAAA,IAAAA,CAAKuF,gBAAgB,IAAI,IAAA,CAAA;AACtD,CAAA;AAEA,WAAe;AACbnF,IAAAA,MAAAA;AACAmB,IAAAA,UAAAA;AACAwB,IAAAA,MAAAA;AACAE,IAAAA,oBAAAA;AACAE,IAAAA,QAAAA;AACApD,IAAAA,YAAAA;AACAwC,IAAAA,OAAAA;AACAgB,IAAAA,cAAAA;AACAE,IAAAA,QAAAA;AACAK,IAAAA,UAAAA;AACAO,IAAAA,WAAAA;AACAM,IAAAA,qBAAAA;AACA3B,IAAAA,KAAAA;AACA6B,IAAAA,gBAAAA;AACAM,IAAAA,iCAAAA;AACA9C,IAAAA,oBAAAA;AACAiD,IAAAA;AACF,CAAE;;;;"}