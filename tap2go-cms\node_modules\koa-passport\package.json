{"name": "koa-passport", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "version": "6.0.0", "description": "Passport middleware for <PERSON><PERSON>", "keywords": ["koa", "passport", "auth", "authentication", "authorization"], "homepage": "https://github.com/rkusa/koa-passport", "license": "MIT", "main": "./lib", "dependencies": {"passport": "^0.6.0"}, "devDependencies": {"jest": "^29.4.1", "koa": "^2.6.2", "koa-bodyparser": "^4.2.1", "koa-route": "^3.2", "passport-local": "^1.0", "supertest": "^6.1.3"}, "bugs": "https://github.com/rkusa/koa-passport/issues", "repository": {"type": "git", "url": "git://github.com/rkusa/koa-passport.git"}, "scripts": {"test": "jest --testMatch '**/test/*.js'"}, "engines": {"node": ">= 4"}}