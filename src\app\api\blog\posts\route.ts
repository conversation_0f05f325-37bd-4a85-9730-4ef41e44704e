import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/hybrid-client';

/**
 * Blog Posts API Route - Direct Neon Database Access
 * Fallback when Strapi is not available
 */

/**
 * GET - Fetch blog posts from Neon database
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const offset = (page - 1) * limit;

    // Build query with optional status filter
    let whereClause = 'WHERE deleted_at IS NULL';
    const params: any[] = [limit, offset];
    
    if (status) {
      whereClause += ' AND status = $3';
      params.push(status);
    }

    // Fetch posts using direct SQL for performance
    const postsQuery = `
      SELECT 
        id,
        uuid,
        title,
        slug,
        content,
        excerpt,
        status,
        featured_image_url,
        author_name,
        author_bio,
        author_avatar_url,
        categories,
        tags,
        is_featured,
        is_sticky,
        view_count,
        reading_time,
        published_at,
        created_at,
        updated_at
      FROM blog_posts 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const posts = await db.sql(postsQuery, params);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM blog_posts 
      ${whereClause.replace('$3', status ? '$1' : '')}
    `;
    const countParams = status ? [status] : [];
    const [{ total }] = await db.sql(countQuery, countParams);

    // Calculate stats
    const statsQuery = `
      SELECT 
        COUNT(*) as total_posts,
        COUNT(CASE WHEN status = 'published' THEN 1 END) as published_posts,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_posts,
        COALESCE(SUM(view_count), 0) as total_views
      FROM blog_posts 
      WHERE deleted_at IS NULL
    `;
    const [stats] = await db.sql(statsQuery);

    // Transform data to match frontend interface
    const transformedPosts = posts.map((post: any) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      excerpt: post.excerpt,
      status: post.status,
      featured_image_url: post.featured_image_url,
      author_name: post.author_name,
      created_at: post.created_at,
      updated_at: post.updated_at,
      // Additional fields for rich content
      categories: post.categories || [],
      tags: post.tags || [],
      is_featured: post.is_featured,
      view_count: post.view_count || 0,
      reading_time: post.reading_time
    }));

    return NextResponse.json({
      success: true,
      posts: transformedPosts,
      stats: {
        totalPosts: parseInt(stats.total_posts),
        publishedPosts: parseInt(stats.published_posts),
        draftPosts: parseInt(stats.draft_posts),
        totalViews: parseInt(stats.total_views)
      },
      pagination: {
        page,
        limit,
        total: parseInt(total),
        totalPages: Math.ceil(parseInt(total) / limit)
      }
    });

  } catch (error: any) {
    console.error('Error fetching blog posts from database:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch blog posts',
      posts: [],
      stats: {
        totalPosts: 0,
        publishedPosts: 0,
        draftPosts: 0,
        totalViews: 0
      }
    }, { status: 500 });
  }
}

/**
 * POST - Create new blog post in Neon database
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Generate slug if not provided
    const slug = body.slug || body.title
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w-]/g, '');

    // Insert using Prisma for type safety
    const post = await db.orm.blogPost.create({
      data: {
        title: body.title,
        slug: slug,
        content: body.content,
        excerpt: body.excerpt || '',
        status: body.status || 'draft',
        authorName: body.author_name || 'Admin',
        authorBio: body.author_bio || '',
        featuredImageUrl: body.featured_image_url || '',
        categories: body.categories || [],
        tags: body.tags || [],
        isFeatured: body.is_featured || false,
        isSticky: body.is_sticky || false,
        readingTime: body.reading_time || 5,
        publishedAt: body.status === 'published' ? new Date() : null
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Blog post created successfully',
      post: {
        id: post.id,
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt,
        status: post.status,
        featured_image_url: post.featuredImageUrl,
        author_name: post.authorName,
        created_at: post.createdAt,
        updated_at: post.updatedAt
      }
    });

  } catch (error: any) {
    console.error('Error creating blog post:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create blog post'
    }, { status: 500 });
  }
}

/**
 * PUT - Update blog post in Neon database
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json({
        success: false,
        message: 'Post ID is required'
      }, { status: 400 });
    }

    // Update using Prisma
    const post = await db.orm.blogPost.update({
      where: { id: parseInt(id) },
      data: {
        title: updateData.title,
        content: updateData.content,
        excerpt: updateData.excerpt,
        status: updateData.status,
        authorName: updateData.author_name,
        featuredImageUrl: updateData.featured_image_url,
        categories: updateData.categories,
        tags: updateData.tags,
        isFeatured: updateData.is_featured,
        publishedAt: updateData.status === 'published' && !updateData.published_at 
          ? new Date() 
          : updateData.published_at
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Blog post updated successfully',
      post: {
        id: post.id,
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt,
        status: post.status,
        featured_image_url: post.featuredImageUrl,
        author_name: post.authorName,
        created_at: post.createdAt,
        updated_at: post.updatedAt
      }
    });

  } catch (error: any) {
    console.error('Error updating blog post:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update blog post'
    }, { status: 500 });
  }
}

/**
 * DELETE - Delete blog post from Neon database
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        message: 'Post ID is required'
      }, { status: 400 });
    }

    // Soft delete by setting deleted_at timestamp
    await db.orm.blogPost.update({
      where: { id: parseInt(id) },
      data: {
        deletedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Blog post deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting blog post:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to delete blog post'
    }, { status: 500 });
  }
}
