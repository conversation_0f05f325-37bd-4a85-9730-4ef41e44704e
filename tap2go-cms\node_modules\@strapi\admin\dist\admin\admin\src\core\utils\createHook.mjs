/* eslint-disable no-await-in-loop */ /* eslint-disable no-restricted-syntax */ const createHook = ()=>{
    const _handlers = [];
    return {
        register (fn) {
            _handlers.push(fn);
        },
        delete (handler) {
            _handlers.splice(_handlers.indexOf(handler), 1);
        },
        runWaterfall (args, store) {
            return _handlers.reduce((acc, fn)=>fn(acc, store), args);
        },
        async runWaterfallAsync (args, store) {
            let result = args;
            for (const fn of _handlers){
                result = await fn(result, store);
            }
            return result;
        },
        runSeries (...args) {
            return _handlers.map((fn)=>fn(...args));
        },
        async runSeriesAsync (...args) {
            const result = [];
            for (const fn of _handlers){
                result.push(await fn(...args));
            }
            return result;
        },
        runParallel (...args) {
            return Promise.all(_handlers.map((fn)=>{
                return fn(...args);
            }));
        }
    };
};

export { createHook };
//# sourceMappingURL=createHook.mjs.map
