/* @generated */	
// prettier-ignore
if (Intl.RelativeTimeFormat && typeof Intl.RelativeTimeFormat.__addLocaleData === 'function') {
  Intl.RelativeTimeFormat.__addLocaleData({"data":{"tr":{"nu":["latn"],"year":{"0":"bu yıl","1":"gelecek yıl","future":{"one":"{0} yıl sonra","other":"{0} yıl sonra"},"past":{"one":"{0} yıl önce","other":"{0} yıl önce"},"-1":"geçen yıl"},"year-short":{"0":"bu yıl","1":"gelecek yıl","future":{"one":"{0} yıl sonra","other":"{0} yıl sonra"},"past":{"one":"{0} yıl önce","other":"{0} yıl önce"},"-1":"geçen yıl"},"year-narrow":{"0":"bu yıl","1":"gelecek yıl","future":{"one":"{0} yıl sonra","other":"{0} yıl sonra"},"past":{"one":"{0} yıl önce","other":"{0} yıl önce"},"-1":"geçen yıl"},"quarter":{"0":"bu çeyrek","1":"gelecek çeyrek","future":{"one":"{0} çeyrek sonra","other":"{0} çeyrek sonra"},"past":{"one":"{0} çeyrek önce","other":"{0} çeyrek önce"},"-1":"geçen çeyrek"},"quarter-short":{"0":"bu çyr.","1":"gelecek çyr.","future":{"one":"{0} çyr. sonra","other":"{0} çyr. sonra"},"past":{"one":"{0} çyr. önce","other":"{0} çyr. önce"},"-1":"geçen çyr."},"quarter-narrow":{"0":"bu çyr.","1":"gelecek çyr.","future":{"one":"{0} çyr. sonra","other":"{0} çyr. sonra"},"past":{"one":"{0} çyr. önce","other":"{0} çyr. önce"},"-1":"geçen çyr."},"month":{"0":"bu ay","1":"gelecek ay","future":{"one":"{0} ay sonra","other":"{0} ay sonra"},"past":{"one":"{0} ay önce","other":"{0} ay önce"},"-1":"geçen ay"},"month-short":{"0":"bu ay","1":"gelecek ay","future":{"one":"{0} ay sonra","other":"{0} ay sonra"},"past":{"one":"{0} ay önce","other":"{0} ay önce"},"-1":"geçen ay"},"month-narrow":{"0":"bu ay","1":"gelecek ay","future":{"one":"{0} ay sonra","other":"{0} ay sonra"},"past":{"one":"{0} ay önce","other":"{0} ay önce"},"-1":"geçen ay"},"week":{"0":"bu hafta","1":"gelecek hafta","future":{"one":"{0} hafta sonra","other":"{0} hafta sonra"},"past":{"one":"{0} hafta önce","other":"{0} hafta önce"},"-1":"geçen hafta"},"week-short":{"0":"bu hf.","1":"gelecek hf.","future":{"one":"{0} hf. sonra","other":"{0} hf. sonra"},"past":{"one":"{0} hf. önce","other":"{0} hf. önce"},"-1":"geçen hf."},"week-narrow":{"0":"bu hf.","1":"gelecek hf.","future":{"one":"{0} hf. sonra","other":"{0} hf. sonra"},"past":{"one":"{0} hf. önce","other":"{0} hf. önce"},"-1":"geçen hf."},"day":{"0":"bugün","1":"yarın","2":"öbür gün","future":{"one":"{0} gün sonra","other":"{0} gün sonra"},"past":{"one":"{0} gün önce","other":"{0} gün önce"},"-2":"evvelsi gün","-1":"dün"},"day-short":{"0":"bugün","1":"yarın","2":"öbür gün","future":{"one":"{0} gün sonra","other":"{0} gün sonra"},"past":{"one":"{0} gün önce","other":"{0} gün önce"},"-2":"evvelsi gün","-1":"dün"},"day-narrow":{"0":"bugün","1":"yarın","2":"öbür gün","future":{"one":"{0} gün sonra","other":"{0} gün sonra"},"past":{"one":"{0} gün önce","other":"{0} gün önce"},"-2":"evvelsi gün","-1":"dün"},"hour":{"0":"bu saat","future":{"one":"{0} saat sonra","other":"{0} saat sonra"},"past":{"one":"{0} saat önce","other":"{0} saat önce"}},"hour-short":{"0":"bu saat","future":{"one":"{0} sa. sonra","other":"{0} sa. sonra"},"past":{"one":"{0} sa. önce","other":"{0} sa. önce"}},"hour-narrow":{"0":"bu saat","future":{"one":"{0} sa. sonra","other":"{0} sa. sonra"},"past":{"one":"{0} sa. önce","other":"{0} sa. önce"}},"minute":{"0":"bu dakika","future":{"one":"{0} dakika sonra","other":"{0} dakika sonra"},"past":{"one":"{0} dakika önce","other":"{0} dakika önce"}},"minute-short":{"0":"bu dakika","future":{"one":"{0} dk. sonra","other":"{0} dk. sonra"},"past":{"one":"{0} dk. önce","other":"{0} dk. önce"}},"minute-narrow":{"0":"bu dakika","future":{"one":"{0} dk. sonra","other":"{0} dk. sonra"},"past":{"one":"{0} dk. önce","other":"{0} dk. önce"}},"second":{"0":"şimdi","future":{"one":"{0} saniye sonra","other":"{0} saniye sonra"},"past":{"one":"{0} saniye önce","other":"{0} saniye önce"}},"second-short":{"0":"şimdi","future":{"one":"{0} sn. sonra","other":"{0} sn. sonra"},"past":{"one":"{0} sn. önce","other":"{0} sn. önce"}},"second-narrow":{"0":"şimdi","future":{"one":"{0} sn. sonra","other":"{0} sn. sonra"},"past":{"one":"{0} sn. önce","other":"{0} sn. önce"}}}},"availableLocales":["tr-CY","tr"],"aliases":{},"parentLocales":{}})
}