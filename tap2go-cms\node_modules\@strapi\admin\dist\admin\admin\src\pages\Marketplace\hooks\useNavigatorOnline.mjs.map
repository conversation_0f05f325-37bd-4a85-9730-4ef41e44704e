{"version": 3, "file": "useNavigatorOnline.mjs", "sources": ["../../../../../../../admin/src/pages/Marketplace/hooks/useNavigatorOnline.ts"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * For more details about this hook see:\n * https://www.30secondsofcode.org/react/s/use-navigator-on-line\n */\nexport const useNavigatorOnline = (): boolean => {\n  const onlineStatus =\n    typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean'\n      ? navigator.onLine\n      : true;\n\n  const [isOnline, setIsOnline] = React.useState(onlineStatus);\n\n  const setOnline = () => setIsOnline(true);\n  const setOffline = () => setIsOnline(false);\n\n  React.useEffect(() => {\n    window.addEventListener('online', setOnline);\n    window.addEventListener('offline', setOffline);\n\n    return () => {\n      window.removeEventListener('online', setOnline);\n      window.removeEventListener('offline', setOffline);\n    };\n  }, []);\n\n  return isOnline;\n};\n"], "names": ["useNavigatorOnline", "onlineStatus", "navigator", "onLine", "isOnline", "setIsOnline", "React", "useState", "setOnline", "setOffline", "useEffect", "window", "addEventListener", "removeEventListener"], "mappings": ";;AAEA;;;UAIaA,kBAAqB,GAAA,IAAA;IAChC,MAAMC,YAAAA,GACJ,OAAOC,SAAAA,KAAc,WAAe,IAAA,OAAOA,SAAUC,CAAAA,MAAM,KAAK,SAAA,GAC5DD,SAAUC,CAAAA,MAAM,GAChB,IAAA;AAEN,IAAA,MAAM,CAACC,QAAUC,EAAAA,WAAAA,CAAY,GAAGC,KAAAA,CAAMC,QAAQ,CAACN,YAAAA,CAAAA;IAE/C,MAAMO,SAAAA,GAAY,IAAMH,WAAY,CAAA,IAAA,CAAA;IACpC,MAAMI,UAAAA,GAAa,IAAMJ,WAAY,CAAA,KAAA,CAAA;AAErCC,IAAAA,KAAAA,CAAMI,SAAS,CAAC,IAAA;QACdC,MAAOC,CAAAA,gBAAgB,CAAC,QAAUJ,EAAAA,SAAAA,CAAAA;QAClCG,MAAOC,CAAAA,gBAAgB,CAAC,SAAWH,EAAAA,UAAAA,CAAAA;QAEnC,OAAO,IAAA;YACLE,MAAOE,CAAAA,mBAAmB,CAAC,QAAUL,EAAAA,SAAAA,CAAAA;YACrCG,MAAOE,CAAAA,mBAAmB,CAAC,SAAWJ,EAAAA,UAAAA,CAAAA;AACxC,SAAA;AACF,KAAA,EAAG,EAAE,CAAA;IAEL,OAAOL,QAAAA;AACT;;;;"}