{"version": 3, "file": "user.mjs", "sources": ["../../../../../server/src/controllers/user.ts"], "sourcesContent": ["import type { Context } from 'koa';\n\nimport * as _ from 'lodash';\nimport { errors } from '@strapi/utils';\nimport {\n  validateUserCreationInput,\n  validateUserUpdateInput,\n  validateUsersDeleteInput,\n} from '../validation/user';\nimport { getService } from '../utils';\nimport {\n  Create,\n  DeleteMany,\n  DeleteOne,\n  FindAll,\n  FindOne,\n  Update,\n} from '../../../shared/contracts/user';\nimport { AdminUser } from '../../../shared/contracts/shared';\n\nconst { ApplicationError } = errors;\n\nexport default {\n  async create(ctx: Context) {\n    const { body } = ctx.request as Create.Request;\n    const cleanData = { ...body, email: _.get(body, `email`, ``).toLowerCase() };\n\n    await validateUserCreationInput(cleanData);\n\n    const attributes = _.pick(cleanData, [\n      'firstname',\n      'lastname',\n      'email',\n      'roles',\n      'preferedLanguage',\n    ]);\n\n    const userAlreadyExists = await getService('user').exists({\n      email: attributes.email,\n    });\n\n    if (userAlreadyExists) {\n      throw new ApplicationError('Email already taken');\n    }\n\n    const createdUser = await getService('user').create(attributes);\n\n    const userInfo = getService('user').sanitizeUser(createdUser);\n\n    // Note: We need to assign manually the registrationToken to the\n    // final user payload so that it's not removed in the sanitation process.\n    Object.assign(userInfo, { registrationToken: createdUser.registrationToken });\n\n    // Send 201 created\n    ctx.created({ data: userInfo } satisfies Create.Response);\n  },\n\n  async find(ctx: Context) {\n    const userService = getService('user');\n\n    const permissionsManager = strapi.service('admin::permission').createPermissionsManager({\n      ability: ctx.state.userAbility,\n      model: 'admin::user',\n    });\n\n    await permissionsManager.validateQuery(ctx.query);\n    const sanitizedQuery = await permissionsManager.sanitizeQuery(ctx.query);\n\n    // @ts-expect-error update the service type\n    const { results, pagination } = await userService.findPage(sanitizedQuery);\n\n    ctx.body = {\n      data: {\n        results: results.map((user: AdminUser) => userService.sanitizeUser(user)),\n        pagination,\n      },\n    } satisfies FindAll.Response;\n  },\n\n  async findOne(ctx: Context) {\n    const { id } = ctx.params as FindOne.Params;\n\n    const user = await getService('user').findOne(id);\n\n    if (!user) {\n      return ctx.notFound('User does not exist');\n    }\n\n    ctx.body = {\n      data: getService('user').sanitizeUser(user as AdminUser),\n    } as FindOne.Response;\n  },\n\n  async update(ctx: Context) {\n    const { id } = ctx.params as Update.Params;\n    const { body: input } = ctx.request as Update.Request;\n\n    await validateUserUpdateInput(input);\n\n    if (_.has(input, 'email')) {\n      const uniqueEmailCheck = await getService('user').exists({\n        id: { $ne: id },\n        email: input.email,\n      });\n\n      if (uniqueEmailCheck) {\n        throw new ApplicationError('A user with this email address already exists');\n      }\n    }\n\n    const updatedUser = await getService('user').updateById(id, input);\n\n    if (!updatedUser) {\n      return ctx.notFound('User does not exist');\n    }\n\n    ctx.body = {\n      data: getService('user').sanitizeUser(updatedUser),\n    } satisfies Update.Response;\n  },\n\n  async deleteOne(ctx: Context) {\n    const { id } = ctx.params as DeleteOne.Params;\n\n    const deletedUser = await getService('user').deleteById(id);\n\n    if (!deletedUser) {\n      return ctx.notFound('User not found');\n    }\n\n    return ctx.deleted({\n      data: getService('user').sanitizeUser(deletedUser),\n    } satisfies DeleteOne.Response);\n  },\n\n  /**\n   * Delete several users\n   * @param ctx - koa context\n   */\n  async deleteMany(ctx: Context) {\n    const { body } = ctx.request as DeleteMany.Request;\n    await validateUsersDeleteInput(body);\n\n    const users = await getService('user').deleteByIds(body.ids);\n\n    const sanitizedUsers = users.map(getService('user').sanitizeUser);\n\n    return ctx.deleted({\n      data: sanitizedUsers,\n    } satisfies DeleteMany.Response);\n  },\n};\n"], "names": ["ApplicationError", "errors", "create", "ctx", "body", "request", "cleanData", "email", "_", "get", "toLowerCase", "validateUserCreationInput", "attributes", "pick", "userAlreadyExists", "getService", "exists", "created<PERSON>ser", "userInfo", "sanitizeUser", "Object", "assign", "registrationToken", "created", "data", "find", "userService", "permissionsManager", "strapi", "service", "createPermissionsManager", "ability", "state", "userAbility", "model", "validate<PERSON><PERSON>y", "query", "sanitized<PERSON>uery", "sanitize<PERSON>uery", "results", "pagination", "findPage", "map", "user", "findOne", "id", "params", "notFound", "update", "input", "validateUserUpdateInput", "has", "uniqueEmailCheck", "$ne", "updatedUser", "updateById", "deleteOne", "deletedUser", "deleteById", "deleted", "deleteMany", "validateUsersDeleteInput", "users", "deleteByIds", "ids", "sanitizedUsers"], "mappings": ";;;;;AAoBA,MAAM,EAAEA,gBAAgB,EAAE,GAAGC,MAAAA;AAE7B,WAAe;AACb,IAAA,MAAMC,QAAOC,GAAY,EAAA;AACvB,QAAA,MAAM,EAAEC,IAAI,EAAE,GAAGD,IAAIE,OAAO;AAC5B,QAAA,MAAMC,SAAY,GAAA;AAAE,YAAA,GAAGF,IAAI;YAAEG,KAAOC,EAAAA,CAAAA,CAAEC,GAAG,CAACL,IAAM,EAAA,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA,CAAEM,WAAW;AAAG,SAAA;AAE3E,QAAA,MAAMC,yBAA0BL,CAAAA,SAAAA,CAAAA;AAEhC,QAAA,MAAMM,UAAaJ,GAAAA,CAAAA,CAAEK,IAAI,CAACP,SAAW,EAAA;AACnC,YAAA,WAAA;AACA,YAAA,UAAA;AACA,YAAA,OAAA;AACA,YAAA,OAAA;AACA,YAAA;AACD,SAAA,CAAA;AAED,QAAA,MAAMQ,iBAAoB,GAAA,MAAMC,UAAW,CAAA,MAAA,CAAA,CAAQC,MAAM,CAAC;AACxDT,YAAAA,KAAAA,EAAOK,WAAWL;AACpB,SAAA,CAAA;AAEA,QAAA,IAAIO,iBAAmB,EAAA;AACrB,YAAA,MAAM,IAAId,gBAAiB,CAAA,qBAAA,CAAA;AAC7B;AAEA,QAAA,MAAMiB,WAAc,GAAA,MAAMF,UAAW,CAAA,MAAA,CAAA,CAAQb,MAAM,CAACU,UAAAA,CAAAA;AAEpD,QAAA,MAAMM,QAAWH,GAAAA,UAAAA,CAAW,MAAQI,CAAAA,CAAAA,YAAY,CAACF,WAAAA,CAAAA;;;QAIjDG,MAAOC,CAAAA,MAAM,CAACH,QAAU,EAAA;AAAEI,YAAAA,iBAAAA,EAAmBL,YAAYK;AAAkB,SAAA,CAAA;;AAG3EnB,QAAAA,GAAAA,CAAIoB,OAAO,CAAC;YAAEC,IAAMN,EAAAA;AAAS,SAAA,CAAA;AAC/B,KAAA;AAEA,IAAA,MAAMO,MAAKtB,GAAY,EAAA;AACrB,QAAA,MAAMuB,cAAcX,UAAW,CAAA,MAAA,CAAA;AAE/B,QAAA,MAAMY,qBAAqBC,MAAOC,CAAAA,OAAO,CAAC,mBAAA,CAAA,CAAqBC,wBAAwB,CAAC;YACtFC,OAAS5B,EAAAA,GAAAA,CAAI6B,KAAK,CAACC,WAAW;YAC9BC,KAAO,EAAA;AACT,SAAA,CAAA;AAEA,QAAA,MAAMP,kBAAmBQ,CAAAA,aAAa,CAAChC,GAAAA,CAAIiC,KAAK,CAAA;AAChD,QAAA,MAAMC,iBAAiB,MAAMV,kBAAAA,CAAmBW,aAAa,CAACnC,IAAIiC,KAAK,CAAA;;QAGvE,MAAM,EAAEG,OAAO,EAAEC,UAAU,EAAE,GAAG,MAAMd,WAAYe,CAAAA,QAAQ,CAACJ,cAAAA,CAAAA;AAE3DlC,QAAAA,GAAAA,CAAIC,IAAI,GAAG;YACToB,IAAM,EAAA;AACJe,gBAAAA,OAAAA,EAASA,QAAQG,GAAG,CAAC,CAACC,IAAoBjB,GAAAA,WAAAA,CAAYP,YAAY,CAACwB,IAAAA,CAAAA,CAAAA;AACnEH,gBAAAA;AACF;AACF,SAAA;AACF,KAAA;AAEA,IAAA,MAAMI,SAAQzC,GAAY,EAAA;AACxB,QAAA,MAAM,EAAE0C,EAAE,EAAE,GAAG1C,IAAI2C,MAAM;AAEzB,QAAA,MAAMH,IAAO,GAAA,MAAM5B,UAAW,CAAA,MAAA,CAAA,CAAQ6B,OAAO,CAACC,EAAAA,CAAAA;AAE9C,QAAA,IAAI,CAACF,IAAM,EAAA;YACT,OAAOxC,GAAAA,CAAI4C,QAAQ,CAAC,qBAAA,CAAA;AACtB;AAEA5C,QAAAA,GAAAA,CAAIC,IAAI,GAAG;YACToB,IAAMT,EAAAA,UAAAA,CAAW,MAAQI,CAAAA,CAAAA,YAAY,CAACwB,IAAAA;AACxC,SAAA;AACF,KAAA;AAEA,IAAA,MAAMK,QAAO7C,GAAY,EAAA;AACvB,QAAA,MAAM,EAAE0C,EAAE,EAAE,GAAG1C,IAAI2C,MAAM;AACzB,QAAA,MAAM,EAAE1C,IAAM6C,EAAAA,KAAK,EAAE,GAAG9C,IAAIE,OAAO;AAEnC,QAAA,MAAM6C,uBAAwBD,CAAAA,KAAAA,CAAAA;AAE9B,QAAA,IAAIzC,CAAE2C,CAAAA,GAAG,CAACF,KAAAA,EAAO,OAAU,CAAA,EAAA;AACzB,YAAA,MAAMG,gBAAmB,GAAA,MAAMrC,UAAW,CAAA,MAAA,CAAA,CAAQC,MAAM,CAAC;gBACvD6B,EAAI,EAAA;oBAAEQ,GAAKR,EAAAA;AAAG,iBAAA;AACdtC,gBAAAA,KAAAA,EAAO0C,MAAM1C;AACf,aAAA,CAAA;AAEA,YAAA,IAAI6C,gBAAkB,EAAA;AACpB,gBAAA,MAAM,IAAIpD,gBAAiB,CAAA,+CAAA,CAAA;AAC7B;AACF;AAEA,QAAA,MAAMsD,cAAc,MAAMvC,UAAAA,CAAW,MAAQwC,CAAAA,CAAAA,UAAU,CAACV,EAAII,EAAAA,KAAAA,CAAAA;AAE5D,QAAA,IAAI,CAACK,WAAa,EAAA;YAChB,OAAOnD,GAAAA,CAAI4C,QAAQ,CAAC,qBAAA,CAAA;AACtB;AAEA5C,QAAAA,GAAAA,CAAIC,IAAI,GAAG;YACToB,IAAMT,EAAAA,UAAAA,CAAW,MAAQI,CAAAA,CAAAA,YAAY,CAACmC,WAAAA;AACxC,SAAA;AACF,KAAA;AAEA,IAAA,MAAME,WAAUrD,GAAY,EAAA;AAC1B,QAAA,MAAM,EAAE0C,EAAE,EAAE,GAAG1C,IAAI2C,MAAM;AAEzB,QAAA,MAAMW,WAAc,GAAA,MAAM1C,UAAW,CAAA,MAAA,CAAA,CAAQ2C,UAAU,CAACb,EAAAA,CAAAA;AAExD,QAAA,IAAI,CAACY,WAAa,EAAA;YAChB,OAAOtD,GAAAA,CAAI4C,QAAQ,CAAC,gBAAA,CAAA;AACtB;QAEA,OAAO5C,GAAAA,CAAIwD,OAAO,CAAC;YACjBnC,IAAMT,EAAAA,UAAAA,CAAW,MAAQI,CAAAA,CAAAA,YAAY,CAACsC,WAAAA;AACxC,SAAA,CAAA;AACF,KAAA;AAEA;;;MAIA,MAAMG,YAAWzD,GAAY,EAAA;AAC3B,QAAA,MAAM,EAAEC,IAAI,EAAE,GAAGD,IAAIE,OAAO;AAC5B,QAAA,MAAMwD,wBAAyBzD,CAAAA,IAAAA,CAAAA;AAE/B,QAAA,MAAM0D,QAAQ,MAAM/C,UAAAA,CAAW,QAAQgD,WAAW,CAAC3D,KAAK4D,GAAG,CAAA;AAE3D,QAAA,MAAMC,iBAAiBH,KAAMpB,CAAAA,GAAG,CAAC3B,UAAAA,CAAW,QAAQI,YAAY,CAAA;QAEhE,OAAOhB,GAAAA,CAAIwD,OAAO,CAAC;YACjBnC,IAAMyC,EAAAA;AACR,SAAA,CAAA;AACF;AACF,CAAE;;;;"}