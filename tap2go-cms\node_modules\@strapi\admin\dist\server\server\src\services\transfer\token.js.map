{"version": 3, "file": "token.js", "sources": ["../../../../../../server/src/services/transfer/token.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport assert from 'assert';\nimport { map, isArray, omit, uniq, isNil, difference, isEmpty, isNumber } from 'lodash/fp';\nimport { errors } from '@strapi/utils';\nimport '@strapi/types';\nimport constants from '../constants';\nimport { getService } from '../../utils';\nimport {\n  DatabaseTransferToken,\n  SanitizedTransferToken,\n  TokenCreatePayload,\n  TokenUpdatePayload,\n  TransferToken,\n  TransferTokenPermission,\n} from '../../../../shared/contracts/transfer';\n\nconst { ValidationError, NotFoundError } = errors;\n\nconst TRANSFER_TOKEN_UID = 'admin::transfer-token';\nconst TRANSFER_TOKEN_PERMISSION_UID = 'admin::transfer-token-permission';\n\nconst SELECT_FIELDS = [\n  'id',\n  'name',\n  'description',\n  'lastUsedAt',\n  'lifespan',\n  'expiresAt',\n  'createdAt',\n  'updatedAt',\n] as const;\n\nconst POPULATE_FIELDS = ['permissions'] as const;\n\n/**\n * Return a list of all tokens and their permissions\n */\nconst list = async (): Promise<SanitizedTransferToken[]> => {\n  const tokens: DatabaseTransferToken[] = await strapi.db.query(TRANSFER_TOKEN_UID).findMany({\n    select: SELECT_FIELDS,\n    populate: POPULATE_FIELDS,\n    orderBy: { name: 'ASC' },\n  });\n\n  if (!tokens) return tokens;\n  return tokens.map((token) => flattenTokenPermissions(token));\n};\n\n/**\n * Create a random token's access key\n */\nconst generateRandomAccessKey = (): string => crypto.randomBytes(128).toString('hex');\n\n/**\n * Validate the given access key's format and returns it if valid\n */\nconst validateAccessKey = (accessKey: string): string => {\n  assert(typeof accessKey === 'string', 'Access key needs to be a string');\n  assert(accessKey.length >= 15, 'Access key needs to have at least 15 characters');\n\n  return accessKey;\n};\n\nexport const hasAccessKey = <T extends { accessKey?: string }>(\n  attributes: T\n): attributes is T & { accessKey: string } => {\n  return 'accessKey' in attributes;\n};\n\n/**\n * Create a token and its permissions\n */\nconst create = async (attributes: TokenCreatePayload): Promise<TransferToken> => {\n  const accessKey = hasAccessKey(attributes)\n    ? validateAccessKey(attributes.accessKey)\n    : generateRandomAccessKey();\n\n  // Make sure the access key isn't picked up directly from the attributes for the next steps\n  delete attributes.accessKey;\n\n  assertTokenPermissionsValidity(attributes);\n  assertValidLifespan(attributes.lifespan);\n\n  const result = (await strapi.db.transaction(async () => {\n    const transferToken = await strapi.db.query(TRANSFER_TOKEN_UID).create({\n      select: SELECT_FIELDS,\n      populate: POPULATE_FIELDS,\n      data: {\n        ...omit('permissions', attributes),\n        accessKey: hash(accessKey),\n        ...getExpirationFields(attributes.lifespan),\n      },\n    });\n\n    await Promise.all(\n      uniq(attributes.permissions).map((action) =>\n        strapi.db\n          .query(TRANSFER_TOKEN_PERMISSION_UID)\n          .create({ data: { action, token: transferToken } })\n      )\n    );\n\n    const currentPermissions: TransferTokenPermission[] = await strapi.db\n      .query(TRANSFER_TOKEN_UID)\n      .load(transferToken, 'permissions');\n\n    if (currentPermissions) {\n      Object.assign(transferToken, { permissions: map('action', currentPermissions) });\n    }\n\n    return transferToken;\n  })) as TransferToken;\n\n  return { ...result, accessKey };\n};\n\n/**\n * Update a token and its permissions\n */\nconst update = async (\n  id: string | number,\n  attributes: TokenUpdatePayload\n): Promise<SanitizedTransferToken> => {\n  // retrieve token without permissions\n  const originalToken = await strapi.db.query(TRANSFER_TOKEN_UID).findOne({ where: { id } });\n\n  if (!originalToken) {\n    throw new NotFoundError('Token not found');\n  }\n\n  assertTokenPermissionsValidity(attributes);\n  assertValidLifespan(attributes.lifespan);\n\n  return strapi.db.transaction(async () => {\n    const updatedToken = await strapi.db.query(TRANSFER_TOKEN_UID).update({\n      select: SELECT_FIELDS,\n      where: { id },\n      data: {\n        ...omit('permissions', attributes),\n      },\n    });\n\n    if (attributes.permissions) {\n      const currentPermissionsResult = await strapi.db\n        .query(TRANSFER_TOKEN_UID)\n        .load(updatedToken, 'permissions');\n\n      const currentPermissions = map('action', currentPermissionsResult || []);\n      const newPermissions = uniq(attributes.permissions);\n\n      const actionsToDelete = difference(currentPermissions, newPermissions);\n      const actionsToAdd = difference(newPermissions, currentPermissions);\n\n      // TODO: improve efficiency here\n      // method using a loop -- works but very inefficient\n      await Promise.all(\n        actionsToDelete.map((action) =>\n          strapi.db.query(TRANSFER_TOKEN_PERMISSION_UID).delete({\n            where: { action, token: id },\n          })\n        )\n      );\n\n      // TODO: improve efficiency here\n      // using a loop -- works but very inefficient\n      await Promise.all(\n        actionsToAdd.map((action) =>\n          strapi.db.query(TRANSFER_TOKEN_PERMISSION_UID).create({\n            data: { action, token: id },\n          })\n        )\n      );\n    }\n\n    // retrieve permissions\n    const permissionsFromDb: TransferTokenPermission[] = await strapi.db\n      .query(TRANSFER_TOKEN_UID)\n      .load(updatedToken, 'permissions');\n\n    return {\n      ...updatedToken,\n      permissions: permissionsFromDb ? permissionsFromDb.map((p) => p.action) : undefined,\n    };\n  }) as unknown as Promise<SanitizedTransferToken>;\n};\n\n/**\n * Revoke (delete) a token\n */\nconst revoke = async (id: string | number): Promise<SanitizedTransferToken> => {\n  return strapi.db.transaction(async () =>\n    strapi.db\n      .query(TRANSFER_TOKEN_UID)\n      .delete({ select: SELECT_FIELDS, populate: POPULATE_FIELDS, where: { id } })\n  ) as unknown as Promise<SanitizedTransferToken>;\n};\n\n/**\n *  Get a token\n */\nconst getBy = async (\n  whereParams = {} as {\n    id?: string | number;\n    name?: string;\n    lastUsedAt?: number;\n    description?: string;\n    accessKey?: string;\n  }\n): Promise<SanitizedTransferToken | null> => {\n  if (Object.keys(whereParams).length === 0) {\n    return null;\n  }\n\n  const token = await strapi.db\n    .query(TRANSFER_TOKEN_UID)\n    .findOne({ select: SELECT_FIELDS, populate: POPULATE_FIELDS, where: whereParams });\n\n  if (!token) {\n    return token;\n  }\n\n  return flattenTokenPermissions(token);\n};\n\n/**\n * Retrieve a token by id\n */\nconst getById = async (id: string | number): Promise<SanitizedTransferToken | null> => {\n  return getBy({ id });\n};\n\n/**\n * Retrieve a token by name\n */\nconst getByName = async (name: string): Promise<SanitizedTransferToken | null> => {\n  return getBy({ name });\n};\n\n/**\n * Check if token exists\n */\nconst exists = async (\n  whereParams = {} as {\n    id?: string | number;\n    name?: string;\n    lastUsedAt?: number;\n    description?: string;\n    accessKey?: string;\n  }\n): Promise<boolean> => {\n  const transferToken = await getBy(whereParams);\n\n  return !!transferToken;\n};\n\nconst regenerate = async (id: string | number): Promise<TransferToken> => {\n  const accessKey = crypto.randomBytes(128).toString('hex');\n  const transferToken = (await strapi.db.transaction(async () =>\n    strapi.db.query(TRANSFER_TOKEN_UID).update({\n      select: ['id', 'accessKey'],\n      where: { id },\n      data: {\n        accessKey: hash(accessKey),\n      },\n    })\n  )) as Promise<TransferToken>;\n\n  if (!transferToken) {\n    throw new NotFoundError('The provided token id does not exist');\n  }\n\n  return {\n    ...transferToken,\n    accessKey,\n  };\n};\n\nconst getExpirationFields = (lifespan: TransferToken['lifespan']) => {\n  // it must be nil or a finite number >= 0\n  const isValidNumber = isNumber(lifespan) && Number.isFinite(lifespan) && lifespan > 0;\n  if (!isValidNumber && !isNil(lifespan)) {\n    throw new ValidationError('lifespan must be a positive number or null');\n  }\n\n  return {\n    lifespan: lifespan || null,\n    expiresAt: lifespan ? Date.now() + lifespan : null,\n  };\n};\n\n/**\n * Return a secure sha512 hash of an accessKey\n */\nconst hash = (accessKey: string): string => {\n  const { hasValidTokenSalt } = getService('transfer').utils;\n\n  if (!hasValidTokenSalt()) {\n    throw new TypeError('Required token salt is not defined');\n  }\n\n  return crypto\n    .createHmac('sha512', strapi.config.get('admin.transfer.token.salt'))\n    .update(accessKey)\n    .digest('hex');\n};\n\nconst checkSaltIsDefined = () => {\n  const { hasValidTokenSalt } = getService('transfer').utils;\n\n  // Ignore the check if the data-transfer feature is manually disabled\n  if (!strapi.config.get('server.transfer.remote.enabled')) {\n    return;\n  }\n\n  if (!hasValidTokenSalt()) {\n    process.emitWarning(\n      `Missing transfer.token.salt: Data transfer features have been disabled.\nPlease set transfer.token.salt in config/admin.js (ex: you can generate one using Node with \\`crypto.randomBytes(16).toString('base64')\\`)\nFor security reasons, prefer storing the secret in an environment variable and read it in config/admin.js. See https://docs.strapi.io/developer-docs/latest/setup-deployment-guides/configurations/optional/environment.html#configuration-using-environment-variables.`\n    );\n  }\n};\n\n/**\n * Flatten a token's database permissions objects to an array of strings\n */\nconst flattenTokenPermissions = (token: DatabaseTransferToken): TransferToken => {\n  if (!token) {\n    return token;\n  }\n\n  return {\n    ...token,\n    permissions: isArray(token.permissions)\n      ? map('action', token.permissions as TransferTokenPermission[])\n      : token.permissions,\n  };\n};\n\n/**\n * Assert that a token's permissions are valid\n */\nconst assertTokenPermissionsValidity = (attributes: TokenUpdatePayload) => {\n  const permissionService = strapi.service('admin::transfer').permission;\n  const validPermissions = permissionService.providers.action.keys();\n  const invalidPermissions = difference(attributes.permissions, validPermissions);\n\n  if (!isEmpty(invalidPermissions)) {\n    throw new ValidationError(`Unknown permissions provided: ${invalidPermissions.join(', ')}`);\n  }\n};\n\n/**\n * Check if a token's lifespan is valid\n */\nconst isValidLifespan = (lifespan: unknown) => {\n  if (isNil(lifespan)) {\n    return true;\n  }\n\n  if (\n    !isNumber(lifespan) ||\n    !Object.values(constants.TRANSFER_TOKEN_LIFESPANS).includes(lifespan)\n  ) {\n    return false;\n  }\n\n  return true;\n};\n\n/**\n * Assert that a token's lifespan is valid\n */\nconst assertValidLifespan = (lifespan: unknown) => {\n  if (!isValidLifespan(lifespan)) {\n    throw new ValidationError(\n      `lifespan must be one of the following values:\n      ${Object.values(constants.TRANSFER_TOKEN_LIFESPANS).join(', ')}`\n    );\n  }\n};\n\nexport {\n  create,\n  list,\n  exists,\n  getBy,\n  getById,\n  getByName,\n  update,\n  revoke,\n  regenerate,\n  hash,\n  checkSaltIsDefined,\n};\n"], "names": ["ValidationError", "NotFoundError", "errors", "TRANSFER_TOKEN_UID", "TRANSFER_TOKEN_PERMISSION_UID", "SELECT_FIELDS", "POPULATE_FIELDS", "list", "tokens", "strapi", "db", "query", "find<PERSON>any", "select", "populate", "orderBy", "name", "map", "token", "flattenTokenPermissions", "generateRandomAccessKey", "crypto", "randomBytes", "toString", "validateAccess<PERSON>ey", "accessKey", "assert", "length", "hasAccessKey", "attributes", "create", "assertTokenPermissionsValidity", "assertValidLifespan", "lifespan", "result", "transaction", "transferToken", "data", "omit", "hash", "getExpirationFields", "Promise", "all", "uniq", "permissions", "action", "currentPermissions", "load", "Object", "assign", "update", "id", "originalToken", "findOne", "where", "updatedToken", "currentPermissionsResult", "newPermissions", "actionsToDelete", "difference", "actionsToAdd", "delete", "permissionsFromDb", "p", "undefined", "revoke", "get<PERSON>y", "where<PERSON><PERSON><PERSON>", "keys", "getById", "getByName", "exists", "regenerate", "isValidNumber", "isNumber", "Number", "isFinite", "isNil", "expiresAt", "Date", "now", "hasValidTokenSalt", "getService", "utils", "TypeError", "createHmac", "config", "get", "digest", "checkSaltIsDefined", "process", "emitWarning", "isArray", "permissionService", "service", "permission", "validPermissions", "providers", "invalidPermissions", "isEmpty", "join", "isValidLifespan", "values", "constants", "TRANSFER_TOKEN_LIFESPANS", "includes"], "mappings": ";;;;;;;;;;AAgBA,MAAM,EAAEA,eAAe,EAAEC,aAAa,EAAE,GAAGC,YAAAA;AAE3C,MAAMC,kBAAqB,GAAA,uBAAA;AAC3B,MAAMC,6BAAgC,GAAA,kCAAA;AAEtC,MAAMC,aAAgB,GAAA;AACpB,IAAA,IAAA;AACA,IAAA,MAAA;AACA,IAAA,aAAA;AACA,IAAA,YAAA;AACA,IAAA,UAAA;AACA,IAAA,WAAA;AACA,IAAA,WAAA;AACA,IAAA;AACD,CAAA;AAED,MAAMC,eAAkB,GAAA;AAAC,IAAA;AAAc,CAAA;AAEvC;;AAEC,UACKC,IAAO,GAAA,UAAA;IACX,MAAMC,MAAAA,GAAkC,MAAMC,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAACR,kBAAoBS,CAAAA,CAAAA,QAAQ,CAAC;QACzFC,MAAQR,EAAAA,aAAAA;QACRS,QAAUR,EAAAA,eAAAA;QACVS,OAAS,EAAA;YAAEC,IAAM,EAAA;AAAM;AACzB,KAAA,CAAA;IAEA,IAAI,CAACR,QAAQ,OAAOA,MAAAA;AACpB,IAAA,OAAOA,MAAOS,CAAAA,GAAG,CAAC,CAACC,QAAUC,uBAAwBD,CAAAA,KAAAA,CAAAA,CAAAA;AACvD;AAEA;;IAGA,MAAME,0BAA0B,IAAcC,MAAAA,CAAOC,WAAW,CAAC,GAAA,CAAA,CAAKC,QAAQ,CAAC,KAAA,CAAA;AAE/E;;IAGA,MAAMC,oBAAoB,CAACC,SAAAA,GAAAA;IACzBC,MAAO,CAAA,OAAOD,cAAc,QAAU,EAAA,iCAAA,CAAA;IACtCC,MAAOD,CAAAA,SAAAA,CAAUE,MAAM,IAAI,EAAI,EAAA,iDAAA,CAAA;IAE/B,OAAOF,SAAAA;AACT,CAAA;AAEO,MAAMG,eAAe,CAC1BC,UAAAA,GAAAA;AAEA,IAAA,OAAO,WAAeA,IAAAA,UAAAA;AACxB;AAEA;;IAGA,MAAMC,SAAS,OAAOD,UAAAA,GAAAA;AACpB,IAAA,MAAMJ,YAAYG,YAAaC,CAAAA,UAAAA,CAAAA,GAC3BL,iBAAkBK,CAAAA,UAAAA,CAAWJ,SAAS,CACtCL,GAAAA,uBAAAA,EAAAA;;AAGJ,IAAA,OAAOS,WAAWJ,SAAS;IAE3BM,8BAA+BF,CAAAA,UAAAA,CAAAA;AAC/BG,IAAAA,mBAAAA,CAAoBH,WAAWI,QAAQ,CAAA;AAEvC,IAAA,MAAMC,SAAU,MAAMzB,MAAAA,CAAOC,EAAE,CAACyB,WAAW,CAAC,UAAA;QAC1C,MAAMC,aAAAA,GAAgB,MAAM3B,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAACR,kBAAoB2B,CAAAA,CAAAA,MAAM,CAAC;YACrEjB,MAAQR,EAAAA,aAAAA;YACRS,QAAUR,EAAAA,eAAAA;YACV+B,IAAM,EAAA;gBACJ,GAAGC,OAAAA,CAAK,eAAeT,UAAW,CAAA;AAClCJ,gBAAAA,SAAAA,EAAWc,IAAKd,CAAAA,SAAAA,CAAAA;gBAChB,GAAGe,mBAAAA,CAAoBX,UAAWI,CAAAA,QAAQ;AAC5C;AACF,SAAA,CAAA;AAEA,QAAA,MAAMQ,QAAQC,GAAG,CACfC,QAAKd,UAAWe,CAAAA,WAAW,EAAE3B,GAAG,CAAC,CAAC4B,MAAAA,GAChCpC,OAAOC,EAAE,CACNC,KAAK,CAACP,6BAAAA,CAAAA,CACN0B,MAAM,CAAC;gBAAEO,IAAM,EAAA;AAAEQ,oBAAAA,MAAAA;oBAAQ3B,KAAOkB,EAAAA;AAAc;AAAE,aAAA,CAAA,CAAA,CAAA;QAIvD,MAAMU,kBAAAA,GAAgD,MAAMrC,MAAAA,CAAOC,EAAE,CAClEC,KAAK,CAACR,kBAAAA,CAAAA,CACN4C,IAAI,CAACX,aAAe,EAAA,aAAA,CAAA;AAEvB,QAAA,IAAIU,kBAAoB,EAAA;YACtBE,MAAOC,CAAAA,MAAM,CAACb,aAAe,EAAA;AAAEQ,gBAAAA,WAAAA,EAAa3B,OAAI,QAAU6B,EAAAA,kBAAAA;AAAoB,aAAA,CAAA;AAChF;QAEA,OAAOV,aAAAA;AACT,KAAA,CAAA;IAEA,OAAO;AAAE,QAAA,GAAGF,MAAM;AAAET,QAAAA;AAAU,KAAA;AAChC;AAEA;;IAGA,MAAMyB,MAAS,GAAA,OACbC,EACAtB,EAAAA,UAAAA,GAAAA;;IAGA,MAAMuB,aAAAA,GAAgB,MAAM3C,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAACR,kBAAoBkD,CAAAA,CAAAA,OAAO,CAAC;QAAEC,KAAO,EAAA;AAAEH,YAAAA;AAAG;AAAE,KAAA,CAAA;AAExF,IAAA,IAAI,CAACC,aAAe,EAAA;AAClB,QAAA,MAAM,IAAInD,aAAc,CAAA,iBAAA,CAAA;AAC1B;IAEA8B,8BAA+BF,CAAAA,UAAAA,CAAAA;AAC/BG,IAAAA,mBAAAA,CAAoBH,WAAWI,QAAQ,CAAA;AAEvC,IAAA,OAAOxB,MAAOC,CAAAA,EAAE,CAACyB,WAAW,CAAC,UAAA;QAC3B,MAAMoB,YAAAA,GAAe,MAAM9C,MAAOC,CAAAA,EAAE,CAACC,KAAK,CAACR,kBAAoB+C,CAAAA,CAAAA,MAAM,CAAC;YACpErC,MAAQR,EAAAA,aAAAA;YACRiD,KAAO,EAAA;AAAEH,gBAAAA;AAAG,aAAA;YACZd,IAAM,EAAA;gBACJ,GAAGC,OAAAA,CAAK,eAAeT,UAAW;AACpC;AACF,SAAA,CAAA;QAEA,IAAIA,UAAAA,CAAWe,WAAW,EAAE;YAC1B,MAAMY,wBAAAA,GAA2B,MAAM/C,MAAAA,CAAOC,EAAE,CAC7CC,KAAK,CAACR,kBAAAA,CAAAA,CACN4C,IAAI,CAACQ,YAAc,EAAA,aAAA,CAAA;AAEtB,YAAA,MAAMT,kBAAqB7B,GAAAA,MAAAA,CAAI,QAAUuC,EAAAA,wBAAAA,IAA4B,EAAE,CAAA;YACvE,MAAMC,cAAAA,GAAiBd,OAAKd,CAAAA,UAAAA,CAAWe,WAAW,CAAA;YAElD,MAAMc,eAAAA,GAAkBC,cAAWb,kBAAoBW,EAAAA,cAAAA,CAAAA;YACvD,MAAMG,YAAAA,GAAeD,cAAWF,cAAgBX,EAAAA,kBAAAA,CAAAA;;;AAIhD,YAAA,MAAML,OAAQC,CAAAA,GAAG,CACfgB,eAAAA,CAAgBzC,GAAG,CAAC,CAAC4B,MACnBpC,GAAAA,MAAAA,CAAOC,EAAE,CAACC,KAAK,CAACP,6BAAAA,CAAAA,CAA+ByD,MAAM,CAAC;oBACpDP,KAAO,EAAA;AAAET,wBAAAA,MAAAA;wBAAQ3B,KAAOiC,EAAAA;AAAG;AAC7B,iBAAA,CAAA,CAAA,CAAA;;;AAMJ,YAAA,MAAMV,OAAQC,CAAAA,GAAG,CACfkB,YAAAA,CAAa3C,GAAG,CAAC,CAAC4B,MAChBpC,GAAAA,MAAAA,CAAOC,EAAE,CAACC,KAAK,CAACP,6BAAAA,CAAAA,CAA+B0B,MAAM,CAAC;oBACpDO,IAAM,EAAA;AAAEQ,wBAAAA,MAAAA;wBAAQ3B,KAAOiC,EAAAA;AAAG;AAC5B,iBAAA,CAAA,CAAA,CAAA;AAGN;;QAGA,MAAMW,iBAAAA,GAA+C,MAAMrD,MAAAA,CAAOC,EAAE,CACjEC,KAAK,CAACR,kBAAAA,CAAAA,CACN4C,IAAI,CAACQ,YAAc,EAAA,aAAA,CAAA;QAEtB,OAAO;AACL,YAAA,GAAGA,YAAY;YACfX,WAAakB,EAAAA,iBAAAA,GAAoBA,kBAAkB7C,GAAG,CAAC,CAAC8C,CAAMA,GAAAA,CAAAA,CAAElB,MAAM,CAAImB,GAAAA;AAC5E,SAAA;AACF,KAAA,CAAA;AACF;AAEA;;IAGA,MAAMC,SAAS,OAAOd,EAAAA,GAAAA;AACpB,IAAA,OAAO1C,MAAOC,CAAAA,EAAE,CAACyB,WAAW,CAAC,UAC3B1B,MAAOC,CAAAA,EAAE,CACNC,KAAK,CAACR,kBAAAA,CAAAA,CACN0D,MAAM,CAAC;YAAEhD,MAAQR,EAAAA,aAAAA;YAAeS,QAAUR,EAAAA,eAAAA;YAAiBgD,KAAO,EAAA;AAAEH,gBAAAA;AAAG;AAAE,SAAA,CAAA,CAAA;AAEhF;AAEA;;AAEC,IACKe,MAAAA,KAAAA,GAAQ,OACZC,WAAAA,GAAc,EAMb,GAAA;AAED,IAAA,IAAInB,OAAOoB,IAAI,CAACD,WAAaxC,CAAAA,CAAAA,MAAM,KAAK,CAAG,EAAA;QACzC,OAAO,IAAA;AACT;IAEA,MAAMT,KAAAA,GAAQ,MAAMT,MAAOC,CAAAA,EAAE,CAC1BC,KAAK,CAACR,kBACNkD,CAAAA,CAAAA,OAAO,CAAC;QAAExC,MAAQR,EAAAA,aAAAA;QAAeS,QAAUR,EAAAA,eAAAA;QAAiBgD,KAAOa,EAAAA;AAAY,KAAA,CAAA;AAElF,IAAA,IAAI,CAACjD,KAAO,EAAA;QACV,OAAOA,KAAAA;AACT;AAEA,IAAA,OAAOC,uBAAwBD,CAAAA,KAAAA,CAAAA;AACjC;AAEA;;IAGA,MAAMmD,UAAU,OAAOlB,EAAAA,GAAAA;AACrB,IAAA,OAAOe,KAAM,CAAA;AAAEf,QAAAA;AAAG,KAAA,CAAA;AACpB;AAEA;;IAGA,MAAMmB,YAAY,OAAOtD,IAAAA,GAAAA;AACvB,IAAA,OAAOkD,KAAM,CAAA;AAAElD,QAAAA;AAAK,KAAA,CAAA;AACtB;AAEA;;AAEC,IACKuD,MAAAA,MAAAA,GAAS,OACbJ,WAAAA,GAAc,EAMb,GAAA;IAED,MAAM/B,aAAAA,GAAgB,MAAM8B,KAAMC,CAAAA,WAAAA,CAAAA;AAElC,IAAA,OAAO,CAAC,CAAC/B,aAAAA;AACX;AAEA,MAAMoC,aAAa,OAAOrB,EAAAA,GAAAA;AACxB,IAAA,MAAM1B,YAAYJ,MAAOC,CAAAA,WAAW,CAAC,GAAA,CAAA,CAAKC,QAAQ,CAAC,KAAA,CAAA;AACnD,IAAA,MAAMa,aAAiB,GAAA,MAAM3B,MAAOC,CAAAA,EAAE,CAACyB,WAAW,CAAC,UACjD1B,MAAAA,CAAOC,EAAE,CAACC,KAAK,CAACR,kBAAAA,CAAAA,CAAoB+C,MAAM,CAAC;YACzCrC,MAAQ,EAAA;AAAC,gBAAA,IAAA;AAAM,gBAAA;AAAY,aAAA;YAC3ByC,KAAO,EAAA;AAAEH,gBAAAA;AAAG,aAAA;YACZd,IAAM,EAAA;AACJZ,gBAAAA,SAAAA,EAAWc,IAAKd,CAAAA,SAAAA;AAClB;AACF,SAAA,CAAA,CAAA;AAGF,IAAA,IAAI,CAACW,aAAe,EAAA;AAClB,QAAA,MAAM,IAAInC,aAAc,CAAA,sCAAA,CAAA;AAC1B;IAEA,OAAO;AACL,QAAA,GAAGmC,aAAa;AAChBX,QAAAA;AACF,KAAA;AACF;AAEA,MAAMe,sBAAsB,CAACP,QAAAA,GAAAA;;AAE3B,IAAA,MAAMwC,gBAAgBC,WAASzC,CAAAA,QAAAA,CAAAA,IAAa0C,OAAOC,QAAQ,CAAC3C,aAAaA,QAAW,GAAA,CAAA;AACpF,IAAA,IAAI,CAACwC,aAAAA,IAAiB,CAACI,QAAAA,CAAM5C,QAAW,CAAA,EAAA;AACtC,QAAA,MAAM,IAAIjC,eAAgB,CAAA,4CAAA,CAAA;AAC5B;IAEA,OAAO;AACLiC,QAAAA,QAAAA,EAAUA,QAAY,IAAA,IAAA;AACtB6C,QAAAA,SAAAA,EAAW7C,QAAW8C,GAAAA,IAAAA,CAAKC,GAAG,EAAA,GAAK/C,QAAW,GAAA;AAChD,KAAA;AACF,CAAA;AAEA;;IAGA,MAAMM,OAAO,CAACd,SAAAA,GAAAA;AACZ,IAAA,MAAM,EAAEwD,iBAAiB,EAAE,GAAGC,gBAAAA,CAAW,YAAYC,KAAK;AAE1D,IAAA,IAAI,CAACF,iBAAqB,EAAA,EAAA;AACxB,QAAA,MAAM,IAAIG,SAAU,CAAA,oCAAA,CAAA;AACtB;AAEA,IAAA,OAAO/D,MACJgE,CAAAA,UAAU,CAAC,QAAA,EAAU5E,OAAO6E,MAAM,CAACC,GAAG,CAAC,2BACvCrC,CAAAA,CAAAA,CAAAA,MAAM,CAACzB,SAAAA,CAAAA,CACP+D,MAAM,CAAC,KAAA,CAAA;AACZ;AAEA,MAAMC,kBAAqB,GAAA,IAAA;AACzB,IAAA,MAAM,EAAER,iBAAiB,EAAE,GAAGC,gBAAAA,CAAW,YAAYC,KAAK;;AAG1D,IAAA,IAAI,CAAC1E,MAAO6E,CAAAA,MAAM,CAACC,GAAG,CAAC,gCAAmC,CAAA,EAAA;AACxD,QAAA;AACF;AAEA,IAAA,IAAI,CAACN,iBAAqB,EAAA,EAAA;QACxBS,OAAQC,CAAAA,WAAW,CACjB,CAAC;;uQAEgQ,CAAC,CAAA;AAEtQ;AACF;AAEA;;IAGA,MAAMxE,0BAA0B,CAACD,KAAAA,GAAAA;AAC/B,IAAA,IAAI,CAACA,KAAO,EAAA;QACV,OAAOA,KAAAA;AACT;IAEA,OAAO;AACL,QAAA,GAAGA,KAAK;QACR0B,WAAagD,EAAAA,UAAAA,CAAQ1E,KAAM0B,CAAAA,WAAW,CAClC3B,GAAAA,MAAAA,CAAI,UAAUC,KAAM0B,CAAAA,WAAW,CAC/B1B,GAAAA,KAAAA,CAAM0B;AACZ,KAAA;AACF,CAAA;AAEA;;IAGA,MAAMb,iCAAiC,CAACF,UAAAA,GAAAA;AACtC,IAAA,MAAMgE,iBAAoBpF,GAAAA,MAAAA,CAAOqF,OAAO,CAAC,mBAAmBC,UAAU;AACtE,IAAA,MAAMC,mBAAmBH,iBAAkBI,CAAAA,SAAS,CAACpD,MAAM,CAACuB,IAAI,EAAA;AAChE,IAAA,MAAM8B,kBAAqBvC,GAAAA,aAAAA,CAAW9B,UAAWe,CAAAA,WAAW,EAAEoD,gBAAAA,CAAAA;IAE9D,IAAI,CAACG,WAAQD,kBAAqB,CAAA,EAAA;QAChC,MAAM,IAAIlG,gBAAgB,CAAC,8BAA8B,EAAEkG,kBAAmBE,CAAAA,IAAI,CAAC,IAAA,CAAA,CAAM,CAAC,CAAA;AAC5F;AACF,CAAA;AAEA;;IAGA,MAAMC,kBAAkB,CAACpE,QAAAA,GAAAA;AACvB,IAAA,IAAI4C,SAAM5C,QAAW,CAAA,EAAA;QACnB,OAAO,IAAA;AACT;AAEA,IAAA,IACE,CAACyC,WAAAA,CAASzC,QACV,CAAA,IAAA,CAACe,MAAOsD,CAAAA,MAAM,CAACC,SAAAA,CAAUC,wBAAwB,CAAA,CAAEC,QAAQ,CAACxE,QAC5D,CAAA,EAAA;QACA,OAAO,KAAA;AACT;IAEA,OAAO,IAAA;AACT,CAAA;AAEA;;IAGA,MAAMD,sBAAsB,CAACC,QAAAA,GAAAA;IAC3B,IAAI,CAACoE,gBAAgBpE,QAAW,CAAA,EAAA;QAC9B,MAAM,IAAIjC,gBACR,CAAC;MACD,EAAEgD,MAAAA,CAAOsD,MAAM,CAACC,SAAAA,CAAUC,wBAAwB,CAAEJ,CAAAA,IAAI,CAAC,IAAA,CAAA,CAAM,CAAC,CAAA;AAEpE;AACF,CAAA;;;;;;;;;;;;;;;"}