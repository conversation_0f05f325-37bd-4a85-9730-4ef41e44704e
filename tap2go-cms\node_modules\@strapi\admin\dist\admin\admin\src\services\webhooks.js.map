{"version": 3, "file": "webhooks.js", "sources": ["../../../../../admin/src/services/webhooks.ts"], "sourcesContent": ["import * as Webhooks from '../../../shared/contracts/webhooks';\n\nimport { adminApi } from './api';\n\nconst webhooksSerivce = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['Webhook'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      getWebhooks: builder.query<\n        Webhooks.GetWebhooks.Response['data'],\n        Webhooks.GetWebhook.Params | void\n      >({\n        query: (args) => ({\n          url: `/admin/webhooks/${args?.id ?? ''}`,\n          method: 'GET',\n        }),\n        transformResponse: (\n          response: Webhooks.GetWebhooks.Response | Webhooks.GetWebhook.Response\n        ) => {\n          if (Array.isArray(response.data)) {\n            return response.data;\n          } else {\n            return [response.data];\n          }\n        },\n        providesTags: (res, _err, arg) => {\n          if (typeof arg === 'object' && 'id' in arg) {\n            return [{ type: 'Webhook' as const, id: arg.id }];\n          } else {\n            return [\n              ...(res?.map(({ id }) => ({ type: 'Webhook' as const, id })) ?? []),\n              { type: 'Webhook' as const, id: 'LIST' },\n            ];\n          }\n        },\n      }),\n      createWebhook: builder.mutation<\n        Webhooks.CreateWebhook.Response['data'],\n        Omit<Webhooks.CreateWebhook.Request['body'], 'id' | 'isEnabled'>\n      >({\n        query: (body) => ({\n          url: `/admin/webhooks`,\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: Webhooks.CreateWebhook.Response) => response.data,\n        invalidatesTags: [{ type: 'Webhook', id: 'LIST' }],\n      }),\n      updateWebhook: builder.mutation<\n        Webhooks.UpdateWebhook.Response['data'],\n        Webhooks.UpdateWebhook.Request['body'] & Webhooks.UpdateWebhook.Params\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/webhooks/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (response: Webhooks.UpdateWebhook.Response) => response.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'Webhook', id }],\n      }),\n      triggerWebhook: builder.mutation<\n        Webhooks.TriggerWebhook.Response['data'],\n        Webhooks.TriggerWebhook.Params['id']\n      >({\n        query: (webhookId) => ({\n          url: `/admin/webhooks/${webhookId}/trigger`,\n          method: 'POST',\n        }),\n        transformResponse: (response: Webhooks.TriggerWebhook.Response) => response.data,\n      }),\n      deleteManyWebhooks: builder.mutation<\n        Webhooks.DeleteWebhooks.Response['data'],\n        Webhooks.DeleteWebhooks.Request['body']\n      >({\n        query: (body) => ({\n          url: `/admin/webhooks/batch-delete`,\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: Webhooks.DeleteWebhooks.Response) => response.data,\n        invalidatesTags: (_res, _err, { ids }) => ids.map((id) => ({ type: 'Webhook', id })),\n      }),\n    }),\n    overrideExisting: false,\n  });\n\nconst {\n  useGetWebhooksQuery,\n  useCreateWebhookMutation,\n  useUpdateWebhookMutation,\n  useTriggerWebhookMutation,\n  useDeleteManyWebhooksMutation,\n} = webhooksSerivce;\n\nexport {\n  useGetWebhooksQuery,\n  useCreateWebhookMutation,\n  useUpdateWebhookMutation,\n  useTriggerWebhookMutation,\n  useDeleteManyWebhooksMutation,\n};\n"], "names": ["webhooksSerivce", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "getWebhooks", "query", "args", "url", "id", "method", "transformResponse", "response", "Array", "isArray", "data", "providesTags", "res", "_err", "arg", "type", "map", "createWebhook", "mutation", "body", "invalidatesTags", "updateWebhook", "_res", "triggerWebhook", "webhookId", "deleteManyWebhooks", "ids", "overrideExisting", "useGetWebhooksQuery", "useCreateWebhookMutation", "useUpdateWebhookMutation", "useTriggerWebhookMutation", "useDeleteManyWebhooksMutation"], "mappings": ";;;;AAIA,MAAMA,eAAAA,GAAkBC,YACrBC,CAAAA,gBAAgB,CAAC;IAChBC,WAAa,EAAA;AAAC,QAAA;AAAU;AAC1B,CAAA,CAAA,CACCC,eAAe,CAAC;IACfC,SAAW,EAAA,CAACC,WAAa;YACvBC,WAAaD,EAAAA,OAAAA,CAAQE,KAAK,CAGxB;gBACAA,KAAO,EAAA,CAACC,QAAU;AAChBC,wBAAAA,GAAAA,EAAK,CAAC,gBAAgB,EAAED,IAAME,EAAAA,EAAAA,IAAM,GAAG,CAAC;wBACxCC,MAAQ,EAAA;qBACV,CAAA;AACAC,gBAAAA,iBAAAA,EAAmB,CACjBC,QAAAA,GAAAA;AAEA,oBAAA,IAAIC,KAAMC,CAAAA,OAAO,CAACF,QAAAA,CAASG,IAAI,CAAG,EAAA;AAChC,wBAAA,OAAOH,SAASG,IAAI;qBACf,MAAA;wBACL,OAAO;AAACH,4BAAAA,QAAAA,CAASG;AAAK,yBAAA;AACxB;AACF,iBAAA;gBACAC,YAAc,EAAA,CAACC,KAAKC,IAAMC,EAAAA,GAAAA,GAAAA;AACxB,oBAAA,IAAI,OAAOA,GAAAA,KAAQ,QAAY,IAAA,IAAA,IAAQA,GAAK,EAAA;wBAC1C,OAAO;AAAC,4BAAA;gCAAEC,IAAM,EAAA,SAAA;AAAoBX,gCAAAA,EAAAA,EAAIU,IAAIV;AAAG;AAAE,yBAAA;qBAC5C,MAAA;wBACL,OAAO;AACDQ,4BAAAA,GAAAA,GAAAA,EAAKI,IAAI,CAAC,EAAEZ,EAAE,EAAE,IAAM;oCAAEW,IAAM,EAAA,SAAA;AAAoBX,oCAAAA;AAAG,iCAAA,MAAO,EAAE;AAClE,4BAAA;gCAAEW,IAAM,EAAA,SAAA;gCAAoBX,EAAI,EAAA;AAAO;AACxC,yBAAA;AACH;AACF;AACF,aAAA,CAAA;YACAa,aAAelB,EAAAA,OAAAA,CAAQmB,QAAQ,CAG7B;gBACAjB,KAAO,EAAA,CAACkB,QAAU;wBAChBhB,GAAK,EAAA,CAAC,eAAe,CAAC;wBACtBE,MAAQ,EAAA,MAAA;wBACRK,IAAMS,EAAAA;qBACR,CAAA;gBACAb,iBAAmB,EAAA,CAACC,QAA8CA,GAAAA,QAAAA,CAASG,IAAI;gBAC/EU,eAAiB,EAAA;AAAC,oBAAA;wBAAEL,IAAM,EAAA,SAAA;wBAAWX,EAAI,EAAA;AAAO;AAAE;AACpD,aAAA,CAAA;YACAiB,aAAetB,EAAAA,OAAAA,CAAQmB,QAAQ,CAG7B;AACAjB,gBAAAA,KAAAA,EAAO,CAAC,EAAEG,EAAE,EAAE,GAAGe,IAAAA,EAAM,IAAM;AAC3BhB,wBAAAA,GAAAA,EAAK,CAAC,gBAAgB,EAAEC,EAAAA,CAAG,CAAC;wBAC5BC,MAAQ,EAAA,KAAA;wBACRK,IAAMS,EAAAA;qBACR,CAAA;gBACAb,iBAAmB,EAAA,CAACC,QAA8CA,GAAAA,QAAAA,CAASG,IAAI;AAC/EU,gBAAAA,eAAAA,EAAiB,CAACE,IAAMT,EAAAA,IAAAA,EAAM,EAAET,EAAE,EAAE,GAAK;AAAC,wBAAA;4BAAEW,IAAM,EAAA,SAAA;AAAWX,4BAAAA;AAAG;AAAE;AACpE,aAAA,CAAA;YACAmB,cAAgBxB,EAAAA,OAAAA,CAAQmB,QAAQ,CAG9B;gBACAjB,KAAO,EAAA,CAACuB,aAAe;AACrBrB,wBAAAA,GAAAA,EAAK,CAAC,gBAAgB,EAAEqB,SAAAA,CAAU,QAAQ,CAAC;wBAC3CnB,MAAQ,EAAA;qBACV,CAAA;gBACAC,iBAAmB,EAAA,CAACC,QAA+CA,GAAAA,QAAAA,CAASG;AAC9E,aAAA,CAAA;YACAe,kBAAoB1B,EAAAA,OAAAA,CAAQmB,QAAQ,CAGlC;gBACAjB,KAAO,EAAA,CAACkB,QAAU;wBAChBhB,GAAK,EAAA,CAAC,4BAA4B,CAAC;wBACnCE,MAAQ,EAAA,MAAA;wBACRK,IAAMS,EAAAA;qBACR,CAAA;gBACAb,iBAAmB,EAAA,CAACC,QAA+CA,GAAAA,QAAAA,CAASG,IAAI;AAChFU,gBAAAA,eAAAA,EAAiB,CAACE,IAAAA,EAAMT,IAAM,EAAA,EAAEa,GAAG,EAAE,GAAKA,GAAAA,CAAIV,GAAG,CAAC,CAACZ,EAAAA,IAAQ;4BAAEW,IAAM,EAAA,SAAA;AAAWX,4BAAAA;yBAAG,CAAA;AACnF,aAAA;SACF,CAAA;IACAuB,gBAAkB,EAAA;AACpB,CAAA,CAAA;AAEI,MAAA,EACJC,mBAAmB,EACnBC,wBAAwB,EACxBC,wBAAwB,EACxBC,yBAAyB,EACzBC,6BAA6B,EAC9B,GAAGvC;;;;;;;;"}