declare const EASINGS: {
    easeInSine: string;
    easeOutSine: string;
    easeInOutSine: string;
    easeInQuad: string;
    easeOutQuad: string;
    easeInOutQuad: string;
    easeInCubic: string;
    easeOutCubic: string;
    easeInOutCubic: string;
    easeInQuart: string;
    easeOutQuart: string;
    easeInOutQuart: string;
    easeInQuint: string;
    easeOutQuint: string;
    easeInOutQuint: string;
    easeInExpo: string;
    easeOutExpo: string;
    easeInOutExpo: string;
    easeInCirc: string;
    easeOutCirc: string;
    easeInOutCirc: string;
    easeInBack: string;
    easeOutBack: string;
    easeInOutBack: string;
    easeInOutFast: string;
    authenticMotion: string;
};
declare const TIMINGS: {
    '320': string;
    '200': string;
    '120': string;
};
/**
 * A collection of common transitions attached to the theme to be used.
 */
declare const TRANSITIONS: {
    color: string;
    backgroundColor: string;
};
declare const ANIMATIONS: {
    overlayFadeIn: import("styled-components/dist/models/Keyframes").default;
    modalPopIn: import("styled-components/dist/models/Keyframes").default;
    modalPopOut: import("styled-components/dist/models/Keyframes").default;
    popIn: import("styled-components/dist/models/Keyframes").default;
    popOut: import("styled-components/dist/models/Keyframes").default;
    slideDownIn: import("styled-components/dist/models/Keyframes").default;
    slideDownOut: import("styled-components/dist/models/Keyframes").default;
    slideUpIn: import("styled-components/dist/models/Keyframes").default;
    slideUpOut: import("styled-components/dist/models/Keyframes").default;
    fadeIn: import("styled-components/dist/models/Keyframes").default;
    fadeOut: import("styled-components/dist/models/Keyframes").default;
};
export { ANIMATIONS, EASINGS, TRANSITIONS, TIMINGS };
//# sourceMappingURL=motion.d.ts.map