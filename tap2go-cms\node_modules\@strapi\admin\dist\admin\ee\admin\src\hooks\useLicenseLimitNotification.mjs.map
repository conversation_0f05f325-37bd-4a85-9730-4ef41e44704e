{"version": 3, "file": "useLicenseLimitNotification.mjs", "sources": ["../../../../../../ee/admin/src/hooks/useLicenseLimitNotification.ts"], "sourcesContent": ["/**\n *\n * useLicenseLimitNotification\n *\n */\nimport * as React from 'react';\n\nimport isNil from 'lodash/isNil';\nimport { useIntl } from 'react-intl';\nimport { useLocation } from 'react-router-dom';\n\nimport { NotificationConfig, useNotification } from '../../../../admin/src/features/Notifications';\n\nimport { useLicenseLimits } from './useLicenseLimits';\n\nconst STORAGE_KEY_PREFIX = 'strapi-notification-seat-limit';\n\nconst BILLING_SELF_HOSTED_URL = 'https://strapi.io/billing/request-seats';\nconst MANAGE_SEATS_URL = 'https://strapi.io/billing/manage-seats';\n\nexport const useLicenseLimitNotification = () => {\n  const { formatMessage } = useIntl();\n  const { license, isError, isLoading } = useLicenseLimits();\n  const { toggleNotification } = useNotification();\n  const { pathname } = useLocation();\n\n  const { enforcementUserCount, permittedSeats, licenseLimitStatus, type } = license ?? {};\n\n  React.useEffect(() => {\n    if (isError || isLoading) {\n      return;\n    }\n\n    const shouldDisplayNotification =\n      !isNil(permittedSeats) &&\n      !window.sessionStorage.getItem(`${STORAGE_KEY_PREFIX}-${pathname}`) &&\n      licenseLimitStatus === 'OVER_LIMIT';\n\n    let notificationType: NotificationConfig['type'];\n\n    if (licenseLimitStatus === 'OVER_LIMIT') {\n      notificationType = 'danger';\n    }\n\n    if (shouldDisplayNotification) {\n      toggleNotification({\n        type: notificationType,\n        message: formatMessage(\n          {\n            id: 'notification.ee.warning.over-.message',\n            defaultMessage:\n              \"Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} other {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app.\",\n          },\n          { licenseLimitStatus }\n        ),\n        title: formatMessage(\n          {\n            id: 'notification.ee.warning.at-seat-limit.title',\n            defaultMessage:\n              '{licenseLimitStatus, select, OVER_LIMIT {Over} other {At}} seat limit ({enforcementUserCount}/{permittedSeats})',\n          },\n          {\n            licenseLimitStatus,\n            enforcementUserCount,\n            permittedSeats,\n          }\n        ),\n        link: {\n          url: type === 'gold' ? BILLING_SELF_HOSTED_URL : MANAGE_SEATS_URL,\n          label: formatMessage({\n            id: 'notification.ee.warning.seat-limit.link',\n            defaultMessage: type === 'gold' ? 'Contact sales' : 'Manage seats',\n          }),\n        },\n        blockTransition: true,\n        onClose() {\n          window.sessionStorage.setItem(`${STORAGE_KEY_PREFIX}-${pathname}`, 'true');\n        },\n      });\n    }\n  }, [\n    toggleNotification,\n    license,\n    pathname,\n    formatMessage,\n    isLoading,\n    permittedSeats,\n    licenseLimitStatus,\n    enforcementUserCount,\n    isError,\n    type,\n  ]);\n};\n"], "names": ["STORAGE_KEY_PREFIX", "BILLING_SELF_HOSTED_URL", "MANAGE_SEATS_URL", "useLicenseLimitNotification", "formatMessage", "useIntl", "license", "isError", "isLoading", "useLicenseLimits", "toggleNotification", "useNotification", "pathname", "useLocation", "enforcementUserCount", "permittedSeats", "licenseLimitStatus", "type", "React", "useEffect", "shouldDisplayNotification", "isNil", "window", "sessionStorage", "getItem", "notificationType", "message", "id", "defaultMessage", "title", "link", "url", "label", "blockTransition", "onClose", "setItem"], "mappings": ";;;;;;;AAeA,MAAMA,kBAAqB,GAAA,gCAAA;AAE3B,MAAMC,uBAA0B,GAAA,yCAAA;AAChC,MAAMC,gBAAmB,GAAA,wCAAA;MAEZC,2BAA8B,GAAA,IAAA;IACzC,MAAM,EAAEC,aAAa,EAAE,GAAGC,OAAAA,EAAAA;AAC1B,IAAA,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAGC,gBAAAA,EAAAA;IACxC,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,eAAAA,EAAAA;IAC/B,MAAM,EAAEC,QAAQ,EAAE,GAAGC,WAAAA,EAAAA;IAErB,MAAM,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,IAAI,EAAE,GAAGX,OAAAA,IAAW,EAAC;AAEvFY,IAAAA,KAAAA,CAAMC,SAAS,CAAC,IAAA;AACd,QAAA,IAAIZ,WAAWC,SAAW,EAAA;AACxB,YAAA;AACF;AAEA,QAAA,MAAMY,4BACJ,CAACC,KAAAA,CAAMN,mBACP,CAACO,MAAAA,CAAOC,cAAc,CAACC,OAAO,CAAC,CAAC,EAAExB,kBAAmB,CAAA,CAAC,EAAEY,QAAS,CAAA,CAAC,KAClEI,kBAAuB,KAAA,YAAA;QAEzB,IAAIS,gBAAAA;AAEJ,QAAA,IAAIT,uBAAuB,YAAc,EAAA;YACvCS,gBAAmB,GAAA,QAAA;AACrB;AAEA,QAAA,IAAIL,yBAA2B,EAAA;YAC7BV,kBAAmB,CAAA;gBACjBO,IAAMQ,EAAAA,gBAAAA;AACNC,gBAAAA,OAAAA,EAAStB,aACP,CAAA;oBACEuB,EAAI,EAAA,uCAAA;oBACJC,cACE,EAAA;iBAEJ,EAAA;AAAEZ,oBAAAA;AAAmB,iBAAA,CAAA;AAEvBa,gBAAAA,KAAAA,EAAOzB,aACL,CAAA;oBACEuB,EAAI,EAAA,6CAAA;oBACJC,cACE,EAAA;iBAEJ,EAAA;AACEZ,oBAAAA,kBAAAA;AACAF,oBAAAA,oBAAAA;AACAC,oBAAAA;AACF,iBAAA,CAAA;gBAEFe,IAAM,EAAA;oBACJC,GAAKd,EAAAA,IAAAA,KAAS,SAAShB,uBAA0BC,GAAAA,gBAAAA;AACjD8B,oBAAAA,KAAAA,EAAO5B,aAAc,CAAA;wBACnBuB,EAAI,EAAA,yCAAA;wBACJC,cAAgBX,EAAAA,IAAAA,KAAS,SAAS,eAAkB,GAAA;AACtD,qBAAA;AACF,iBAAA;gBACAgB,eAAiB,EAAA,IAAA;AACjBC,gBAAAA,OAAAA,CAAAA,GAAAA;oBACEZ,MAAOC,CAAAA,cAAc,CAACY,OAAO,CAAC,CAAC,EAAEnC,kBAAAA,CAAmB,CAAC,EAAEY,QAAS,CAAA,CAAC,EAAE,MAAA,CAAA;AACrE;AACF,aAAA,CAAA;AACF;KACC,EAAA;AACDF,QAAAA,kBAAAA;AACAJ,QAAAA,OAAAA;AACAM,QAAAA,QAAAA;AACAR,QAAAA,aAAAA;AACAI,QAAAA,SAAAA;AACAO,QAAAA,cAAAA;AACAC,QAAAA,kBAAAA;AACAF,QAAAA,oBAAAA;AACAP,QAAAA,OAAAA;AACAU,QAAAA;AACD,KAAA,CAAA;AACH;;;;"}