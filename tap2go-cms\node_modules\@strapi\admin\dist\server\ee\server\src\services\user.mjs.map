{"version": 3, "file": "user.mjs", "sources": ["../../../../../../ee/server/src/services/user.ts"], "sourcesContent": ["import _ from 'lodash';\nimport { pipe, map, castArray, toNumber } from 'lodash/fp';\nimport { arrays, errors } from '@strapi/utils';\nimport { hasSuperAdminRole } from '../../../../server/src/domain/user';\nimport constants from '../../../../server/src/services/constants';\nimport { getService } from '../utils';\n\nconst { ValidationError } = errors;\nconst { SUPER_ADMIN_CODE } = constants;\n\n/** Checks if ee disabled users list needs to be updated\n * @param {string} id\n * @param {object} input\n */\nconst updateEEDisabledUsersList = async (id: string, input: any) => {\n  const disabledUsers = await getService('seat-enforcement').getDisabledUserList();\n\n  if (!disabledUsers) {\n    return;\n  }\n\n  const user = disabledUsers.find((user: any) => user.id === Number(id));\n  if (!user) {\n    return;\n  }\n\n  if (user.isActive !== input.isActive) {\n    const newDisabledUsersList = disabledUsers.filter((user: any) => user.id !== Number(id));\n    await strapi.store.set({\n      type: 'ee',\n      key: 'disabled_users',\n      value: newDisabledUsersList,\n    });\n  }\n};\n\nconst castNumberArray = pipe(castArray, map(toNumber));\n\nconst removeFromEEDisabledUsersList = async (ids: unknown) => {\n  let idsToCheck: any;\n  if (typeof ids === 'object') {\n    idsToCheck = castNumberArray(ids);\n  } else {\n    idsToCheck = [Number(ids)];\n  }\n\n  const disabledUsers = await getService('seat-enforcement').getDisabledUserList();\n\n  if (!disabledUsers) {\n    return;\n  }\n\n  const newDisabledUsersList = disabledUsers.filter((user: any) => !idsToCheck.includes(user.id));\n  await strapi.store.set({\n    type: 'ee',\n    key: 'disabled_users',\n    value: newDisabledUsersList,\n  });\n};\n\n/**\n * Update a user in database\n * @param id query params to find the user to update\n * @param attributes A partial user object\n * @returns {Promise<user>}\n */\nconst updateById = async (id: any, attributes: any) => {\n  // Check at least one super admin remains\n  if (_.has(attributes, 'roles')) {\n    const lastAdminUser = await isLastSuperAdminUser(id);\n    const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n    const willRemoveSuperAdminRole = !arrays.includesString(attributes.roles, superAdminRole.id);\n\n    if (lastAdminUser && willRemoveSuperAdminRole) {\n      throw new ValidationError('You must have at least one user with super admin role.');\n    }\n  }\n\n  // cannot disable last super admin\n  if (attributes.isActive === false) {\n    const lastAdminUser = await isLastSuperAdminUser(id);\n    if (lastAdminUser) {\n      throw new ValidationError('You must have at least one user with super admin role.');\n    }\n  }\n\n  // hash password if a new one is sent\n  if (_.has(attributes, 'password')) {\n    const hashedPassword = await getService('auth').hashPassword(attributes.password);\n\n    const updatedUser = await strapi.db.query('admin::user').update({\n      where: { id },\n      data: {\n        ...attributes,\n        password: hashedPassword,\n      },\n      populate: ['roles'],\n    });\n\n    strapi.eventHub.emit('user.update', { user: sanitizeUser(updatedUser) });\n\n    return updatedUser;\n  }\n\n  const updatedUser = await strapi.db.query('admin::user').update({\n    where: { id },\n    data: attributes,\n    populate: ['roles'],\n  });\n\n  await updateEEDisabledUsersList(id, attributes);\n\n  if (updatedUser) {\n    strapi.eventHub.emit('user.update', { user: sanitizeUser(updatedUser) });\n  }\n\n  return updatedUser;\n};\n\n/** Delete a user\n * @param id id of the user to delete\n * @returns {Promise<user>}\n */\nconst deleteById = async (id: unknown) => {\n  // Check at least one super admin remains\n  const userToDelete = await strapi.db.query('admin::user').findOne({\n    where: { id },\n    populate: ['roles'],\n  });\n\n  if (!userToDelete) {\n    return null;\n  }\n\n  if (userToDelete) {\n    if (userToDelete.roles.some((r: any) => r.code === SUPER_ADMIN_CODE)) {\n      const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n      if (superAdminRole.usersCount === 1) {\n        throw new ValidationError('You must have at least one user with super admin role.');\n      }\n    }\n  }\n\n  const deletedUser = await strapi.db\n    .query('admin::user')\n    .delete({ where: { id }, populate: ['roles'] });\n\n  await removeFromEEDisabledUsersList(id);\n\n  strapi.eventHub.emit('user.delete', { user: sanitizeUser(deletedUser) });\n\n  return deletedUser;\n};\n\n/** Delete a user\n * @param ids ids of the users to delete\n * @returns {Promise<user>}\n */\nconst deleteByIds = async (ids: any) => {\n  // Check at least one super admin remains\n  const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n  const nbOfSuperAdminToDelete = await strapi.db.query('admin::user').count({\n    where: {\n      id: ids,\n      roles: { id: superAdminRole.id },\n    },\n  });\n\n  if (superAdminRole.usersCount === nbOfSuperAdminToDelete) {\n    throw new ValidationError('You must have at least one user with super admin role.');\n  }\n\n  const deletedUsers = [];\n  for (const id of ids) {\n    const deletedUser = await strapi.db.query('admin::user').delete({\n      where: { id },\n      populate: ['roles'],\n    });\n\n    deletedUsers.push(deletedUser);\n  }\n\n  await removeFromEEDisabledUsersList(ids);\n\n  strapi.eventHub.emit('user.delete', {\n    users: deletedUsers.map((deletedUser) => sanitizeUser(deletedUser)),\n  });\n\n  return deletedUsers;\n};\n\nconst sanitizeUserRoles = (role: unknown) => _.pick(role, ['id', 'name', 'description', 'code']);\n\n/**\n * Check if a user is the last super admin\n * @param {int|string} userId user's id to look for\n */\nconst isLastSuperAdminUser = async (userId: unknown) => {\n  const user = (await findOne(userId)) as any;\n  const superAdminRole = await getService('role').getSuperAdminWithUsersCount();\n\n  return superAdminRole.usersCount === 1 && hasSuperAdminRole(user);\n};\n\n/**\n * Remove private user fields\n * @param {Object} user - user to sanitize\n */\nconst sanitizeUser = (user: any) => {\n  return {\n    ..._.omit(user, ['password', 'resetPasswordToken', 'registrationToken', 'roles']),\n    roles: user.roles && user.roles.map(sanitizeUserRoles),\n  };\n};\n\n/**\n * Find one user\n */\nconst findOne = async (id: any, populate = ['roles']) => {\n  return strapi.db.query('admin::user').findOne({ where: { id }, populate });\n};\n\nconst getCurrentActiveUserCount = async () => {\n  return strapi.db.query('admin::user').count({ where: { isActive: true } });\n};\n\nexport default {\n  updateEEDisabledUsersList,\n  removeFromEEDisabledUsersList,\n  getCurrentActiveUserCount,\n  deleteByIds,\n  deleteById,\n  updateById,\n};\n"], "names": ["ValidationError", "errors", "SUPER_ADMIN_CODE", "constants", "updateEEDisabledUsersList", "id", "input", "disabledUsers", "getService", "getDisabledUserList", "user", "find", "Number", "isActive", "newDisabledUsersList", "filter", "strapi", "store", "set", "type", "key", "value", "castNumberArray", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "map", "toNumber", "removeFromEEDisabledUsersList", "ids", "idsToCheck", "includes", "updateById", "attributes", "_", "has", "lastAdminUser", "isLastSuperAdminUser", "superAdminRole", "getSuperAdminWithUsersCount", "willRemoveSuperAdminRole", "arrays", "includesString", "roles", "hashedPassword", "hashPassword", "password", "updatedUser", "db", "query", "update", "where", "data", "populate", "eventHub", "emit", "sanitizeUser", "deleteById", "userToDelete", "findOne", "some", "r", "code", "usersCount", "deletedUser", "delete", "deleteByIds", "nbOfSuperAdminToDelete", "count", "deletedUsers", "push", "users", "sanitizeUserRoles", "role", "pick", "userId", "hasSuperAdminRole", "omit", "getCurrentActiveUserCount"], "mappings": ";;;;;;;AAOA,MAAM,EAAEA,eAAe,EAAE,GAAGC,MAAAA;AAC5B,MAAM,EAAEC,gBAAgB,EAAE,GAAGC,SAAAA;AAE7B;;;IAIA,MAAMC,yBAA4B,GAAA,OAAOC,EAAYC,EAAAA,KAAAA,GAAAA;AACnD,IAAA,MAAMC,aAAgB,GAAA,MAAMC,UAAW,CAAA,kBAAA,CAAA,CAAoBC,mBAAmB,EAAA;AAE9E,IAAA,IAAI,CAACF,aAAe,EAAA;AAClB,QAAA;AACF;IAEA,MAAMG,IAAAA,GAAOH,cAAcI,IAAI,CAAC,CAACD,IAAcA,GAAAA,IAAAA,CAAKL,EAAE,KAAKO,MAAOP,CAAAA,EAAAA,CAAAA,CAAAA;AAClE,IAAA,IAAI,CAACK,IAAM,EAAA;AACT,QAAA;AACF;AAEA,IAAA,IAAIA,IAAKG,CAAAA,QAAQ,KAAKP,KAAAA,CAAMO,QAAQ,EAAE;QACpC,MAAMC,oBAAAA,GAAuBP,cAAcQ,MAAM,CAAC,CAACL,IAAcA,GAAAA,IAAAA,CAAKL,EAAE,KAAKO,MAAOP,CAAAA,EAAAA,CAAAA,CAAAA;AACpF,QAAA,MAAMW,MAAOC,CAAAA,KAAK,CAACC,GAAG,CAAC;YACrBC,IAAM,EAAA,IAAA;YACNC,GAAK,EAAA,gBAAA;YACLC,KAAOP,EAAAA;AACT,SAAA,CAAA;AACF;AACF,CAAA;AAEA,MAAMQ,eAAAA,GAAkBC,IAAKC,CAAAA,SAAAA,EAAWC,GAAIC,CAAAA,QAAAA,CAAAA,CAAAA;AAE5C,MAAMC,gCAAgC,OAAOC,GAAAA,GAAAA;IAC3C,IAAIC,UAAAA;IACJ,IAAI,OAAOD,QAAQ,QAAU,EAAA;AAC3BC,QAAAA,UAAAA,GAAaP,eAAgBM,CAAAA,GAAAA,CAAAA;KACxB,MAAA;QACLC,UAAa,GAAA;YAACjB,MAAOgB,CAAAA,GAAAA;AAAK,SAAA;AAC5B;AAEA,IAAA,MAAMrB,aAAgB,GAAA,MAAMC,UAAW,CAAA,kBAAA,CAAA,CAAoBC,mBAAmB,EAAA;AAE9E,IAAA,IAAI,CAACF,aAAe,EAAA;AAClB,QAAA;AACF;IAEA,MAAMO,oBAAAA,GAAuBP,aAAcQ,CAAAA,MAAM,CAAC,CAACL,IAAc,GAAA,CAACmB,UAAWC,CAAAA,QAAQ,CAACpB,IAAAA,CAAKL,EAAE,CAAA,CAAA;AAC7F,IAAA,MAAMW,MAAOC,CAAAA,KAAK,CAACC,GAAG,CAAC;QACrBC,IAAM,EAAA,IAAA;QACNC,GAAK,EAAA,gBAAA;QACLC,KAAOP,EAAAA;AACT,KAAA,CAAA;AACF,CAAA;AAEA;;;;;IAMA,MAAMiB,UAAa,GAAA,OAAO1B,EAAS2B,EAAAA,UAAAA,GAAAA;;AAEjC,IAAA,IAAIC,UAAEC,CAAAA,GAAG,CAACF,UAAAA,EAAY,OAAU,CAAA,EAAA;QAC9B,MAAMG,aAAAA,GAAgB,MAAMC,oBAAqB/B,CAAAA,EAAAA,CAAAA;AACjD,QAAA,MAAMgC,cAAiB,GAAA,MAAM7B,UAAW,CAAA,MAAA,CAAA,CAAQ8B,2BAA2B,EAAA;QAC3E,MAAMC,wBAAAA,GAA2B,CAACC,MAAOC,CAAAA,cAAc,CAACT,UAAWU,CAAAA,KAAK,EAAEL,cAAAA,CAAehC,EAAE,CAAA;AAE3F,QAAA,IAAI8B,iBAAiBI,wBAA0B,EAAA;AAC7C,YAAA,MAAM,IAAIvC,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AACF;;IAGA,IAAIgC,UAAAA,CAAWnB,QAAQ,KAAK,KAAO,EAAA;QACjC,MAAMsB,aAAAA,GAAgB,MAAMC,oBAAqB/B,CAAAA,EAAAA,CAAAA;AACjD,QAAA,IAAI8B,aAAe,EAAA;AACjB,YAAA,MAAM,IAAInC,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AACF;;AAGA,IAAA,IAAIiC,UAAEC,CAAAA,GAAG,CAACF,UAAAA,EAAY,UAAa,CAAA,EAAA;AACjC,QAAA,MAAMW,iBAAiB,MAAMnC,UAAAA,CAAW,QAAQoC,YAAY,CAACZ,WAAWa,QAAQ,CAAA;QAEhF,MAAMC,WAAAA,GAAc,MAAM9B,MAAO+B,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAeC,CAAAA,CAAAA,MAAM,CAAC;YAC9DC,KAAO,EAAA;AAAE7C,gBAAAA;AAAG,aAAA;YACZ8C,IAAM,EAAA;AACJ,gBAAA,GAAGnB,UAAU;gBACba,QAAUF,EAAAA;AACZ,aAAA;YACAS,QAAU,EAAA;AAAC,gBAAA;AAAQ;AACrB,SAAA,CAAA;AAEApC,QAAAA,MAAAA,CAAOqC,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAE5C,YAAAA,IAAAA,EAAM6C,YAAaT,CAAAA,WAAAA;AAAa,SAAA,CAAA;QAEtE,OAAOA,WAAAA;AACT;IAEA,MAAMA,WAAAA,GAAc,MAAM9B,MAAO+B,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAeC,CAAAA,CAAAA,MAAM,CAAC;QAC9DC,KAAO,EAAA;AAAE7C,YAAAA;AAAG,SAAA;QACZ8C,IAAMnB,EAAAA,UAAAA;QACNoB,QAAU,EAAA;AAAC,YAAA;AAAQ;AACrB,KAAA,CAAA;AAEA,IAAA,MAAMhD,0BAA0BC,EAAI2B,EAAAA,UAAAA,CAAAA;AAEpC,IAAA,IAAIc,WAAa,EAAA;AACf9B,QAAAA,MAAAA,CAAOqC,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAE5C,YAAAA,IAAAA,EAAM6C,YAAaT,CAAAA,WAAAA;AAAa,SAAA,CAAA;AACxE;IAEA,OAAOA,WAAAA;AACT,CAAA;AAEA;;;IAIA,MAAMU,aAAa,OAAOnD,EAAAA,GAAAA;;IAExB,MAAMoD,YAAAA,GAAe,MAAMzC,MAAO+B,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAeU,CAAAA,CAAAA,OAAO,CAAC;QAChER,KAAO,EAAA;AAAE7C,YAAAA;AAAG,SAAA;QACZ+C,QAAU,EAAA;AAAC,YAAA;AAAQ;AACrB,KAAA,CAAA;AAEA,IAAA,IAAI,CAACK,YAAc,EAAA;QACjB,OAAO,IAAA;AACT;AAEA,IAAA,IAAIA,YAAc,EAAA;QAChB,IAAIA,YAAAA,CAAaf,KAAK,CAACiB,IAAI,CAAC,CAACC,CAAWA,GAAAA,CAAAA,CAAEC,IAAI,KAAK3D,gBAAmB,CAAA,EAAA;AACpE,YAAA,MAAMmC,cAAiB,GAAA,MAAM7B,UAAW,CAAA,MAAA,CAAA,CAAQ8B,2BAA2B,EAAA;YAC3E,IAAID,cAAAA,CAAeyB,UAAU,KAAK,CAAG,EAAA;AACnC,gBAAA,MAAM,IAAI9D,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AACF;AACF;IAEA,MAAM+D,WAAAA,GAAc,MAAM/C,MAAO+B,CAAAA,EAAE,CAChCC,KAAK,CAAC,aACNgB,CAAAA,CAAAA,MAAM,CAAC;QAAEd,KAAO,EAAA;AAAE7C,YAAAA;AAAG,SAAA;QAAG+C,QAAU,EAAA;AAAC,YAAA;AAAQ;AAAC,KAAA,CAAA;AAE/C,IAAA,MAAMzB,6BAA8BtB,CAAAA,EAAAA,CAAAA;AAEpCW,IAAAA,MAAAA,CAAOqC,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAAE5C,QAAAA,IAAAA,EAAM6C,YAAaQ,CAAAA,WAAAA;AAAa,KAAA,CAAA;IAEtE,OAAOA,WAAAA;AACT,CAAA;AAEA;;;IAIA,MAAME,cAAc,OAAOrC,GAAAA,GAAAA;;AAEzB,IAAA,MAAMS,cAAiB,GAAA,MAAM7B,UAAW,CAAA,MAAA,CAAA,CAAQ8B,2BAA2B,EAAA;IAC3E,MAAM4B,sBAAAA,GAAyB,MAAMlD,MAAO+B,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAemB,CAAAA,CAAAA,KAAK,CAAC;QACxEjB,KAAO,EAAA;YACL7C,EAAIuB,EAAAA,GAAAA;YACJc,KAAO,EAAA;AAAErC,gBAAAA,EAAAA,EAAIgC,eAAehC;AAAG;AACjC;AACF,KAAA,CAAA;IAEA,IAAIgC,cAAAA,CAAeyB,UAAU,KAAKI,sBAAwB,EAAA;AACxD,QAAA,MAAM,IAAIlE,eAAgB,CAAA,wDAAA,CAAA;AAC5B;AAEA,IAAA,MAAMoE,eAAe,EAAE;IACvB,KAAK,MAAM/D,MAAMuB,GAAK,CAAA;QACpB,MAAMmC,WAAAA,GAAc,MAAM/C,MAAO+B,CAAAA,EAAE,CAACC,KAAK,CAAC,aAAegB,CAAAA,CAAAA,MAAM,CAAC;YAC9Dd,KAAO,EAAA;AAAE7C,gBAAAA;AAAG,aAAA;YACZ+C,QAAU,EAAA;AAAC,gBAAA;AAAQ;AACrB,SAAA,CAAA;AAEAgB,QAAAA,YAAAA,CAAaC,IAAI,CAACN,WAAAA,CAAAA;AACpB;AAEA,IAAA,MAAMpC,6BAA8BC,CAAAA,GAAAA,CAAAA;AAEpCZ,IAAAA,MAAAA,CAAOqC,QAAQ,CAACC,IAAI,CAAC,aAAe,EAAA;AAClCgB,QAAAA,KAAAA,EAAOF,YAAa3C,CAAAA,GAAG,CAAC,CAACsC,cAAgBR,YAAaQ,CAAAA,WAAAA,CAAAA;AACxD,KAAA,CAAA;IAEA,OAAOK,YAAAA;AACT,CAAA;AAEA,MAAMG,oBAAoB,CAACC,IAAAA,GAAkBvC,UAAEwC,CAAAA,IAAI,CAACD,IAAM,EAAA;AAAC,QAAA,IAAA;AAAM,QAAA,MAAA;AAAQ,QAAA,aAAA;AAAe,QAAA;AAAO,KAAA,CAAA;AAE/F;;;IAIA,MAAMpC,uBAAuB,OAAOsC,MAAAA,GAAAA;IAClC,MAAMhE,IAAAA,GAAQ,MAAMgD,OAAQgB,CAAAA,MAAAA,CAAAA;AAC5B,IAAA,MAAMrC,cAAiB,GAAA,MAAM7B,UAAW,CAAA,MAAA,CAAA,CAAQ8B,2BAA2B,EAAA;AAE3E,IAAA,OAAOD,cAAeyB,CAAAA,UAAU,KAAK,CAAA,IAAKa,iBAAkBjE,CAAAA,IAAAA,CAAAA;AAC9D,CAAA;AAEA;;;IAIA,MAAM6C,eAAe,CAAC7C,IAAAA,GAAAA;IACpB,OAAO;QACL,GAAGuB,UAAAA,CAAE2C,IAAI,CAAClE,IAAM,EAAA;AAAC,YAAA,UAAA;AAAY,YAAA,oBAAA;AAAsB,YAAA,mBAAA;AAAqB,YAAA;SAAQ,CAAC;AACjFgC,QAAAA,KAAAA,EAAOhC,KAAKgC,KAAK,IAAIhC,KAAKgC,KAAK,CAACjB,GAAG,CAAC8C,iBAAAA;AACtC,KAAA;AACF,CAAA;AAEA;;AAEC,IACD,MAAMb,OAAAA,GAAU,OAAOrD,EAAAA,EAAS+C,QAAW,GAAA;AAAC,IAAA;AAAQ,CAAA,GAAA;AAClD,IAAA,OAAOpC,OAAO+B,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAeU,OAAO,CAAC;QAAER,KAAO,EAAA;AAAE7C,YAAAA;AAAG,SAAA;AAAG+C,QAAAA;AAAS,KAAA,CAAA;AAC1E,CAAA;AAEA,MAAMyB,yBAA4B,GAAA,UAAA;AAChC,IAAA,OAAO7D,OAAO+B,EAAE,CAACC,KAAK,CAAC,aAAA,CAAA,CAAemB,KAAK,CAAC;QAAEjB,KAAO,EAAA;YAAErC,QAAU,EAAA;AAAK;AAAE,KAAA,CAAA;AAC1E,CAAA;AAEA,WAAe;AACbT,IAAAA,yBAAAA;AACAuB,IAAAA,6BAAAA;AACAkD,IAAAA,yBAAAA;AACAZ,IAAAA,WAAAA;AACAT,IAAAA,UAAAA;AACAzB,IAAAA;AACF,CAAE;;;;"}