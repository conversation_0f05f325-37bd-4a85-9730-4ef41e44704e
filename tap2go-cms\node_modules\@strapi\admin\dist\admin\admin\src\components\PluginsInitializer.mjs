import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import * as React from 'react';
import { produce } from 'immer';
import { Page } from './PageHelpers.mjs';
import { useStrapiApp } from '../features/StrapiApp.mjs';

/**
 * TODO: this isn't great, and we really should focus on fixing this.
 */ const PluginsInitializer = ({ children })=>{
    const appPlugins = useStrapiApp('PluginsInitializer', (state)=>state.plugins);
    const [{ plugins }, dispatch] = React.useReducer(reducer, initialState, ()=>init(appPlugins));
    const setPlugin = React.useRef((pluginId)=>{
        dispatch({
            type: 'SET_PLUGIN_READY',
            pluginId
        });
    });
    const hasApluginNotReady = Object.keys(plugins).some((plugin)=>plugins[plugin].isReady === false);
    /**
   *
   * I have spent some time trying to understand what is happening here, and wanted to
   * leave that knowledge for my future me:
   *
   * `initializer` is an undocumented property of the `registerPlugin` API. At the time
   * of writing it seems only to be used by the i18n plugin.
   *
   * How does it work?
   *
   * Every plugin that has an `initializer` component defined, receives the
   * `setPlugin` function as a component prop. In the case of i18n the plugin fetches locales
   * first and calls `setPlugin` with `pluginId` once they are loaded, which then triggers the
   * reducer of the admin app defined above.
   *
   * Once all plugins are set to `isReady: true` the app renders.
   *
   * This API is used to block rendering of the admin app. We should remove that in v5 completely
   * and make sure plugins can inject data into the global store before they are initialized, to avoid
   * having a new prop-callback based communication channel between plugins and the core admin app.
   *
   */ if (hasApluginNotReady) {
        const initializers = Object.keys(plugins).reduce((acc, current)=>{
            const InitializerComponent = plugins[current].initializer;
            if (InitializerComponent) {
                const key = plugins[current].pluginId;
                acc.push(/*#__PURE__*/ jsx(InitializerComponent, {
                    setPlugin: setPlugin.current
                }, key));
            }
            return acc;
        }, []);
        return /*#__PURE__*/ jsxs(Fragment, {
            children: [
                initializers,
                /*#__PURE__*/ jsx(Page.Loading, {})
            ]
        });
    }
    return children;
};
const initialState = {
    plugins: {}
};
const reducer = (state = initialState, action)=>produce(state, (draftState)=>{
        switch(action.type){
            case 'SET_PLUGIN_READY':
                {
                    draftState.plugins[action.pluginId].isReady = true;
                    break;
                }
            default:
                return draftState;
        }
    });
/* -------------------------------------------------------------------------------------------------
 * Init state
 * -----------------------------------------------------------------------------------------------*/ const init = (plugins)=>{
    return {
        plugins
    };
};

export { PluginsInitializer };
//# sourceMappingURL=PluginsInitializer.mjs.map
