const languageNativeNames = {
    ar: 'العربية',
    ca: 'Català',
    cs: '<PERSON><PERSON><PERSON><PERSON>',
    de: 'Deutsch',
    dk: 'Dansk',
    en: 'English',
    'en-GB': 'English (United Kingdom)',
    es: 'Español',
    eu: 'Euskara',
    uz: 'O`z<PERSON><PERSON>',
    ro: 'Român<PERSON>',
    fr: 'Français',
    gu: 'Gujarati',
    he: 'עברית',
    hu: 'Magyar',
    id: 'Indonesian',
    it: 'Italiano',
    ja: '日本語',
    ko: '한국어',
    ml: 'Malayalam',
    ms: 'Melayu',
    nl: 'Nederlands',
    no: 'Norwegian',
    pl: 'Polski',
    'pt-BR': 'Português (Brasil)',
    pt: 'Português (Portugal)',
    ru: 'Русский',
    sk: 'Slovenčina',
    sv: 'Swedish',
    th: 'ไทย',
    tr: 'Türkçe',
    uk: 'Українська',
    vi: 'Tiếng Vi<PERSON>',
    'zh-<PERSON>': '中文 (简体)',
    zh: '中文 (繁體)',
    sa: 'संस्कृत',
    hi: 'हिन्दी'
};

export { languageNativeNames };
//# sourceMappingURL=languageNativeNames.mjs.map
