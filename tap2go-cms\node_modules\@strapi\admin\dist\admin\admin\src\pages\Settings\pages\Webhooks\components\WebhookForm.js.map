{"version": 3, "file": "WebhookForm.js", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Webhooks/components/WebhookForm.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box, Button, Flex, Grid, TextInput } from '@strapi/design-system';\nimport { Check, Play as Publish } from '@strapi/icons';\nimport { IntlShape, useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { TriggerWebhook } from '../../../../../../../shared/contracts/webhooks';\nimport { Form, FormHelpers } from '../../../../../components/Form';\nimport { InputRenderer } from '../../../../../components/FormInputs/Renderer';\nimport { Layouts } from '../../../../../components/Layouts/Layout';\nimport { BackButton } from '../../../../../features/BackButton';\nimport { useEnterprise } from '../../../../../hooks/useEnterprise';\n\nimport { EventTableCE } from './EventsTable';\nimport { HeadersInput } from './HeadersInput';\nimport { TriggerContainer } from './TriggerContainer';\n\nimport type { Modules } from '@strapi/types';\n\ninterface WebhookFormValues {\n  name: Modules.WebhookStore.Webhook['name'];\n  url: Modules.WebhookStore.Webhook['url'];\n  headers: Array<{ key: string; value: string }>;\n  events: Modules.WebhookStore.Webhook['events'];\n}\n\ninterface WebhookFormProps {\n  data?: Modules.WebhookStore.Webhook;\n  handleSubmit: (\n    values: WebhookFormValues,\n    helpers: FormHelpers<WebhookFormValues>\n  ) => Promise<void>;\n  isCreating: boolean;\n  isTriggering: boolean;\n  triggerWebhook: () => void;\n  triggerResponse?: TriggerWebhook.Response['data'];\n}\n\nconst WebhookForm = ({\n  handleSubmit,\n  triggerWebhook,\n  isCreating,\n  isTriggering,\n  triggerResponse,\n  data,\n}: WebhookFormProps) => {\n  const { formatMessage } = useIntl();\n  const [showTriggerResponse, setShowTriggerResponse] = React.useState(false);\n  const EventTable = useEnterprise(\n    EventTableCE,\n    async () =>\n      (\n        await import(\n          '../../../../../../../ee/admin/src/pages/SettingsPage/pages/Webhooks/components/EventsTable'\n        )\n      ).EventsTableEE\n  );\n\n  /**\n   * Map the headers into a form that can be used within the formik form\n   */\n  const mapHeaders = (headers: Modules.WebhookStore.Webhook['headers']) => {\n    if (!Object.keys(headers).length) {\n      return [{ key: '', value: '' }];\n    }\n\n    return Object.entries(headers).map(([key, value]) => ({ key, value }));\n  };\n\n  // block rendering until the EE component is fully loaded\n  if (!EventTable) {\n    return null;\n  }\n\n  return (\n    <Form\n      initialValues={{\n        name: data?.name || '',\n        url: data?.url || '',\n        headers: mapHeaders(data?.headers || {}),\n        events: data?.events || [],\n      }}\n      method={isCreating ? 'POST' : 'PUT'}\n      onSubmit={handleSubmit}\n      validationSchema={makeWebhookValidationSchema({ formatMessage })}\n    >\n      {({ isSubmitting, modified }) => (\n        <>\n          <Layouts.Header\n            primaryAction={\n              <Flex gap={2}>\n                <Button\n                  onClick={() => {\n                    triggerWebhook();\n                    setShowTriggerResponse(true);\n                  }}\n                  variant=\"tertiary\"\n                  startIcon={<Publish />}\n                  disabled={isCreating || isTriggering}\n                >\n                  {formatMessage({\n                    id: 'Settings.webhooks.trigger',\n                    defaultMessage: 'Trigger',\n                  })}\n                </Button>\n                <Button\n                  startIcon={<Check />}\n                  type=\"submit\"\n                  disabled={!modified}\n                  loading={isSubmitting}\n                >\n                  {formatMessage({\n                    id: 'global.save',\n                    defaultMessage: 'Save',\n                  })}\n                </Button>\n              </Flex>\n            }\n            title={\n              isCreating\n                ? formatMessage({\n                    id: 'Settings.webhooks.create',\n                    defaultMessage: 'Create a webhook',\n                  })\n                : data?.name\n            }\n            navigationAction={<BackButton fallback=\"../webhooks\" />}\n          />\n          <Layouts.Content>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n              {showTriggerResponse && (\n                <TriggerContainer\n                  isPending={isTriggering}\n                  response={triggerResponse}\n                  onCancel={() => setShowTriggerResponse(false)}\n                />\n              )}\n              <Box background=\"neutral0\" padding={8} shadow=\"filterShadow\" hasRadius>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                  <Grid.Root gap={6}>\n                    {[\n                      {\n                        label: formatMessage({\n                          id: 'global.name',\n                          defaultMessage: 'Name',\n                        }),\n                        name: 'name',\n                        required: true,\n                        size: 6,\n                        type: 'string' as const,\n                      },\n                      {\n                        label: formatMessage({\n                          id: 'Settings.roles.form.input.url',\n                          defaultMessage: 'Url',\n                        }),\n                        name: 'url',\n                        required: true,\n                        size: 12,\n                        type: 'string' as const,\n                      },\n                    ].map(({ size, ...field }) => (\n                      <Grid.Item\n                        key={field.name}\n                        col={size}\n                        direction=\"column\"\n                        alignItems=\"stretch\"\n                      >\n                        <InputRenderer {...field} />\n                      </Grid.Item>\n                    ))}\n                  </Grid.Root>\n                  <HeadersInput />\n                  <EventTable />\n                </Flex>\n              </Box>\n            </Flex>\n          </Layouts.Content>\n        </>\n      )}\n    </Form>\n  );\n};\n\nconst NAME_REGEX = /(^$)|(^[A-Za-z][_0-9A-Za-z ]*$)/;\nconst URL_REGEX = /(^$)|((https?:\\/\\/.*)(d*)\\/?(.*))/;\n\nconst makeWebhookValidationSchema = ({ formatMessage }: Pick<IntlShape, 'formatMessage'>) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .nullable()\n      .required(\n        formatMessage({\n          id: 'Settings.webhooks.validation.name.required',\n          defaultMessage: 'Name is required',\n        })\n      )\n      .matches(\n        NAME_REGEX,\n        formatMessage({\n          id: 'Settings.webhooks.validation.name.regex',\n          defaultMessage:\n            'The name must start with a letter and only contain letters, numbers, spaces and underscores',\n        })\n      ),\n    url: yup\n      .string()\n      .nullable()\n      .required(\n        formatMessage({\n          id: 'Settings.webhooks.validation.url.required',\n          defaultMessage: 'Url is required',\n        })\n      )\n      .matches(\n        URL_REGEX,\n        formatMessage({\n          id: 'Settings.webhooks.validation.url.regex',\n          defaultMessage: 'The value must be a valid Url',\n        })\n      ),\n    headers: yup.lazy((array) => {\n      const baseSchema = yup.array();\n\n      if (array.length === 1) {\n        const { key, value } = array[0];\n\n        if (!key && !value) {\n          return baseSchema;\n        }\n      }\n\n      return baseSchema.of(\n        yup.object().shape({\n          key: yup\n            .string()\n            .required(\n              formatMessage({\n                id: 'Settings.webhooks.validation.key',\n                defaultMessage: 'Key is required',\n              })\n            )\n            .nullable(),\n          value: yup\n            .string()\n            .required(\n              formatMessage({\n                id: 'Settings.webhooks.validation.value',\n                defaultMessage: 'Value is required',\n              })\n            )\n            .nullable(),\n        })\n      );\n    }),\n    events: yup.array(),\n  });\n\nexport { WebhookForm };\nexport type { WebhookFormValues, WebhookFormProps };\n"], "names": ["WebhookForm", "handleSubmit", "triggerWebhook", "isCreating", "isTriggering", "triggerResponse", "data", "formatMessage", "useIntl", "showTriggerResponse", "setShowTriggerResponse", "React", "useState", "EventTable", "useEnterprise", "EventTableCE", "EventsTableEE", "mapHeaders", "headers", "Object", "keys", "length", "key", "value", "entries", "map", "_jsx", "Form", "initialValues", "name", "url", "events", "method", "onSubmit", "validationSchema", "makeWebhookValidationSchema", "isSubmitting", "modified", "_jsxs", "_Fragment", "Layouts", "Header", "primaryAction", "Flex", "gap", "<PERSON><PERSON>", "onClick", "variant", "startIcon", "Publish", "disabled", "id", "defaultMessage", "Check", "type", "loading", "title", "navigationAction", "BackButton", "fallback", "Content", "direction", "alignItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPending", "response", "onCancel", "Box", "background", "padding", "shadow", "hasRadius", "Grid", "Root", "label", "required", "size", "field", "<PERSON><PERSON>", "col", "InputR<PERSON><PERSON>", "HeadersInput", "NAME_REGEX", "URL_REGEX", "yup", "object", "shape", "string", "nullable", "matches", "lazy", "array", "baseSchema", "of"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,MAAMA,WAAc,GAAA,CAAC,EACnBC,YAAY,EACZC,cAAc,EACdC,UAAU,EACVC,YAAY,EACZC,eAAe,EACfC,IAAI,EACa,GAAA;IACjB,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;AAC1B,IAAA,MAAM,CAACC,mBAAqBC,EAAAA,sBAAAA,CAAuB,GAAGC,gBAAAA,CAAMC,QAAQ,CAAC,KAAA,CAAA;IACrE,MAAMC,UAAAA,GAAaC,2BACjBC,CAAAA,wBAAAA,EACA,UAEI,CAAA,MAAM,oDACJ,+FACF,KAAA,EACAC,aAAa,CAAA;AAGnB;;MAGA,MAAMC,aAAa,CAACC,OAAAA,GAAAA;AAClB,QAAA,IAAI,CAACC,MAAOC,CAAAA,IAAI,CAACF,OAAAA,CAAAA,CAASG,MAAM,EAAE;YAChC,OAAO;AAAC,gBAAA;oBAAEC,GAAK,EAAA,EAAA;oBAAIC,KAAO,EAAA;AAAG;AAAE,aAAA;AACjC;QAEA,OAAOJ,MAAAA,CAAOK,OAAO,CAACN,OAASO,CAAAA,CAAAA,GAAG,CAAC,CAAC,CAACH,GAAAA,EAAKC,KAAM,CAAA,IAAM;AAAED,gBAAAA,GAAAA;AAAKC,gBAAAA;aAAM,CAAA,CAAA;AACrE,KAAA;;AAGA,IAAA,IAAI,CAACV,UAAY,EAAA;QACf,OAAO,IAAA;AACT;AAEA,IAAA,qBACEa,cAACC,CAAAA,SAAAA,EAAAA;QACCC,aAAe,EAAA;AACbC,YAAAA,IAAAA,EAAMvB,MAAMuB,IAAQ,IAAA,EAAA;AACpBC,YAAAA,GAAAA,EAAKxB,MAAMwB,GAAO,IAAA,EAAA;YAClBZ,OAASD,EAAAA,UAAAA,CAAWX,IAAMY,EAAAA,OAAAA,IAAW,EAAC,CAAA;YACtCa,MAAQzB,EAAAA,IAAAA,EAAMyB,UAAU;AAC1B,SAAA;AACAC,QAAAA,MAAAA,EAAQ7B,aAAa,MAAS,GAAA,KAAA;QAC9B8B,QAAUhC,EAAAA,YAAAA;AACViC,QAAAA,gBAAAA,EAAkBC,2BAA4B,CAAA;AAAE5B,YAAAA;AAAc,SAAA,CAAA;AAE7D,QAAA,QAAA,EAAA,CAAC,EAAE6B,YAAY,EAAEC,QAAQ,EAAE,iBAC1BC,eAAA,CAAAC,mBAAA,EAAA;;AACE,kCAAAb,cAAA,CAACc,eAAQC,MAAM,EAAA;AACbC,wBAAAA,aAAAA,gBACEJ,eAACK,CAAAA,iBAAAA,EAAAA;4BAAKC,GAAK,EAAA,CAAA;;8CACTlB,cAACmB,CAAAA,mBAAAA,EAAAA;oCACCC,OAAS,EAAA,IAAA;AACP5C,wCAAAA,cAAAA,EAAAA;wCACAQ,sBAAuB,CAAA,IAAA,CAAA;AACzB,qCAAA;oCACAqC,OAAQ,EAAA,UAAA;AACRC,oCAAAA,SAAAA,gBAAWtB,cAACuB,CAAAA,UAAAA,EAAAA,EAAAA,CAAAA;AACZC,oCAAAA,QAAAA,EAAU/C,UAAcC,IAAAA,YAAAA;8CAEvBG,aAAc,CAAA;wCACb4C,EAAI,EAAA,2BAAA;wCACJC,cAAgB,EAAA;AAClB,qCAAA;;8CAEF1B,cAACmB,CAAAA,mBAAAA,EAAAA;AACCG,oCAAAA,SAAAA,gBAAWtB,cAAC2B,CAAAA,WAAAA,EAAAA,EAAAA,CAAAA;oCACZC,IAAK,EAAA,QAAA;AACLJ,oCAAAA,QAAAA,EAAU,CAACb,QAAAA;oCACXkB,OAASnB,EAAAA,YAAAA;8CAER7B,aAAc,CAAA;wCACb4C,EAAI,EAAA,aAAA;wCACJC,cAAgB,EAAA;AAClB,qCAAA;;;;AAINI,wBAAAA,KAAAA,EACErD,aACII,aAAc,CAAA;4BACZ4C,EAAI,EAAA,0BAAA;4BACJC,cAAgB,EAAA;AAClB,yBAAA,CAAA,GACA9C,IAAMuB,EAAAA,IAAAA;AAEZ4B,wBAAAA,gBAAAA,gBAAkB/B,cAACgC,CAAAA,qBAAAA,EAAAA;4BAAWC,QAAS,EAAA;;;AAEzC,kCAAAjC,cAAA,CAACc,eAAQoB,OAAO,EAAA;AACd,wBAAA,QAAA,gBAAAtB,eAACK,CAAAA,iBAAAA,EAAAA;4BAAKkB,SAAU,EAAA,QAAA;4BAASC,UAAW,EAAA,SAAA;4BAAUlB,GAAK,EAAA,CAAA;;AAChDnC,gCAAAA,mBAAAA,kBACCiB,cAACqC,CAAAA,iCAAAA,EAAAA;oCACCC,SAAW5D,EAAAA,YAAAA;oCACX6D,QAAU5D,EAAAA,eAAAA;AACV6D,oCAAAA,QAAAA,EAAU,IAAMxD,sBAAuB,CAAA,KAAA;;8CAG3CgB,cAACyC,CAAAA,gBAAAA,EAAAA;oCAAIC,UAAW,EAAA,UAAA;oCAAWC,OAAS,EAAA,CAAA;oCAAGC,MAAO,EAAA,cAAA;oCAAeC,SAAS,EAAA,IAAA;AACpE,oCAAA,QAAA,gBAAAjC,eAACK,CAAAA,iBAAAA,EAAAA;wCAAKkB,SAAU,EAAA,QAAA;wCAASC,UAAW,EAAA,SAAA;wCAAUlB,GAAK,EAAA,CAAA;;AACjD,0DAAAlB,cAAA,CAAC8C,kBAAKC,IAAI,EAAA;gDAAC7B,GAAK,EAAA,CAAA;AACb,gDAAA,QAAA,EAAA;AACC,oDAAA;AACE8B,wDAAAA,KAAAA,EAAOnE,aAAc,CAAA;4DACnB4C,EAAI,EAAA,aAAA;4DACJC,cAAgB,EAAA;AAClB,yDAAA,CAAA;wDACAvB,IAAM,EAAA,MAAA;wDACN8C,QAAU,EAAA,IAAA;wDACVC,IAAM,EAAA,CAAA;wDACNtB,IAAM,EAAA;AACR,qDAAA;AACA,oDAAA;AACEoB,wDAAAA,KAAAA,EAAOnE,aAAc,CAAA;4DACnB4C,EAAI,EAAA,+BAAA;4DACJC,cAAgB,EAAA;AAClB,yDAAA,CAAA;wDACAvB,IAAM,EAAA,KAAA;wDACN8C,QAAU,EAAA,IAAA;wDACVC,IAAM,EAAA,EAAA;wDACNtB,IAAM,EAAA;AACR;iDACD,CAAC7B,GAAG,CAAC,CAAC,EAAEmD,IAAI,EAAE,GAAGC,KAAO,EAAA,iBACvBnD,cAAC8C,CAAAA,iBAAAA,CAAKM,IAAI,EAAA;wDAERC,GAAKH,EAAAA,IAAAA;wDACLf,SAAU,EAAA,QAAA;wDACVC,UAAW,EAAA,SAAA;AAEX,wDAAA,QAAA,gBAAApC,cAACsD,CAAAA,sBAAAA,EAAAA;AAAe,4DAAA,GAAGH;;AALdA,qDAAAA,EAAAA,KAAAA,CAAMhD,IAAI,CAAA;;0DASrBH,cAACuD,CAAAA,yBAAAA,EAAAA,EAAAA,CAAAA;0DACDvD,cAACb,CAAAA,UAAAA,EAAAA,EAAAA;;;;;;;;;;AASnB;AAEA,MAAMqE,UAAa,GAAA,iCAAA;AACnB,MAAMC,SAAY,GAAA,mCAAA;AAElB,MAAMhD,2BAAAA,GAA8B,CAAC,EAAE5B,aAAa,EAAoC,GACtF6E,cAAIC,CAAAA,MAAM,EAAGC,CAAAA,KAAK,CAAC;AACjBzD,QAAAA,IAAAA,EAAMuD,eACHG,MAAM,EAAA,CACNC,QAAQ,EACRb,CAAAA,QAAQ,CACPpE,aAAc,CAAA;YACZ4C,EAAI,EAAA,4CAAA;YACJC,cAAgB,EAAA;SAGnBqC,CAAAA,CAAAA,CAAAA,OAAO,CACNP,UAAAA,EACA3E,aAAc,CAAA;YACZ4C,EAAI,EAAA,yCAAA;YACJC,cACE,EAAA;AACJ,SAAA,CAAA,CAAA;AAEJtB,QAAAA,GAAAA,EAAKsD,eACFG,MAAM,EAAA,CACNC,QAAQ,EACRb,CAAAA,QAAQ,CACPpE,aAAc,CAAA;YACZ4C,EAAI,EAAA,2CAAA;YACJC,cAAgB,EAAA;SAGnBqC,CAAAA,CAAAA,CAAAA,OAAO,CACNN,SAAAA,EACA5E,aAAc,CAAA;YACZ4C,EAAI,EAAA,wCAAA;YACJC,cAAgB,EAAA;AAClB,SAAA,CAAA,CAAA;QAEJlC,OAASkE,EAAAA,cAAAA,CAAIM,IAAI,CAAC,CAACC,KAAAA,GAAAA;YACjB,MAAMC,UAAAA,GAAaR,eAAIO,KAAK,EAAA;YAE5B,IAAIA,KAAAA,CAAMtE,MAAM,KAAK,CAAG,EAAA;gBACtB,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE,GAAGoE,KAAK,CAAC,CAAE,CAAA;gBAE/B,IAAI,CAACrE,GAAO,IAAA,CAACC,KAAO,EAAA;oBAClB,OAAOqE,UAAAA;AACT;AACF;AAEA,YAAA,OAAOA,WAAWC,EAAE,CAClBT,eAAIC,MAAM,EAAA,CAAGC,KAAK,CAAC;AACjBhE,gBAAAA,GAAAA,EAAK8D,cACFG,CAAAA,MAAM,EACNZ,CAAAA,QAAQ,CACPpE,aAAc,CAAA;oBACZ4C,EAAI,EAAA,kCAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA,CAAA,CAAA,CAEDoC,QAAQ,EAAA;AACXjE,gBAAAA,KAAAA,EAAO6D,cACJG,CAAAA,MAAM,EACNZ,CAAAA,QAAQ,CACPpE,aAAc,CAAA;oBACZ4C,EAAI,EAAA,oCAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA,CAAA,CAAA,CAEDoC,QAAQ;AACb,aAAA,CAAA,CAAA;AAEJ,SAAA,CAAA;AACAzD,QAAAA,MAAAA,EAAQqD,eAAIO,KAAK;AACnB,KAAA,CAAA;;;;"}