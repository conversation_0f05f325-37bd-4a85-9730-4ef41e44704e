{"version": 3, "file": "token.mjs", "sources": ["../../../../../../server/src/validation/transfer/token.ts"], "sourcesContent": ["import { yup, validateYupSchema } from '@strapi/utils';\nimport constants from '../../services/constants';\n\nconst transferTokenCreationSchema = yup\n  .object()\n  .shape({\n    name: yup.string().min(1).required(),\n    description: yup.string().optional(),\n    permissions: yup\n      .array()\n      .min(1)\n      .of(yup.string().oneOf(Object.values(constants.TRANSFER_TOKEN_TYPE)))\n      .required(),\n    lifespan: yup\n      .number()\n      .min(1)\n      .oneOf(Object.values(constants.TRANSFER_TOKEN_LIFESPANS))\n      .nullable(),\n  })\n  .noUnknown()\n  .strict();\n\nconst transferTokenUpdateSchema = yup\n  .object()\n  .shape({\n    name: yup.string().min(1).notNull(),\n    description: yup.string().nullable(),\n    permissions: yup\n      .array()\n      .min(1)\n      .of(yup.string().oneOf(Object.values(constants.TRANSFER_TOKEN_TYPE)))\n      .nullable(),\n  })\n  .noUnknown()\n  .strict();\n\nexport const validateTransferTokenCreationInput = validateYupSchema(transferTokenCreationSchema);\nexport const validateTransferTokenUpdateInput = validateYupSchema(transferTokenUpdateSchema);\n\nexport default {\n  validateTransferTokenCreationInput,\n  validateTransferTokenUpdateInput,\n};\n"], "names": ["transferTokenCreationSchema", "yup", "object", "shape", "name", "string", "min", "required", "description", "optional", "permissions", "array", "of", "oneOf", "Object", "values", "constants", "TRANSFER_TOKEN_TYPE", "lifespan", "number", "TRANSFER_TOKEN_LIFESPANS", "nullable", "noUnknown", "strict", "transferTokenUpdateSchema", "notNull", "validateTransferTokenCreationInput", "validateYupSchema", "validateTransferTokenUpdateInput"], "mappings": ";;;AAGA,MAAMA,2BAA8BC,GAAAA,GAAAA,CACjCC,MAAM,EAAA,CACNC,KAAK,CAAC;AACLC,IAAAA,IAAAA,EAAMH,IAAII,MAAM,EAAA,CAAGC,GAAG,CAAC,GAAGC,QAAQ,EAAA;IAClCC,WAAaP,EAAAA,GAAAA,CAAII,MAAM,EAAA,CAAGI,QAAQ,EAAA;AAClCC,IAAAA,WAAAA,EAAaT,IACVU,KAAK,EAAA,CACLL,GAAG,CAAC,CAAA,CAAA,CACJM,EAAE,CAACX,GAAAA,CAAII,MAAM,EAAGQ,CAAAA,KAAK,CAACC,MAAOC,CAAAA,MAAM,CAACC,SAAUC,CAAAA,mBAAmB,IACjEV,QAAQ,EAAA;AACXW,IAAAA,QAAAA,EAAUjB,GACPkB,CAAAA,MAAM,EACNb,CAAAA,GAAG,CAAC,CACJO,CAAAA,CAAAA,KAAK,CAACC,MAAAA,CAAOC,MAAM,CAACC,SAAUI,CAAAA,wBAAwB,GACtDC,QAAQ;AACb,CACCC,CAAAA,CAAAA,SAAS,GACTC,MAAM,EAAA;AAET,MAAMC,yBAA4BvB,GAAAA,GAAAA,CAC/BC,MAAM,EAAA,CACNC,KAAK,CAAC;AACLC,IAAAA,IAAAA,EAAMH,IAAII,MAAM,EAAA,CAAGC,GAAG,CAAC,GAAGmB,OAAO,EAAA;IACjCjB,WAAaP,EAAAA,GAAAA,CAAII,MAAM,EAAA,CAAGgB,QAAQ,EAAA;AAClCX,IAAAA,WAAAA,EAAaT,IACVU,KAAK,EAAA,CACLL,GAAG,CAAC,CAAA,CAAA,CACJM,EAAE,CAACX,GAAAA,CAAII,MAAM,EAAGQ,CAAAA,KAAK,CAACC,MAAOC,CAAAA,MAAM,CAACC,SAAUC,CAAAA,mBAAmB,IACjEI,QAAQ;AACb,CACCC,CAAAA,CAAAA,SAAS,GACTC,MAAM,EAAA;AAEF,MAAMG,kCAAqCC,GAAAA,iBAAAA,CAAkB3B,2BAA6B;AAC1F,MAAM4B,gCAAmCD,GAAAA,iBAAAA,CAAkBH,yBAA2B;AAE7F,YAAe;AACbE,IAAAA,kCAAAA;AACAE,IAAAA;AACF,CAAE;;;;"}