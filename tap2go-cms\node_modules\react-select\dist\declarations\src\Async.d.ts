import { ReactElement, RefAttributes } from 'react';
import Select from './Select';
import { GroupBase } from './types';
import useAsync from './useAsync';
import type { AsyncProps } from './useAsync';
export type { AsyncProps };
declare type AsyncSelect = <Option = unknown, IsMulti extends boolean = false, Group extends GroupBase<Option> = GroupBase<Option>>(props: AsyncProps<Option, IsMulti, Group> & RefAttributes<Select<Option, IsMulti, Group>>) => ReactElement;
declare const AsyncSelect: AsyncSelect;
export { useAsync };
export default AsyncSelect;
