{"version": 3, "file": "utils.js", "sources": ["../../../../../../../ee/server/src/controllers/authentication-utils/utils.ts"], "sourcesContent": ["import { mapValues } from 'lodash/fp';\nimport { PROVIDER_REDIRECT_ERROR, PROVIDER_REDIRECT_SUCCESS } from './constants';\n\nconst PROVIDER_URLS_MAP = {\n  success: PROVIDER_REDIRECT_SUCCESS,\n  error: PROVIDER_REDIRECT_ERROR,\n};\n\nexport const getAdminStore = async () => strapi.store({ type: 'core', name: 'admin' });\n\nexport const getPrefixedRedirectUrls = () => {\n  const { url: adminUrl } = strapi.config.get('admin') as any;\n  const prefixUrl = (url: string) => `${adminUrl || '/admin'}${url}`;\n\n  return mapValues(prefixUrl, PROVIDER_URLS_MAP);\n};\n\nexport default {\n  getAdminStore,\n  getPrefixedRedirectUrls,\n};\n"], "names": ["PROVIDER_URLS_MAP", "success", "PROVIDER_REDIRECT_SUCCESS", "error", "PROVIDER_REDIRECT_ERROR", "getAdminStore", "strapi", "store", "type", "name", "getPrefixedRedirectUrls", "url", "adminUrl", "config", "get", "prefixUrl", "mapValues"], "mappings": ";;;;;;;AAGA,MAAMA,iBAAoB,GAAA;IACxBC,OAASC,EAAAA,mCAAAA;IACTC,KAAOC,EAAAA;AACT,CAAA;AAEaC,MAAAA,aAAAA,GAAgB,UAAYC,MAAAA,CAAOC,KAAK,CAAC;QAAEC,IAAM,EAAA,MAAA;QAAQC,IAAM,EAAA;KAAW;MAE1EC,uBAA0B,GAAA,IAAA;IACrC,MAAM,EAAEC,KAAKC,QAAQ,EAAE,GAAGN,MAAOO,CAAAA,MAAM,CAACC,GAAG,CAAC,OAAA,CAAA;IAC5C,MAAMC,SAAAA,GAAY,CAACJ,GAAgB,GAAA,CAAC,EAAEC,QAAY,IAAA,QAAA,CAAS,EAAED,GAAAA,CAAI,CAAC;AAElE,IAAA,OAAOK,aAAUD,SAAWf,EAAAA,iBAAAA,CAAAA;AAC9B;AAEA,YAAe;AACbK,IAAAA,aAAAA;AACAK,IAAAA;AACF,CAAE;;;;;;"}