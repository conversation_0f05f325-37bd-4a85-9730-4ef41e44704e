"use strict";Object.defineProperty(exports,"__esModule",{value:!0});class t{constructor(t,e){this.operator=t,this.value=e,Object.defineProperty(this,"t",{writable:!0})}get notes(){return this.t}addNote(t){this.t=this.t||[],this.t.push(t)}}class e extends t{}class r extends e{constructor(t,e){if(!Array.isArray(e))throw new Error(`"${t}" operator expects to receive an array of conditions`);super(t,e)}}class o extends t{constructor(t,e,r){super(t,r),this.field=e}}const s=new e("__null__",null),n=Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function i(t,e){return e instanceof r&&e.operator===t}function c(t,e){return 1===e.length?e[0]:new r(t,function t(e,r,o){const s=o||[];for(let o=0,n=r.length;o<n;o++){const n=r[o];i(e,n)?t(e,n.value,s):s.push(n)}return s}(t,e))}const u=t=>t,a=()=>Object.create(null),h=Object.defineProperty(a(),"__@type@__",{value:"ignore value"});function p(t,e,r=!1){if(!t||t&&t.constructor!==Object)return!1;for(const o in t){if(n(t,o)&&n(e,o)&&(!r||t[o]!==h))return!0}return!1}function f(t){const e=[];for(const r in t)n(t,r)&&t[r]!==h&&e.push(r);return e}function l(t,e){e!==s&&t.push(e)}const d=t=>c("and",t),x={compound(t,e,o){const s=(Array.isArray(e)?e:[e]).map(t=>o.parse(t));return new r(t.name,s)},field:(t,e,r)=>new o(t.name,r.field,e),document:(t,r)=>new e(t.name,r)};class b{constructor(t,e=a()){this.o=void 0,this.s=void 0,this.i=void 0,this.u=void 0,this.h=void 0,this.parse=this.parse.bind(this),this.u={operatorToConditionName:e.operatorToConditionName||u,defaultOperatorName:e.defaultOperatorName||"eq",mergeFinalConditions:e.mergeFinalConditions||d},this.o=Object.keys(t).reduce((e,r)=>(e[r]=Object.assign({name:this.u.operatorToConditionName(r)},t[r]),e),{}),this.s=Object.assign({},e.fieldContext,{field:"",query:{},parse:this.parse,hasOperators:t=>p(t,this.o,e.useIgnoreValue)}),this.i=Object.assign({},e.documentContext,{parse:this.parse,query:{}}),this.h=e.useIgnoreValue?f:Object.keys}setParse(t){this.parse=t,this.s.parse=t,this.i.parse=t}parseField(t,e,r,o){const s=this.o[e];if(!s)throw new Error(`Unsupported operator "${e}"`);if("field"!==s.type)throw new Error(`Unexpected ${s.type} operator "${e}" at field level`);return this.s.field=t,this.s.query=o,this.parseInstruction(s,r,this.s)}parseInstruction(t,e,r){"function"==typeof t.validate&&t.validate(t,e);return(t.parse||x[t.type])(t,e,r)}parseFieldOperators(t,e){const r=[],o=this.h(e);for(let s=0,n=o.length;s<n;s++){const n=o[s];if(!this.o[n])throw new Error(`Field query for "${t}" may contain only operators or a plain object as a value`);l(r,this.parseField(t,n,e[n],e))}return r}parse(t){const e=[],r=this.h(t);this.i.query=t;for(let o=0,s=r.length;o<s;o++){const s=r[o],n=t[s],i=this.o[s];if(i){if("document"!==i.type&&"compound"!==i.type)throw new Error(`Cannot use parsing instruction for operator "${s}" in "document" context as it is supposed to be used in  "${i.type}" context`);l(e,this.parseInstruction(i,n,this.i))}else this.s.hasOperators(n)?e.push(...this.parseFieldOperators(s,n)):l(e,this.parseField(s,this.u.defaultOperatorName,n,t))}return this.u.mergeFinalConditions(e)}}function w(t,e){const r=t[e];if("function"!=typeof r)throw new Error(`Unable to interpret "${e}" condition. Did you forget to register interpreter for it?`);return r}function O(t){return t.operator}const _=b.prototype.parseInstruction;exports.CompoundCondition=r,exports.Condition=t,exports.DocumentCondition=e,exports.FieldCondition=o,exports.ITSELF="__itself__",exports.NULL_CONDITION=s,exports.ObjectQueryParser=b,exports.buildAnd=d,exports.buildOr=t=>c("or",t),exports.createInterpreter=function(t,e){const r=e,o=r&&r.getInterpreterName||O;let s;switch(r?r.numberOfArguments:0){case 1:s=e=>{const s=o(e,r);return w(t,s)(e,n)};break;case 3:s=(e,s,i)=>{const c=o(e,r);return w(t,c)(e,s,i,n)};break;default:s=(e,s)=>{const i=o(e,r);return w(t,i)(e,s,n)}}const n=Object.assign({},r,{interpret:s});return n.interpret},exports.createTranslatorFactory=function(t,e){return(r,...o)=>{const s=t(r,...o),n=e.bind(null,s);return n.ast=s,n}},exports.defaultInstructionParsers=x,exports.hasOperators=p,exports.identity=u,exports.ignoreValue=h,exports.isCompound=i,exports.object=a,exports.optimizedCompoundCondition=c,exports.parseInstruction=_;
//# sourceMappingURL=index.js.map
