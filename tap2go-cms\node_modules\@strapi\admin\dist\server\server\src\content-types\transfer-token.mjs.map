{"version": 3, "file": "transfer-token.mjs", "sources": ["../../../../../server/src/content-types/transfer-token.ts"], "sourcesContent": ["export default {\n  collectionName: 'strapi_transfer_tokens',\n  info: {\n    name: 'Transfer Token',\n    singularName: 'transfer-token',\n    pluralName: 'transfer-tokens',\n    displayName: 'Transfer Token',\n    description: '',\n  },\n  options: {},\n  pluginOptions: {\n    'content-manager': {\n      visible: false,\n    },\n    'content-type-builder': {\n      visible: false,\n    },\n  },\n  attributes: {\n    name: {\n      type: 'string',\n      minLength: 1,\n      configurable: false,\n      required: true,\n      unique: true,\n    },\n    description: {\n      type: 'string',\n      minLength: 1,\n      configurable: false,\n      required: false,\n      default: '',\n    },\n    accessKey: {\n      type: 'string',\n      minLength: 1,\n      configurable: false,\n      required: true,\n    },\n    lastUsedAt: {\n      type: 'datetime',\n      configurable: false,\n      required: false,\n    },\n    permissions: {\n      type: 'relation',\n      target: 'admin::transfer-token-permission',\n      relation: 'oneToMany',\n      mappedBy: 'token',\n      configurable: false,\n      required: false,\n    },\n    expiresAt: {\n      type: 'datetime',\n      configurable: false,\n      required: false,\n    },\n    lifespan: {\n      type: 'biginteger',\n      configurable: false,\n      required: false,\n    },\n  },\n};\n"], "names": ["collectionName", "info", "name", "singularName", "pluralName", "displayName", "description", "options", "pluginOptions", "visible", "attributes", "type", "<PERSON><PERSON><PERSON><PERSON>", "configurable", "required", "unique", "default", "accessKey", "lastUsedAt", "permissions", "target", "relation", "mappedBy", "expiresAt", "lifespan"], "mappings": "AAAA,oBAAe;IACbA,cAAgB,EAAA,wBAAA;IAChBC,IAAM,EAAA;QACJC,IAAM,EAAA,gBAAA;QACNC,YAAc,EAAA,gBAAA;QACdC,UAAY,EAAA,iBAAA;QACZC,WAAa,EAAA,gBAAA;QACbC,WAAa,EAAA;AACf,KAAA;AACAC,IAAAA,OAAAA,EAAS,EAAC;IACVC,aAAe,EAAA;QACb,iBAAmB,EAAA;YACjBC,OAAS,EAAA;AACX,SAAA;QACA,sBAAwB,EAAA;YACtBA,OAAS,EAAA;AACX;AACF,KAAA;IACAC,UAAY,EAAA;QACVR,IAAM,EAAA;YACJS,IAAM,EAAA,QAAA;YACNC,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA,IAAA;YACVC,MAAQ,EAAA;AACV,SAAA;QACAT,WAAa,EAAA;YACXK,IAAM,EAAA,QAAA;YACNC,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA,KAAA;YACVE,OAAS,EAAA;AACX,SAAA;QACAC,SAAW,EAAA;YACTN,IAAM,EAAA,QAAA;YACNC,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAI,UAAY,EAAA;YACVP,IAAM,EAAA,UAAA;YACNE,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAK,WAAa,EAAA;YACXR,IAAM,EAAA,UAAA;YACNS,MAAQ,EAAA,kCAAA;YACRC,QAAU,EAAA,WAAA;YACVC,QAAU,EAAA,OAAA;YACVT,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAS,SAAW,EAAA;YACTZ,IAAM,EAAA,UAAA;YACNE,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAU,QAAU,EAAA;YACRb,IAAM,EAAA,YAAA;YACNE,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ;AACF;AACF,CAAE;;;;"}