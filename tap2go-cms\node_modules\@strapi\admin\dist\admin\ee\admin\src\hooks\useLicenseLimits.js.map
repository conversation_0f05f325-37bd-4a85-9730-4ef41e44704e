{"version": 3, "file": "useLicenseLimits.js", "sources": ["../../../../../../ee/admin/src/hooks/useLicenseLimits.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useGetLicenseLimitsQuery } from '../../../../admin/src/services/admin';\nimport { GetLicenseLimitInformation } from '../../../../shared/contracts/admin';\n\ninterface UseLicenseLimitsArgs {\n  enabled?: boolean;\n}\n\nfunction useLicenseLimits({ enabled }: UseLicenseLimitsArgs = { enabled: true }) {\n  const { data, isError, isLoading } = useGetLicenseLimitsQuery(undefined, {\n    skip: !enabled,\n  });\n\n  type FeatureNames = GetLicenseLimitInformation.Response['data']['features'][number]['name'];\n\n  type GetFeatureType = <T>(name: FeatureNames) => Record<string, T> | undefined;\n\n  const getFeature = React.useCallback<GetFeatureType>(\n    (name) => {\n      const feature = data?.data?.features.find((feature) => feature.name === name);\n\n      if (feature && 'options' in feature) {\n        return feature.options;\n      } else {\n        return {};\n      }\n    },\n    [data]\n  );\n\n  return {\n    license: data?.data,\n    getFeature,\n    isError,\n    isLoading,\n    isTrial: data?.data?.isTrial ?? false,\n  };\n}\n\nexport { useLicenseLimits };\nexport type { UseLicenseLimitsArgs };\n"], "names": ["useLicenseLimits", "enabled", "data", "isError", "isLoading", "useGetLicenseLimitsQuery", "undefined", "skip", "getFeature", "React", "useCallback", "name", "feature", "features", "find", "options", "license", "isTrial"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AASA,SAASA,gBAAiB,CAAA,EAAEC,OAAO,EAAwB,GAAG;IAAEA,OAAS,EAAA;AAAK,CAAC,EAAA;IAC7E,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAGC,8BAAAA,CAAyBC,SAAW,EAAA;AACvEC,QAAAA,IAAAA,EAAM,CAACN;AACT,KAAA,CAAA;AAMA,IAAA,MAAMO,UAAaC,GAAAA,gBAAAA,CAAMC,WAAW,CAClC,CAACC,IAAAA,GAAAA;QACC,MAAMC,OAAAA,GAAUV,MAAMA,IAAMW,EAAAA,QAAAA,CAASC,KAAK,CAACF,OAAAA,GAAYA,OAAQD,CAAAA,IAAI,KAAKA,IAAAA,CAAAA;QAExE,IAAIC,OAAAA,IAAW,aAAaA,OAAS,EAAA;AACnC,YAAA,OAAOA,QAAQG,OAAO;SACjB,MAAA;AACL,YAAA,OAAO,EAAC;AACV;KAEF,EAAA;AAACb,QAAAA;AAAK,KAAA,CAAA;IAGR,OAAO;AACLc,QAAAA,OAAAA,EAASd,IAAMA,EAAAA,IAAAA;AACfM,QAAAA,UAAAA;AACAL,QAAAA,OAAAA;AACAC,QAAAA,SAAAA;QACAa,OAASf,EAAAA,IAAAA,EAAMA,MAAMe,OAAW,IAAA;AAClC,KAAA;AACF;;;;"}