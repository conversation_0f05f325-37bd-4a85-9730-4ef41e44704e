# Strapi built-in admin panel

## Description

This is the admin panel package

## Contribute

You can read the contribution guide [here](../../../CONTRIBUTING.md)

### Setup

Create a new Strapi project: `strapi new myApp`.

Go in your project: `cd myApp`.

### Development

Start the React application: `cd myApp/admin`, then `npm start`.

The admin panel should now be available at [http://localhost:4000](http://localhost:4000).

### Build

In order to check your updates, you can build the admin panel: `cd myApp`, then `npm run build`.
