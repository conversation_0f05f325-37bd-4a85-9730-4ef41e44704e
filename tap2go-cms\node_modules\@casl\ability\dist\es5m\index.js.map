{"version": 3, "file": "index.js", "sources": ["../../src/utils.ts", "../../src/Rule.ts", "../../src/structures/LinkedItem.ts", "../../src/RuleIndex.ts", "../../src/PureAbility.ts", "../../src/matchers/conditions.ts", "../../src/matchers/field.ts", "../../src/Ability.ts", "../../src/AbilityBuilder.ts", "../../src/ForbiddenError.ts"], "sourcesContent": ["import { AnyObject, Subject, SubjectType, SubjectClass, ForcedSubject, AliasesMap } from './types';\n\nexport function wrapArray<T>(value: T[] | T): T[] {\n  return Array.isArray(value) ? value : [value];\n}\n\nexport function setByPath(object: AnyObject, path: string, value: unknown): void {\n  let ref = object;\n  let lastKey = path;\n\n  if (path.indexOf('.') !== -1) {\n    const keys = path.split('.');\n\n    lastKey = keys.pop()!;\n    ref = keys.reduce((res, prop) => {\n      res[prop] = res[prop] || {};\n      return res[prop] as AnyObject;\n    }, object);\n  }\n\n  ref[lastKey] = value;\n}\n\nconst hasOwnProperty = (Object as any).hasOwn\n  || Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);\n\nconst TYPE_FIELD = '__caslSubjectType__';\nexport function setSubjectType<\n  T extends string,\n  U extends Record<PropertyKey, any>\n>(type: T, object: U): U & ForcedSubject<T> {\n  if (object) {\n    if (!hasOwnProperty(object, TYPE_FIELD)) {\n      Object.defineProperty(object, TYPE_FIELD, { value: type });\n    } else if (type !== object[TYPE_FIELD]) {\n      throw new Error(`Trying to cast object to subject type ${type} but previously it was casted to ${object[TYPE_FIELD]}`);\n    }\n  }\n\n  return object as U & ForcedSubject<T>;\n}\n\nexport const isSubjectType = (value: unknown): value is SubjectType => {\n  const type = typeof value;\n  return type === 'string' || type === 'function';\n};\n\nconst getSubjectClassName = (value: SubjectClass) => value.modelName || value.name;\nexport const getSubjectTypeName = (value: SubjectType) => {\n  return typeof value === 'string' ? value : getSubjectClassName(value);\n};\n\nexport function detectSubjectType(subject: Exclude<Subject, SubjectType>): string {\n  if (hasOwnProperty(subject, TYPE_FIELD)) {\n    return subject[TYPE_FIELD];\n  }\n\n  return getSubjectClassName(subject.constructor as SubjectClass);\n}\n\ntype AliasMerge = (actions: string[], action: string | string[]) => string[];\nfunction expandActions(aliasMap: AliasesMap, rawActions: string | string[], merge: AliasMerge) {\n  let actions = wrapArray(rawActions);\n  let i = 0;\n\n  while (i < actions.length) {\n    const action = actions[i++];\n\n    if (hasOwnProperty(aliasMap, action)) {\n      actions = merge(actions, aliasMap[action]);\n    }\n  }\n\n  return actions;\n}\n\nfunction findDuplicate(actions: string[], actionToFind: string | string[]) {\n  if (typeof actionToFind === 'string' && actions.indexOf(actionToFind) !== -1) {\n    return actionToFind;\n  }\n\n  for (let i = 0; i < actionToFind.length; i++) {\n    if (actions.indexOf(actionToFind[i]) !== -1) return actionToFind[i];\n  }\n\n  return null;\n}\n\nconst defaultAliasMerge: AliasMerge = (actions, action) => actions.concat(action);\nfunction validateForCycles(aliasMap: AliasesMap, reservedAction: string) {\n  if (reservedAction in aliasMap) {\n    throw new Error(`Cannot use \"${reservedAction}\" as an alias because it's reserved action.`);\n  }\n\n  const keys = Object.keys(aliasMap);\n  const mergeAliasesAndDetectCycles: AliasMerge = (actions, action) => {\n    const duplicate = findDuplicate(actions, action);\n    if (duplicate) throw new Error(`Detected cycle ${duplicate} -> ${actions.join(', ')}`);\n\n    const isUsingReservedAction = typeof action === 'string' && action === reservedAction\n      || actions.indexOf(reservedAction) !== -1\n      || Array.isArray(action) && action.indexOf(reservedAction) !== -1;\n    if (isUsingReservedAction) throw new Error(`Cannot make an alias to \"${reservedAction}\" because this is reserved action`);\n\n    return actions.concat(action);\n  };\n\n  for (let i = 0; i < keys.length; i++) {\n    expandActions(aliasMap, keys[i], mergeAliasesAndDetectCycles);\n  }\n}\n\nexport type AliasResolverOptions = { skipValidate?: boolean; anyAction?: string };\nexport function createAliasResolver(aliasMap: AliasesMap, options?: AliasResolverOptions) {\n  if (!options || options.skipValidate !== false) {\n    validateForCycles(aliasMap, options && options.anyAction || 'manage');\n  }\n\n  return (action: string | string[]) => expandActions(aliasMap, action, defaultAliasMerge);\n}\n\nfunction copyArrayTo<T>(dest: T[], target: T[], start: number) {\n  for (let i = start; i < target.length; i++) {\n    dest.push(target[i]);\n  }\n}\n\nexport function mergePrioritized<T extends { priority: number }>(\n  array?: T[],\n  anotherArray?: T[]\n): T[] {\n  if (!array || !array.length) {\n    return anotherArray || [];\n  }\n\n  if (!anotherArray || !anotherArray.length) {\n    return array || [];\n  }\n\n  let i = 0;\n  let j = 0;\n  const merged: T[] = [];\n\n  while (i < array.length && j < anotherArray.length) {\n    if (array[i].priority < anotherArray[j].priority) {\n      merged.push(array[i]);\n      i++;\n    } else {\n      merged.push(anotherArray[j]);\n      j++;\n    }\n  }\n\n  copyArrayTo(merged, array, i);\n  copyArrayTo(merged, anotherArray, j);\n\n  return merged;\n}\n\nexport function getOrDefault<K, V>(map: Map<K, V>, key: K, defaultValue: () => V) {\n  let value = map.get(key);\n\n  if (!value) {\n    value = defaultValue();\n    map.set(key, value);\n  }\n\n  return value;\n}\n\nexport const identity = <T>(x: T) => x;\n", "import { wrapArray, isSubjectType } from './utils';\nimport {\n  MatchConditions,\n  MatchField,\n  Abilities,\n  ToAbilityTypes,\n  Normalize,\n  ConditionsMatcher,\n  FieldMatcher,\n} from './types';\nimport { RawRule, RawRuleFrom } from './RawRule';\n\ntype Tuple<A extends Abilities> = Normalize<ToAbilityTypes<A>>;\n\nfunction validate(rule: RawRuleFrom<Abilities, any>, options: RuleOptions<any>) {\n  if (Array.isArray(rule.fields) && !rule.fields.length) {\n    throw new Error('`rawRule.fields` cannot be an empty array. https://bit.ly/390miLa');\n  }\n\n  if (rule.fields && !options.fieldMatcher) {\n    throw new Error('You need to pass \"fieldMatcher\" option in order to restrict access by fields');\n  }\n\n  if (rule.conditions && !options.conditionsMatcher) {\n    throw new Error('You need to pass \"conditionsMatcher\" option in order to restrict access by conditions');\n  }\n}\n\nexport interface RuleOptions<Conditions> {\n  conditionsMatcher?: ConditionsMatcher<Conditions>\n  fieldMatcher?: Field<PERSON>atch<PERSON>\n  resolveAction(action: string | string[]): string | string[]\n}\n\nexport class Rule<A extends Abilities, C> {\n  private _matchConditions: MatchConditions | undefined;\n  private _matchField: MatchField<string> | undefined;\n  private readonly _options!: RuleOptions<C>;\n  public readonly action!: Tuple<A>[0] | Tuple<A>[0][];\n  public readonly subject!: Tuple<A>[1] | Tuple<A>[1][];\n  public readonly inverted!: boolean;\n  public readonly conditions!: C | undefined;\n  public readonly fields!: string[] | undefined;\n  public readonly reason!: string | undefined;\n  public readonly origin!: RawRule<ToAbilityTypes<A>, C>;\n  public readonly priority!: number;\n\n  constructor(\n    rule: RawRule<ToAbilityTypes<A>, C>,\n    options: RuleOptions<C>,\n    priority: number = 0\n  ) {\n    validate(rule, options);\n\n    this.action = options.resolveAction(rule.action);\n    this.subject = rule.subject!;\n    this.inverted = !!rule.inverted;\n    this.conditions = rule.conditions;\n    this.reason = rule.reason;\n    this.origin = rule;\n    this.fields = rule.fields ? wrapArray(rule.fields) : undefined;\n    this.priority = priority;\n    this._options = options;\n  }\n\n  private _conditionsMatcher() {\n    if (this.conditions && !this._matchConditions) {\n      this._matchConditions = this._options.conditionsMatcher!(this.conditions);\n    }\n\n    return this._matchConditions!;\n  }\n\n  get ast() {\n    const matches = this._conditionsMatcher();\n    return matches ? matches.ast : undefined;\n  }\n\n  matchesConditions(object: Normalize<A>[1] | undefined): boolean {\n    if (!this.conditions) {\n      return true;\n    }\n\n    if (!object || isSubjectType(object)) {\n      return !this.inverted;\n    }\n\n    const matches = this._conditionsMatcher();\n    return matches(object as Record<string, unknown>);\n  }\n\n  matchesField(field: string | undefined): boolean {\n    if (!this.fields) {\n      return true;\n    }\n\n    if (!field) {\n      return !this.inverted;\n    }\n\n    if (this.fields && !this._matchField) {\n      this._matchField = this._options.fieldMatcher!(this.fields);\n    }\n\n    return this._matchField!(field);\n  }\n}\n", "export interface LinkedItem<T> {\n  next: LinkedItem<T> | null\n  prev: LinkedItem<T> | null\n  readonly value: T\n}\n\nexport function linkedItem<T>(value: T, prev: LinkedItem<T>['prev']) {\n  const item = { value, prev, next: null };\n\n  if (prev) {\n    prev.next = item;\n  }\n\n  return item;\n}\n\nexport function unlinkItem(item: LinkedItem<any>) {\n  if (item.next) {\n    item.next.prev = item.prev;\n  }\n\n  if (item.prev) {\n    item.prev.next = item.next;\n  }\n\n  item.next = item.prev = null; // eslint-disable-line\n}\n\nexport const cloneLinkedItem = <T extends LinkedItem<any>>(item: T): T => ({\n  value: item.value,\n  prev: item.prev,\n  next: item.next,\n} as T);\n", "import { Rule, RuleOptions } from './Rule';\nimport { RawRuleFrom } from './RawRule';\nimport {\n  Abilities,\n  Normalize,\n  SubjectType,\n  AbilityParameters,\n  AbilityTuple,\n  ExtractSubjectType\n} from './types';\nimport { wrapArray, detectSubjectType, mergePrioritized, getOrDefault, identity, isSubjectType } from './utils';\nimport { LinkedItem, linkedItem, unlinkItem, cloneLinkedItem } from './structures/LinkedItem';\n\nexport interface RuleIndexOptions<A extends Abilities, C> extends Partial<RuleOptions<C>> {\n  detectSubjectType?(\n    subject: Exclude<Normalize<A>[1], SubjectType>\n  ): ExtractSubjectType<Normalize<A>[1]>;\n  anyAction?: string;\n  anySubjectType?: string;\n}\n\nexport declare const ɵabilities: unique symbol;\nexport declare const ɵconditions: unique symbol;\ninterface WithGenerics {\n  [ɵabilities]: any\n  [ɵconditions]: any\n}\nexport type Public<T extends WithGenerics> = { [K in keyof T]: T[K] };\nexport interface Generics<T extends WithGenerics> {\n  abilities: T[typeof ɵabilities],\n  conditions: T[typeof ɵconditions]\n}\n\nexport type RuleOf<T extends WithGenerics> =\n  Rule<Generics<T>['abilities'], Generics<T>['conditions']>;\nexport type RawRuleOf<T extends WithGenerics> =\n  RawRuleFrom<Generics<T>['abilities'], Generics<T>['conditions']>;\n\nexport type RuleIndexOptionsOf<T extends WithGenerics> =\n  RuleIndexOptions<Generics<T>['abilities'], Generics<T>['conditions']>;\n\ninterface AbilityEvent<T extends WithGenerics> {\n  target: T\n  /** @deprecated use \"target\" property instead */\n  ability: T\n}\n\nexport interface UpdateEvent<T extends WithGenerics> extends AbilityEvent<T> {\n  rules: RawRuleOf<T>[]\n}\n/**\n * @deprecated `on`/`emit` properly infer type without this type\n * TODO(major): delete\n */\nexport type EventHandler<Event> = (event: Event) => void;\n\nexport type Events<\n  T extends WithGenerics,\n  K extends keyof EventsMap<T> = keyof EventsMap<T>\n> = Map<K, LinkedItem<EventsMap<T>[K]> | null>;\n\ninterface EventsMap<T extends WithGenerics> {\n  update(event: UpdateEvent<T>): void\n  updated(event: UpdateEvent<T>): void\n}\n\ntype IndexTree<A extends Abilities, C> = Map<SubjectType, Map<string, {\n  rules: Rule<A, C>[],\n  merged: boolean\n}>>;\n\nexport type Unsubscribe = () => void;\n\nconst defaultActionEntry = () => ({\n  rules: [] as unknown as Rule<any, any>[],\n  merged: false\n});\nconst defaultSubjectEntry = () => new Map<string, ReturnType<typeof defaultActionEntry>>();\n\ntype AbilitySubjectTypeParameters<T extends Abilities, IncludeField extends boolean = true> =\n  AbilityParameters<\n  T,\n  T extends AbilityTuple\n    ? IncludeField extends true\n      ? (action: T[0], subject: ExtractSubjectType<T[1]>, field?: string) => 0\n      : (action: T[0], subject: ExtractSubjectType<T[1]>) => 0\n    : never,\n  (action: Extract<T, string>) => 0\n  >;\n\nexport class RuleIndex<A extends Abilities, Conditions> {\n  private _hasPerFieldRules: boolean = false;\n  private _events?: Events<this>;\n  private _indexedRules: IndexTree<A, Conditions>;\n  private _rules: RawRuleFrom<A, Conditions>[];\n  private readonly _ruleOptions: RuleOptions<Conditions>;\n  private readonly _detectSubjectType: this['detectSubjectType'];\n  private readonly _anyAction: string;\n  private readonly _anySubjectType: string;\n  readonly [ɵabilities]!: A;\n  readonly [ɵconditions]!: Conditions;\n\n  constructor(\n    rules: RawRuleFrom<A, Conditions>[] = [],\n    options: RuleIndexOptions<A, Conditions> = {}\n  ) {\n    this._ruleOptions = {\n      conditionsMatcher: options.conditionsMatcher,\n      fieldMatcher: options.fieldMatcher,\n      resolveAction: options.resolveAction || identity,\n    };\n    this._anyAction = options.anyAction || 'manage';\n    this._anySubjectType = options.anySubjectType || 'all';\n    this._detectSubjectType = options.detectSubjectType || (detectSubjectType as this['detectSubjectType']);\n    this._rules = rules;\n    this._indexedRules = this._buildIndexFor(rules);\n  }\n\n  get rules() {\n    return this._rules;\n  }\n\n  detectSubjectType(object?: Normalize<A>[1]): ExtractSubjectType<Normalize<A>[1]> {\n    if (isSubjectType(object)) return object as ExtractSubjectType<Normalize<A>[1]>;\n    if (!object) return this._anySubjectType as ExtractSubjectType<Normalize<A>[1]>;\n    return this._detectSubjectType(object as Exclude<Normalize<A>[1], SubjectType>);\n  }\n\n  update(rules: RawRuleFrom<A, Conditions>[]): Public<this> {\n    const event = {\n      rules,\n      ability: this,\n      target: this\n    } as unknown as UpdateEvent<this>;\n\n    this._emit('update', event);\n    this._rules = rules;\n    this._indexedRules = this._buildIndexFor(rules);\n    this._emit('updated', event);\n\n    return this;\n  }\n\n  private _buildIndexFor(rawRules: RawRuleFrom<A, Conditions>[]) {\n    const indexedRules: IndexTree<A, Conditions> = new Map();\n\n    for (let i = rawRules.length - 1; i >= 0; i--) {\n      const priority = rawRules.length - i - 1;\n      const rule = new Rule(rawRules[i], this._ruleOptions, priority);\n      const actions = wrapArray(rule.action);\n      const subjects = wrapArray(rule.subject || this._anySubjectType);\n      if (!this._hasPerFieldRules && rule.fields) this._hasPerFieldRules = true;\n\n      for (let k = 0; k < subjects.length; k++) {\n        const subjectRules = getOrDefault(indexedRules, subjects[k], defaultSubjectEntry);\n\n        for (let j = 0; j < actions.length; j++) {\n          getOrDefault(subjectRules, actions[j], defaultActionEntry).rules.push(rule);\n        }\n      }\n    }\n\n    return indexedRules;\n  }\n\n  possibleRulesFor(...args: AbilitySubjectTypeParameters<A, false>): Rule<A, Conditions>[];\n  possibleRulesFor(\n    action: string,\n    subjectType: SubjectType = this._anySubjectType\n  ): Rule<A, Conditions>[] {\n    if (!isSubjectType(subjectType)) {\n      throw new Error('\"possibleRulesFor\" accepts only subject types (i.e., string or class) as the 2nd parameter');\n    }\n\n    const subjectRules = getOrDefault(this._indexedRules, subjectType, defaultSubjectEntry);\n    const actionRules = getOrDefault(subjectRules, action, defaultActionEntry);\n\n    if (actionRules.merged) {\n      return actionRules.rules;\n    }\n\n    const anyActionRules = action !== this._anyAction && subjectRules.has(this._anyAction)\n      ? subjectRules.get(this._anyAction)!.rules\n      : undefined;\n    let rules = mergePrioritized(actionRules.rules, anyActionRules);\n\n    if (subjectType !== this._anySubjectType) {\n      rules = mergePrioritized(rules, (this as any).possibleRulesFor(action, this._anySubjectType));\n    }\n\n    actionRules.rules = rules;\n    actionRules.merged = true;\n\n    return rules;\n  }\n\n  rulesFor(...args: AbilitySubjectTypeParameters<A>): Rule<A, Conditions>[];\n  rulesFor(action: string, subjectType?: SubjectType, field?: string): Rule<A, Conditions>[] {\n    const rules: Rule<A, Conditions>[] = (this as any).possibleRulesFor(action, subjectType);\n\n    if (field && typeof field !== 'string') {\n      throw new Error('The 3rd, `field` parameter is expected to be a string. See https://stalniy.github.io/casl/en/api/casl-ability#can-of-pure-ability for details');\n    }\n\n    if (!this._hasPerFieldRules) {\n      return rules;\n    }\n\n    return rules.filter(rule => rule.matchesField(field));\n  }\n\n  actionsFor(subjectType: ExtractSubjectType<Normalize<A>[1]>): string[] {\n    if (!isSubjectType(subjectType)) {\n      throw new Error('\"actionsFor\" accepts only subject types (i.e., string or class) as a parameter');\n    }\n\n    const actions = new Set<string>();\n\n    const subjectRules = this._indexedRules.get(subjectType);\n    if (subjectRules) {\n      Array.from(subjectRules.keys()).forEach(action => actions.add(action));\n    }\n\n    const anySubjectTypeRules = subjectType !== this._anySubjectType\n      ? this._indexedRules.get(this._anySubjectType)\n      : undefined;\n    if (anySubjectTypeRules) {\n      Array.from(anySubjectTypeRules.keys()).forEach(action => actions.add(action));\n    }\n\n    return Array.from(actions);\n  }\n\n  on<T extends keyof EventsMap<this>>(\n    event: T,\n    handler: EventsMap<Public<this>>[T]\n  ): Unsubscribe {\n    this._events = this._events || new Map();\n    const events = this._events;\n    const tail = events.get(event) || null;\n    const item = linkedItem(handler, tail);\n    events.set(event, item);\n\n    return () => {\n      const currentTail = events.get(event);\n\n      if (!item.next && !item.prev && currentTail === item) {\n        events.delete(event);\n      } else if (item === currentTail) {\n        events.set(event, item.prev);\n      }\n\n      unlinkItem(item);\n    };\n  }\n\n  private _emit<T extends keyof EventsMap<this>>(\n    name: T,\n    payload: Parameters<EventsMap<this>[T]>[0]\n  ) {\n    if (!this._events) return;\n\n    let current = this._events.get(name) || null;\n    while (current !== null) {\n      const prev = current.prev ? cloneLinkedItem(current.prev) : null;\n      current.value(payload);\n      current = prev;\n    }\n  }\n}\n", "import { RuleIndex, RuleIndexOptions, RuleIndexOptionsOf, Public, RawRuleOf } from './RuleIndex';\nimport { Abilities, AbilityTuple, CanParameters, Subject } from './types';\nimport { Rule } from './Rule';\n\nexport interface AbilityOptions<A extends Abilities, Conditions>\n  extends RuleIndexOptions<A, Conditions> {}\nexport interface AnyAbility extends Public<PureAbility<any, any>> {}\nexport interface AbilityOptionsOf<T extends AnyAbility> extends RuleIndexOptionsOf<T> {}\n\nexport type AbilityClass<T extends AnyAbility> = new (\n  rules?: RawRuleOf<T>[],\n  options?: AbilityOptionsOf<T>\n) => T;\n\nexport type CreateAbility<T extends AnyAbility> = (\n  rules?: RawRuleOf<T>[],\n  options?: AbilityOptionsOf<T>\n) => T;\n\nexport class PureAbility<\n  A extends Abilities = AbilityTuple,\n  Conditions = unknown\n> extends RuleIndex<A, Conditions> {\n  can(...args: CanParameters<A>): boolean;\n  can(action: string, subject?: Subject, field?: string): boolean {\n    const rule = (this as PrimitiveAbility).relevantRuleFor(action, subject, field);\n    return !!rule && !rule.inverted;\n  }\n\n  relevantRuleFor(...args: CanParameters<A>): Rule<A, Conditions> | null;\n  relevantRuleFor(action: string, subject?: Subject, field?: string): Rule<A, Conditions> | null {\n    const subjectType = this.detectSubjectType(subject);\n    const rules = (this as any).rulesFor(action, subjectType, field);\n\n    for (let i = 0, length = rules.length; i < length; i++) {\n      if (rules[i].matchesConditions(subject)) {\n        return rules[i];\n      }\n    }\n\n    return null;\n  }\n\n  cannot(...args: CanParameters<A>): boolean;\n  cannot(action: string, subject?: Subject, field?: string): boolean {\n    return !(this as PrimitiveAbility).can(action, subject, field);\n  }\n}\n\n/**\n * helper interface that helps to emit js methods that have static parameters\n */\ninterface PrimitiveAbility<A extends Abilities = AbilityTuple, Conditions = unknown> {\n  can(action: string, subject?: Subject, field?: string): boolean;\n  relevantRuleFor(action: string, subject?: Subject, field?: string): Rule<A, Conditions> | null\n}\n", "import {\n  $eq,\n  eq,\n  $ne,\n  ne,\n  $lt,\n  lt,\n  $lte,\n  lte,\n  $gt,\n  gt,\n  $gte,\n  gte,\n  $in,\n  within,\n  $nin,\n  nin,\n  $all,\n  all,\n  $size,\n  size,\n  $regex,\n  $options,\n  regex,\n  $elemMatch,\n  elemMatch,\n  $exists,\n  exists,\n  and,\n  createFactory,\n  BuildMongoQuery,\n  DefaultOperators,\n} from '@ucast/mongo2js';\nimport { ConditionsMatcher, AnyObject } from '../types';\nimport { Container, GenericFactory } from '../hkt';\n\nconst defaultInstructions = {\n  $eq,\n  $ne,\n  $lt,\n  $lte,\n  $gt,\n  $gte,\n  $in,\n  $nin,\n  $all,\n  $size,\n  $regex,\n  $options,\n  $elemMatch,\n  $exists,\n};\nconst defaultInterpreters = {\n  eq,\n  ne,\n  lt,\n  lte,\n  gt,\n  gte,\n  in: within,\n  nin,\n  all,\n  size,\n  regex,\n  elemMatch,\n  exists,\n  and,\n};\n\ninterface MongoQueryFactory extends GenericFactory {\n  produce: MongoQuery<this[0]>\n}\n\ntype MergeUnion<T, Keys extends keyof T = keyof T> = { [K in Keys]: T[K] };\nexport type MongoQuery<T = AnyObject> = BuildMongoQuery<MergeUnion<T>, {\n  toplevel: {},\n  field: Pick<DefaultOperators<MergeUnion<T>>['field'], keyof typeof defaultInstructions>\n}> & Container<MongoQueryFactory>;\n\ntype MongoQueryMatcherFactory =\n  (...args: Partial<Parameters<typeof createFactory>>) => ConditionsMatcher<MongoQuery>;\nexport const buildMongoQueryMatcher = ((instructions, interpreters, options) => createFactory(\n  { ...defaultInstructions, ...instructions },\n  { ...defaultInterpreters, ...interpreters },\n  options\n)) as MongoQueryMatcherFactory;\n\nexport const mongoQueryMatcher = createFactory(defaultInstructions, defaultInterpreters);\nexport type {\n  MongoQueryFieldOperators,\n  MongoQueryTopLevelOperators,\n  MongoQueryOperators,\n} from '@ucast/mongo2js';\n", "import { FieldMatcher } from '../types';\n\nconst REGEXP_SPECIAL_CHARS = /[-/\\\\^$+?.()|[\\]{}]/g;\nconst REGEXP_ANY = /\\.?\\*+\\.?/g;\nconst REGEXP_STARS = /\\*+/;\nconst REGEXP_DOT = /\\./g;\n\nfunction detectRegexpPattern(match: string, index: number, string: string): string {\n  const quantifier = string[0] === '*' || match[0] === '.' && match[match.length - 1] === '.'\n    ? '+'\n    : '*';\n  const matcher = match.indexOf('**') === -1 ? '[^.]' : '.';\n  const pattern = match.replace(REGEXP_DOT, '\\\\$&')\n    .replace(REGEXP_STARS, matcher + quantifier);\n\n  return index + match.length === string.length ? `(?:${pattern})?` : pattern;\n}\n\nfunction escapeRegexp(match: string, index: number, string: string): string {\n  if (match === '.' && (string[index - 1] === '*' || string[index + 1] === '*')) {\n    return match;\n  }\n\n  return `\\\\${match}`;\n}\n\nfunction createPattern(fields: string[]) {\n  const patterns = fields.map(field => field\n    .replace(REGEXP_SPECIAL_CHARS, escapeRegexp)\n    .replace(REGEXP_ANY, detectRegexpPattern));\n  const pattern = patterns.length > 1 ? `(?:${patterns.join('|')})` : patterns[0];\n\n  return new RegExp(`^${pattern}$`);\n}\n\nexport const fieldPatternMatcher: FieldMatcher = (fields) => {\n  let pattern: RegExp | null;\n\n  return (field) => {\n    if (typeof pattern === 'undefined') {\n      pattern = fields.every(f => f.indexOf('*') === -1)\n        ? null\n        : createPattern(fields);\n    }\n\n    return pattern === null\n      ? fields.indexOf(field) !== -1\n      : pattern.test(field);\n  };\n};\n", "import { PureAbility, AbilityOptions, AbilityOptionsOf } from './PureAbility';\nimport { RawRuleFrom } from './RawRule';\nimport { AbilityTuple } from './types';\nimport { MongoQuery, mongoQueryMatcher } from './matchers/conditions';\nimport { fieldPatternMatcher } from './matchers/field';\nimport { Public, RawRuleOf } from './RuleIndex';\n\n/**\n * @deprecated use createMongoAbility function instead and MongoAbility<Abilities> interface.\n * In the next major version PureAbility will be renamed to Ability and this class will be removed\n */\nexport class Ability<\n  A extends AbilityTuple = AbilityTuple,\n  C extends MongoQuery = MongoQuery\n> extends PureAbility<A, C> {\n  constructor(rules: RawRuleFrom<A, C>[] = [], options: AbilityOptions<A, C> = {}) {\n    super(rules, {\n      conditionsMatcher: mongoQueryMatcher,\n      fieldMatcher: fieldPatternMatcher,\n      ...options,\n    });\n  }\n}\n\nexport interface AnyMongoAbility extends Public<PureAbility<any, MongoQuery>> {}\nexport interface MongoAbility<\n  A extends AbilityTuple = AbilityTuple,\n  C extends MongoQuery = MongoQuery\n> extends PureAbility<A, C> {}\n\nexport function createMongoAbility<\n  T extends AnyMongoAbility = MongoAbility\n>(rules?: RawRuleOf<T>[], options?: AbilityOptionsOf<T>): T;\nexport function createMongoAbility<\n  A extends AbilityTuple = AbilityTuple,\n  C extends MongoQuery = MongoQuery\n>(rules?: RawRuleFrom<A, C>[], options?: AbilityOptions<A, C>): MongoAbility<A, C>;\nexport function createMongoAbility(rules: any[] = [], options = {}): AnyMongoAbility {\n  return new PureAbility(rules, {\n    conditionsMatcher: mongoQueryMatcher,\n    fieldMatcher: fieldPatternMatcher,\n    ...options,\n  });\n}\n", "import { AnyMongoAbility, createMongoAbility, MongoAbility } from './Ability';\nimport { ProduceGeneric } from './hkt';\nimport { AbilityOptionsOf, AnyAbility } from './PureAbility';\nimport { Generics, RawRuleOf } from './RuleIndex';\nimport {\n  AbilityTuple, AnyClass, AnyObject, ExtractSubjectType as E, Normalize, SubjectType,\n  TaggedInterface\n} from './types';\n\nfunction isAbilityClass(factory: AbilityFactory<any>): factory is AnyClass {\n  return typeof factory.prototype.possibleRulesFor === 'function';\n}\n\nclass RuleBuilder<T extends AnyAbility> {\n  public _rule!: RawRuleOf<T>;\n\n  constructor(rule: RawRuleOf<T>) {\n    this._rule = rule;\n  }\n\n  because(reason: string): this {\n    this._rule.reason = reason;\n    return this;\n  }\n}\n\ntype AbilityFactory<T extends AnyAbility> = AnyClass<T> | ((rules?: any[], options?: any) => T);\ntype InstanceOf<T extends AnyAbility, S extends SubjectType> = S extends AnyClass<infer R>\n  ? R\n  : S extends (...args: any[]) => infer O\n    ? O\n    : S extends string\n      ? Exclude<Normalize<Generics<T>['abilities']>[1], SubjectType> extends TaggedInterface<string>\n        ? Extract<Normalize<Generics<T>['abilities']>[1], TaggedInterface<S>>\n        : AnyObject\n      : never;\ntype ConditionsOf<T extends AnyAbility, I extends {}> =\n  ProduceGeneric<Generics<T>['conditions'], I>;\ntype ActionFrom<T extends AbilityTuple, S extends SubjectType> = T extends any\n  ? S extends Extract<T[1], SubjectType> ? T[0] : never\n  : never;\ntype ActionOf<T extends AnyAbility, S extends SubjectType> = ActionFrom<Generics<T>['abilities'], S>;\ntype SubjectTypeOf<T extends AnyAbility> = E<Normalize<Generics<T>['abilities']>[1]>;\n\ntype SimpleCanParams<T extends AnyAbility> = Parameters<(\n  action: Generics<T>['abilities'] | Generics<T>['abilities'][]\n) => 0>;\ntype BuilderCanParameters<\n  S extends SubjectType,\n  I extends InstanceOf<T, S>,\n  T extends AnyAbility\n> = Generics<T>['abilities'] extends AbilityTuple\n  ? Parameters<(\n    action: ActionOf<T, S> | ActionOf<T, S>[],\n    subject: S | S[],\n    conditions?: ConditionsOf<T, I>\n  ) => 0>\n  : SimpleCanParams<T>;\n\ntype BuilderCanParametersWithFields<\n  S extends SubjectType,\n  I extends InstanceOf<T, S>,\n  F extends string,\n  T extends AnyAbility\n> = Generics<T>['abilities'] extends AbilityTuple\n  ? Parameters<(\n    action: ActionOf<T, S> | ActionOf<T, S>[],\n    subject: S | S[],\n    fields?: F | F[],\n    conditions?: ConditionsOf<T, I>\n  ) => 0>\n  : SimpleCanParams<T>;\ntype Keys<T> = string & keyof T;\n\ntype AddRule<T extends AnyAbility> = {\n  <\n    I extends InstanceOf<T, S>,\n    F extends string = Keys<I>,\n    S extends SubjectTypeOf<T> = SubjectTypeOf<T>\n  >(...args: BuilderCanParametersWithFields<S, I, F | Keys<I>, T>): RuleBuilder<T>;\n  <\n    I extends InstanceOf<T, S>,\n    S extends SubjectTypeOf<T> = SubjectTypeOf<T>\n  >(...args: BuilderCanParameters<S, I, T>): RuleBuilder<T>;\n};\n\nexport class AbilityBuilder<T extends AnyAbility> {\n  public rules: RawRuleOf<T>[] = [];\n  private readonly _createAbility: AbilityFactory<T>;\n  public can: AddRule<T>;\n  public cannot: AddRule<T>;\n  public build: (options?: AbilityOptionsOf<T>) => T;\n\n  constructor(AbilityType: AbilityFactory<T>) {\n    this._createAbility = AbilityType;\n\n    this.can = (\n      action: string | string[],\n      subject?: SubjectType | SubjectType[],\n      conditionsOrFields?: string | string[] | Generics<T>['conditions'],\n      conditions?: Generics<T>['conditions']\n    ) => this._addRule(action, subject, conditionsOrFields, conditions, false);\n    this.cannot = (\n      action: string | string[],\n      subject?: SubjectType | SubjectType[],\n      conditionsOrFields?: string | string[] | Generics<T>['conditions'],\n      conditions?: Generics<T>['conditions']\n    ) => this._addRule(action, subject, conditionsOrFields, conditions, true);\n\n    this.build = options => (isAbilityClass(this._createAbility)\n      ? new this._createAbility(this.rules, options)\n      : this._createAbility(this.rules, options));\n  }\n\n  private _addRule(\n    action: string | string[],\n    subject?: SubjectType | SubjectType[],\n    conditionsOrFields?: string | string[] | Generics<T>['conditions'],\n    conditions?: Generics<T>['conditions'],\n    inverted?: boolean\n  ): RuleBuilder<T> {\n    const rule = { action } as RawRuleOf<T>;\n\n    if (inverted) rule.inverted = inverted;\n    if (subject) {\n      rule.subject = subject;\n\n      if (Array.isArray(conditionsOrFields) || typeof conditionsOrFields === 'string') {\n        rule.fields = conditionsOrFields;\n      } else if (typeof conditionsOrFields !== 'undefined') {\n        rule.conditions = conditionsOrFields;\n      }\n\n      if (typeof conditions !== 'undefined') {\n        rule.conditions = conditions;\n      }\n    }\n\n    this.rules.push(rule);\n    return new RuleBuilder(rule);\n  }\n}\n\ntype DSL<T extends AnyAbility, R> = (\n  can: AbilityBuilder<T>['can'],\n  cannot: AbilityBuilder<T>['cannot']\n) => R;\n\nexport function defineAbility<\n  T extends AnyMongoAbility = MongoAbility\n>(define: DSL<T, Promise<void>>, options?: AbilityOptionsOf<T>): Promise<T>;\nexport function defineAbility<\n  T extends AnyMongoAbility = MongoAbility\n>(define: DSL<T, void>, options?: AbilityOptionsOf<T>): T;\nexport function defineAbility<\n  T extends AnyMongoAbility\n>(define: DSL<T, void | Promise<void>>, options?: AbilityOptionsOf<T>): T | Promise<T> {\n  const builder = new AbilityBuilder<T>(createMongoAbility);\n  const result = define(builder.can, builder.cannot);\n\n  if (result && typeof result.then === 'function') {\n    return result.then(() => builder.build(options));\n  }\n\n  return builder.build(options);\n}\n", "import { AnyAbility } from './PureAbility';\nimport { Normalize, Subject } from './types';\nimport { Generics } from './RuleIndex';\nimport { getSubjectTypeName } from './utils';\n\nexport type GetErrorMessage = (error: ForbiddenError<AnyAbility>) => string;\n/** @deprecated will be removed in the next major release */\nexport const getDefaultErrorMessage: GetErrorMessage = error => `Cannot execute \"${error.action}\" on \"${error.subjectType}\"`;\n\nconst NativeError = function NError(this: Error, message: string) {\n  this.message = message;\n} as unknown as new (message: string) => Error;\n\nNativeError.prototype = Object.create(Error.prototype);\n\nexport class ForbiddenError<T extends AnyAbility> extends NativeError {\n  public readonly ability!: T;\n  public action!: Normalize<Generics<T>['abilities']>[0];\n  public subject!: Generics<T>['abilities'][1];\n  public field?: string;\n  public subjectType!: string;\n\n  static _defaultErrorMessage = getDefaultErrorMessage;\n\n  static setDefaultMessage(messageOrFn: string | GetErrorMessage) {\n    this._defaultErrorMessage = typeof messageOrFn === 'string' ? () => messageOrFn : messageOrFn;\n  }\n\n  static from<U extends AnyAbility>(ability: U): ForbiddenError<U> {\n    return new this<U>(ability);\n  }\n\n  private constructor(ability: T) {\n    super('');\n    this.ability = ability;\n\n    if (typeof Error.captureStackTrace === 'function') {\n      this.name = 'ForbiddenError';\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n\n  setMessage(message: string): this {\n    this.message = message;\n    return this;\n  }\n\n  throwUnlessCan(...args: Parameters<T['can']>): void;\n  throwUnlessCan(action: string, subject?: Subject, field?: string): void {\n    const error = (this as any).unlessCan(action, subject, field);\n    if (error) throw error;\n  }\n\n  unlessCan(...args: Parameters<T['can']>): this | undefined;\n  unlessCan(action: string, subject?: Subject, field?: string): this | undefined {\n    const rule = this.ability.relevantRuleFor(action, subject, field);\n\n    if (rule && !rule.inverted) {\n      return;\n    }\n\n    this.action = action;\n    this.subject = subject;\n    this.subjectType = getSubjectTypeName(this.ability.detectSubjectType(subject));\n    this.field = field;\n\n    const reason = rule ? rule.reason : '';\n    // eslint-disable-next-line no-underscore-dangle\n    this.message = this.message || reason || (this.constructor as any)._defaultErrorMessage(this);\n    return this; // eslint-disable-line consistent-return\n  }\n}\n"], "names": ["wrapArray", "value", "Array", "isArray", "hasOwnProperty", "Object", "hasOwn", "prototype", "call", "bind", "TYPE_FIELD", "setSubjectType", "type", "object", "defineProperty", "Error", "isSubjectType", "getSubjectClassName", "modelName", "name", "getSubjectTypeName", "detectSubjectType", "subject", "constructor", "expandActions", "aliasMap", "rawActions", "merge", "actions", "i", "length", "action", "findDuplicate", "actionToFind", "indexOf", "defaultAliasMerge", "concat", "validateForCycles", "reservedAction", "keys", "mergeAliasesAndDetectCycles", "duplicate", "join", "isUsingReservedAction", "createAliasResolver", "options", "skipValidate", "anyAction", "copyArrayTo", "dest", "target", "start", "push", "mergePrioritized", "array", "anotherArray", "j", "merged", "priority", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "key", "defaultValue", "get", "set", "identity", "x", "validate", "rule", "fields", "field<PERSON><PERSON><PERSON>", "conditions", "conditionsM<PERSON>er", "Rule", "this", "resolveAction", "inverted", "reason", "origin", "undefined", "_options", "_proto", "_conditionsMatcher", "_matchConditions", "matchesConditions", "matches", "matchesField", "field", "_matchField", "_createClass", "ast", "linkedItem", "prev", "item", "next", "unlinkItem", "cloneLinkedItem", "defaultActionEntry", "rules", "defaultSubjectEntry", "Map", "RuleIndex", "_hasPerFieldRules", "_ruleOptions", "_anyAction", "_anySubjectType", "anySubjectType", "_detectSubjectType", "_rules", "_indexedRules", "_buildIndexFor", "update", "event", "ability", "_emit", "rawRules", "indexedRules", "subjects", "k", "subjectRules", "possibleRulesFor", "subjectType", "actionRules", "anyActionRules", "has", "rulesFor", "filter", "actionsFor", "Set", "from", "for<PERSON>ach", "add", "anySubjectTypeRules", "on", "handler", "_events", "events", "tail", "currentTail", "delete", "payload", "current", "PureAbility", "_RuleIndex", "_inherits<PERSON><PERSON>e", "apply", "arguments", "can", "relevantRuleFor", "cannot", "defaultInstructions", "$eq", "$ne", "$lt", "$lte", "$gt", "$gte", "$in", "$nin", "$all", "$size", "$regex", "$options", "$elemMatch", "$exists", "defaultInterpreters", "eq", "ne", "lt", "lte", "gt", "gte", "in", "within", "nin", "all", "size", "regex", "elemMatch", "exists", "and", "buildMongoQueryMatcher", "instructions", "interpreters", "createFactory", "_extends", "mongoQueryM<PERSON>er", "REGEXP_SPECIAL_CHARS", "REGEXP_ANY", "REGEXP_STARS", "REGEXP_DOT", "detectRegexpPattern", "match", "index", "string", "quantifier", "matcher", "pattern", "replace", "escapeRegexp", "createPattern", "patterns", "RegExp", "fieldPatternMatcher", "every", "f", "test", "Ability", "_PureAbility", "createMongoAbility", "isAbilityClass", "factory", "RuleBuilder", "_rule", "because", "AbilityBuilder", "AbilityType", "_this", "_createAbility", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "_addRule", "build", "_proto2", "defineAbility", "define", "builder", "result", "then", "getDefaultErrorMessage", "error", "NativeError", "NError", "message", "create", "ForbiddenError", "_NativeError", "setDefaultMessage", "messageOrFn", "_defaultErrorMessage", "captureStackTrace", "_assertThisInitialized", "setMessage", "throwUnlessCan", "unlessCan"], "mappings": "y/CAEO,SAASA,EAAaC,GAC3B,OAAOC,MAAMC,QAAQF,GAASA,EAAQ,CAACA,EACzC,CAmBA,IAAMG,EAAkBC,OAAeC,QAClCD,OAAOE,UAAUH,eAAeI,KAAKC,KAAKJ,OAAOE,UAAUH,gBAEhE,IAAMM,EAAa,sBACZ,SAASC,EAGdC,EAASC,GACT,GAAIA,EACF,IAAKT,EAAeS,EAAQH,GAC1BL,OAAOS,eAAeD,EAAQH,EAAY,CAAET,MAAOW,SAC9C,GAAIA,IAASC,EAAOH,GACzB,MAAM,IAAIK,MAA+CH,yCAAAA,sCAAwCC,EAAOH,IAI5G,OAAOG,CACT,CAEO,IAAMG,EAAgB,SAAhBA,EAAiBf,GAC5B,IAAMW,SAAcX,EACpB,MAAgB,WAATW,GAA8B,aAATA,CAC9B,EAEA,IAAMK,EAAsB,SAAtBA,EAAuBhB,GAAmB,OAAKA,EAAMiB,WAAajB,EAAMkB,IAAI,EAC3E,IAAMC,EAAqB,SAArBA,EAAsBnB,GACjC,MAAwB,kBAAVA,EAAqBA,EAAQgB,EAAoBhB,EACjE,EAEO,SAASoB,EAAkBC,GAChC,GAAIlB,EAAekB,EAASZ,GAC1B,OAAOY,EAAQZ,GAGjB,OAAOO,EAAoBK,EAAQC,YACrC,CAGA,SAASC,EAAcC,EAAsBC,EAA+BC,GAC1E,IAAIC,EAAU5B,EAAU0B,GACxB,IAAIG,EAAI,EAER,MAAOA,EAAID,EAAQE,OAAQ,CACzB,IAAMC,EAASH,EAAQC,KAEvB,GAAIzB,EAAeqB,EAAUM,GAC3BH,EAAUD,EAAMC,EAASH,EAASM,GAEtC,CAEA,OAAOH,CACT,CAEA,SAASI,EAAcJ,EAAmBK,GACxC,GAA4B,kBAAjBA,IAAgE,IAAnCL,EAAQM,QAAQD,GACtD,OAAOA,EAGT,IAAK,IAAIJ,EAAI,EAAGA,EAAII,EAAaH,OAAQD,IACvC,IAA0C,IAAtCD,EAAQM,QAAQD,EAAaJ,IAAY,OAAOI,EAAaJ,GAGnE,OAAO,IACT,CAEA,IAAMM,EAAgC,SAAhCA,EAAiCP,EAASG,GAAM,OAAKH,EAAQQ,OAAOL,EAAO,EACjF,SAASM,EAAkBZ,EAAsBa,GAC/C,GAAIA,KAAkBb,EACpB,MAAM,IAAIV,MAAqBuB,eAAAA,EAA4D,gDAG7F,IAAMC,EAAOlC,OAAOkC,KAAKd,GACzB,IAAMe,EAA0C,SAA1CA,EAA2CZ,EAASG,GACxD,IAAMU,EAAYT,EAAcJ,EAASG,GACzC,GAAIU,EAAW,MAAM,IAAI1B,MAAwB0B,kBAAAA,EAAgBb,OAAAA,EAAQc,KAAK,OAE9E,IAAMC,EAA0C,kBAAXZ,GAAuBA,IAAWO,IAC7B,IAArCV,EAAQM,QAAQI,IAChBpC,MAAMC,QAAQ4B,KAA+C,IAApCA,EAAOG,QAAQI,GAC7C,GAAIK,EAAuB,MAAM,IAAI5B,MAAK,4BAA6BuB,EAAkD,qCAEzH,OAAOV,EAAQQ,OAAOL,IAGxB,IAAK,IAAIF,EAAI,EAAGA,EAAIU,EAAKT,OAAQD,IAC/BL,EAAcC,EAAUc,EAAKV,GAAIW,EAErC,CAGO,SAASI,EAAoBnB,EAAsBoB,GACxD,IAAKA,GAAoC,QAAzBA,EAAQC,aACtBT,EAAkBZ,EAAUoB,GAAWA,EAAQE,WAAa,UAG9D,OAAO,SAAChB,GAAyB,OAAKP,EAAcC,EAAUM,EAAQI,EAAkB,CAC1F,CAEA,SAASa,EAAeC,EAAWC,EAAaC,GAC9C,IAAK,IAAItB,EAAIsB,EAAOtB,EAAIqB,EAAOpB,OAAQD,IACrCoB,EAAKG,KAAKF,EAAOrB,GAErB,CAEO,SAASwB,EACdC,EACAC,GAEA,IAAKD,IAAUA,EAAMxB,OACnB,OAAOyB,GAAgB,GAGzB,IAAKA,IAAiBA,EAAazB,OACjC,OAAOwB,GAAS,GAGlB,IAAIzB,EAAI,EACR,IAAI2B,EAAI,EACR,IAAMC,EAAc,GAEpB,MAAO5B,EAAIyB,EAAMxB,QAAU0B,EAAID,EAAazB,OAC1C,GAAIwB,EAAMzB,GAAG6B,SAAWH,EAAaC,GAAGE,SAAU,CAChDD,EAAOL,KAAKE,EAAMzB,IAClBA,GACF,KAAO,CACL4B,EAAOL,KAAKG,EAAaC,IACzBA,GACF,CAGFR,EAAYS,EAAQH,EAAOzB,GAC3BmB,EAAYS,EAAQF,EAAcC,GAElC,OAAOC,CACT,CAEO,SAASE,EAAmBC,EAAgBC,EAAQC,GACzD,IAAI7D,EAAQ2D,EAAIG,IAAIF,GAEpB,IAAK5D,EAAO,CACVA,EAAQ6D,IACRF,EAAII,IAAIH,EAAK5D,EACf,CAEA,OAAOA,CACT,CAEO,IAAMgE,EAAW,SAAXA,EAAeC,GAAI,OAAKA,CAAC,EC5JtC,SAASC,GAASC,EAAmCvB,GACnD,GAAI3C,MAAMC,QAAQiE,EAAKC,UAAYD,EAAKC,OAAOvC,OAC7C,MAAM,IAAIf,MAAM,qEAGlB,GAAIqD,EAAKC,SAAWxB,EAAQyB,aAC1B,MAAM,IAAIvD,MAAM,gFAGlB,GAAIqD,EAAKG,aAAe1B,EAAQ2B,kBAC9B,MAAM,IAAIzD,MAAM,wFAEpB,CAQA,IAAa0D,GAAI,WAaf,SAAAA,EACEL,EACAvB,EACAa,GACA,QADgB,IAAhBA,EAAAA,EAAmB,EAEnBS,GAASC,EAAMvB,GAEf6B,KAAK3C,OAASc,EAAQ8B,cAAcP,EAAKrC,QACzC2C,KAAKpD,QAAU8C,EAAK9C,QACpBoD,KAAKE,WAAaR,EAAKQ,SACvBF,KAAKH,WAAaH,EAAKG,WACvBG,KAAKG,OAAST,EAAKS,OACnBH,KAAKI,OAASV,EACdM,KAAKL,OAASD,EAAKC,OAASrE,EAAUoE,EAAKC,aAAUU,EACrDL,KAAKhB,SAAWA,EAChBgB,KAAKM,EAAWnC,CAClB,CAAC,IAAAoC,EAAAR,EAAAlE,UAAA0E,EAEOC,EAAR,SAAAA,IACE,GAAIR,KAAKH,aAAeG,KAAKS,EAC3BT,KAAKS,EAAmBT,KAAKM,EAASR,kBAAmBE,KAAKH,YAGhE,OAAOG,KAAKS,GACbF,EAODG,kBAAA,SAAAA,EAAkBvE,GAChB,IAAK6D,KAAKH,WACR,OAAO,KAGT,IAAK1D,GAAUG,EAAcH,GAC3B,OAAQ6D,KAAKE,SAGf,IAAMS,EAAUX,KAAKQ,IACrB,OAAOG,EAAQxE,IAChBoE,EAEDK,aAAA,SAAAA,EAAaC,GACX,IAAKb,KAAKL,OACR,OAAO,KAGT,IAAKkB,EACH,OAAQb,KAAKE,SAGf,GAAIF,KAAKL,SAAWK,KAAKc,EACvBd,KAAKc,EAAcd,KAAKM,EAASV,aAAcI,KAAKL,QAGtD,OAAOK,KAAKc,EAAaD,IAC1BE,EAAAhB,EAAA,CAAA,CAAAZ,IAAA,MAAAE,IAhCD,SAAAA,IACE,IAAMsB,EAAUX,KAAKQ,IACrB,OAAOG,EAAUA,EAAQK,SAAMX,CACjC,KAAC,OAAAN,CAAA,CA1Cc,GC5BV,SAASkB,GAAc1F,EAAU2F,GACtC,IAAMC,EAAO,CAAE5F,MAAAA,EAAO2F,KAAAA,EAAME,KAAM,MAElC,GAAIF,EACFA,EAAKE,KAAOD,EAGd,OAAOA,CACT,CAEO,SAASE,GAAWF,GACzB,GAAIA,EAAKC,KACPD,EAAKC,KAAKF,KAAOC,EAAKD,KAGxB,GAAIC,EAAKD,KACPC,EAAKD,KAAKE,KAAOD,EAAKC,KAGxBD,EAAKC,KAAOD,EAAKD,KAAO,IAC1B,CAEO,IAAMI,GAAkB,SAAlBA,EAA8CH,GAAO,MAAS,CACzE5F,MAAO4F,EAAK5F,MACZ2F,KAAMC,EAAKD,KACXE,KAAMD,EAAKC,KACZ,ECyCD,IAAMG,GAAqB,SAArBA,IAAkB,MAAU,CAChCC,MAAO,GACPzC,OAAQ,MACT,EACD,IAAM0C,GAAsB,SAAtBA,IAAmB,OAAS,IAAIC,GAAoD,EAa1F,IAAaC,GAAS,WAYpB,SAAAA,EACEH,EACArD,GACA,QAFmC,IAAnCqD,EAAAA,EAAsC,GAAE,QACA,IAAxCrD,EAAAA,EAA2C,CAAA,EAAE6B,KAbvC4B,EAA6B,MAenC5B,KAAK6B,EAAe,CAClB/B,kBAAmB3B,EAAQ2B,kBAC3BF,aAAczB,EAAQyB,aACtBK,cAAe9B,EAAQ8B,eAAiBV,GAE1CS,KAAK8B,EAAa3D,EAAQE,WAAa,SACvC2B,KAAK+B,EAAkB5D,EAAQ6D,gBAAkB,MACjDhC,KAAKiC,EAAqB9D,EAAQxB,mBAAsBA,EACxDqD,KAAKkC,EAASV,EACdxB,KAAKmC,EAAgBnC,KAAKoC,EAAeZ,EAC3C,CAAC,IAAAjB,EAAAoB,EAAA9F,UAAA0E,EAMD5D,kBAAA,SAAAA,EAAkBR,GAChB,GAAIG,EAAcH,GAAS,OAAOA,EAClC,IAAKA,EAAQ,OAAO6D,KAAK+B,EACzB,OAAO/B,KAAKiC,EAAmB9F,IAChCoE,EAED8B,OAAA,SAAAA,EAAOb,GACL,IAAMc,EAAQ,CACZd,MAAAA,EACAe,QAASvC,KACTxB,OAAQwB,MAGVA,KAAKwC,EAAM,SAAUF,GACrBtC,KAAKkC,EAASV,EACdxB,KAAKmC,EAAgBnC,KAAKoC,EAAeZ,GACzCxB,KAAKwC,EAAM,UAAWF,GAEtB,OAAOtC,MACRO,EAEO6B,EAAR,SAAAA,EAAuBK,GACrB,IAAMC,EAAyC,IAAIhB,IAEnD,IAAK,IAAIvE,EAAIsF,EAASrF,OAAS,EAAGD,GAAK,EAAGA,IAAK,CAC7C,IAAM6B,EAAWyD,EAASrF,OAASD,EAAI,EACvC,IAAMuC,EAAO,IAAIK,GAAK0C,EAAStF,GAAI6C,KAAK6B,EAAc7C,GACtD,IAAM9B,EAAU5B,EAAUoE,EAAKrC,QAC/B,IAAMsF,EAAWrH,EAAUoE,EAAK9C,SAAWoD,KAAK+B,GAChD,IAAK/B,KAAK4B,GAAqBlC,EAAKC,OAAQK,KAAK4B,EAAoB,KAErE,IAAK,IAAIgB,EAAI,EAAGA,EAAID,EAASvF,OAAQwF,IAAK,CACxC,IAAMC,EAAe5D,EAAayD,EAAcC,EAASC,GAAInB,IAE7D,IAAK,IAAI3C,EAAI,EAAGA,EAAI5B,EAAQE,OAAQ0B,IAClCG,EAAa4D,EAAc3F,EAAQ4B,GAAIyC,IAAoBC,MAAM9C,KAAKgB,EAE1E,CACF,CAEA,OAAOgD,GACRnC,EAGDuC,iBAAA,SAAAA,EACEzF,EACA0F,GACuB,QADC,IAAxBA,EAAAA,EAA2B/C,KAAK+B,EAEhC,IAAKzF,EAAcyG,GACjB,MAAM,IAAI1G,MAAM,8FAGlB,IAAMwG,EAAe5D,EAAae,KAAKmC,EAAeY,EAAatB,IACnE,IAAMuB,EAAc/D,EAAa4D,EAAcxF,EAAQkE,IAEvD,GAAIyB,EAAYjE,OACd,OAAOiE,EAAYxB,MAGrB,IAAMyB,EAAiB5F,IAAW2C,KAAK8B,GAAce,EAAaK,IAAIlD,KAAK8B,GACvEe,EAAaxD,IAAIW,KAAK8B,GAAaN,WACnCnB,EACJ,IAAImB,EAAQ7C,EAAiBqE,EAAYxB,MAAOyB,GAEhD,GAAIF,IAAgB/C,KAAK+B,EACvBP,EAAQ7C,EAAiB6C,EAAQxB,KAAa8C,iBAAiBzF,EAAQ2C,KAAK+B,IAG9EiB,EAAYxB,MAAQA,EACpBwB,EAAYjE,OAAS,KAErB,OAAOyC,GACRjB,EAGD4C,SAAA,SAAAA,EAAS9F,EAAgB0F,EAA2BlC,GAClD,IAAMW,EAAgCxB,KAAa8C,iBAAiBzF,EAAQ0F,GAE5E,GAAIlC,GAA0B,kBAAVA,EAClB,MAAM,IAAIxE,MAAM,iJAGlB,IAAK2D,KAAK4B,EACR,OAAOJ,EAGT,OAAOA,EAAM4B,QAAO,SAAA1D,GAAI,OAAIA,EAAKkB,aAAaC,OAC/CN,EAED8C,WAAA,SAAAA,EAAWN,GACT,IAAKzG,EAAcyG,GACjB,MAAM,IAAI1G,MAAM,kFAGlB,IAAMa,EAAU,IAAIoG,IAEpB,IAAMT,EAAe7C,KAAKmC,EAAc9C,IAAI0D,GAC5C,GAAIF,EACFrH,MAAM+H,KAAKV,EAAahF,QAAQ2F,SAAQ,SAAAnG,GAAM,OAAIH,EAAQuG,IAAIpG,MAGhE,IAAMqG,EAAsBX,IAAgB/C,KAAK+B,EAC7C/B,KAAKmC,EAAc9C,IAAIW,KAAK+B,QAC5B1B,EACJ,GAAIqD,EACFlI,MAAM+H,KAAKG,EAAoB7F,QAAQ2F,SAAQ,SAAAnG,GAAM,OAAIH,EAAQuG,IAAIpG,MAGvE,OAAO7B,MAAM+H,KAAKrG,IACnBqD,EAEDoD,GAAA,SAAAA,EACErB,EACAsB,GAEA5D,KAAK6D,EAAU7D,KAAK6D,GAAW,IAAInC,IACnC,IAAMoC,EAAS9D,KAAK6D,EACpB,IAAME,EAAOD,EAAOzE,IAAIiD,IAAU,KAClC,IAAMnB,EAAOF,GAAW2C,EAASG,GACjCD,EAAOxE,IAAIgD,EAAOnB,GAElB,OAAO,WACL,IAAM6C,EAAcF,EAAOzE,IAAIiD,GAE/B,IAAKnB,EAAKC,OAASD,EAAKD,MAAQ8C,IAAgB7C,EAC9C2C,EAAOG,OAAO3B,QACT,GAAInB,IAAS6C,EAClBF,EAAOxE,IAAIgD,EAAOnB,EAAKD,MAGzBG,GAAWF,KAEdZ,EAEOiC,EAAR,SAAAA,EACE/F,EACAyH,GAEA,IAAKlE,KAAK6D,EAAS,OAEnB,IAAIM,EAAUnE,KAAK6D,EAAQxE,IAAI5C,IAAS,KACxC,MAAmB,OAAZ0H,EAAkB,CACvB,IAAMjD,EAAOiD,EAAQjD,KAAOI,GAAgB6C,EAAQjD,MAAQ,KAC5DiD,EAAQ5I,MAAM2I,GACdC,EAAUjD,CACZ,GACDH,EAAAY,EAAA,CAAA,CAAAxC,IAAA,QAAAE,IAtJD,SAAAA,IACE,OAAOW,KAAKkC,CACd,KAAC,OAAAP,CAAA,CA9BmB,GCvETyC,IAAAA,YAAWC,GAAAC,EAAAF,YAAAC,GAAA,SAAAD,cAAA,OAAAC,EAAAE,MAAAvE,KAAAwE,YAAAxE,IAAA,CAAA,IAAAO,EAAA6D,YAAAvI,UAAA0E,EAKtBkE,IAAA,SAAAA,EAAIpH,EAAgBT,EAAmBiE,GACrC,IAAMnB,EAAQM,KAA0B0E,gBAAgBrH,EAAQT,EAASiE,GACzE,QAASnB,IAASA,EAAKQ,UACxBK,EAGDmE,gBAAA,SAAAA,EAAgBrH,EAAgBT,EAAmBiE,GACjD,IAAMkC,EAAc/C,KAAKrD,kBAAkBC,GAC3C,IAAM4E,EAASxB,KAAamD,SAAS9F,EAAQ0F,EAAalC,GAE1D,IAAK,IAAI1D,EAAI,EAAGC,EAASoE,EAAMpE,OAAQD,EAAIC,EAAQD,IACjD,GAAIqE,EAAMrE,GAAGuD,kBAAkB9D,GAC7B,OAAO4E,EAAMrE,GAIjB,OAAO,MACRoD,EAGDoE,OAAA,SAAAA,EAAOtH,EAAgBT,EAAmBiE,GACxC,OAASb,KAA0ByE,IAAIpH,EAAQT,EAASiE,IACzD,OAAAuD,WAAA,EAxBOzC,ICcV,IAAMiD,GAAsB,CAC1BC,IAAAA,EACAC,IAAAA,EACAC,IAAAA,EACAC,KAAAA,EACAC,IAAAA,EACAC,KAAAA,EACAC,IAAAA,EACAC,KAAAA,EACAC,KAAAA,EACAC,MAAAA,EACAC,OAAAA,EACAC,SAAAA,EACAC,WAAAA,EACAC,QAAAA,GAEF,IAAMC,GAAsB,CAC1BC,GAAAA,EACAC,GAAAA,EACAC,GAAAA,EACAC,IAAAA,EACAC,GAAAA,EACAC,IAAAA,EACAC,GAAIC,EACJC,IAAAA,EACAC,IAAAA,EACAC,KAAAA,EACAC,MAAAA,EACAC,UAAAA,EACAC,OAAAA,EACAC,IAAAA,GAeK,IAAMC,GAA0B,SAA1BA,EAA2BC,EAAcC,EAAc1I,GAAO,OAAK2I,EAAaC,EAAA,CAAA,EACtFnC,GAAwBgC,GAAYG,KACpCpB,GAAwBkB,GAC7B1I,EACD,EAEM,IAAM6I,GAAoBF,EAAclC,GAAqBe,ICrFpE,IAAMsB,GAAuB,uBAC7B,IAAMC,GAAa,aACnB,IAAMC,GAAe,MACrB,IAAMC,GAAa,MAEnB,SAASC,GAAoBC,EAAeC,EAAeC,GACzD,IAAMC,EAA2B,MAAdD,EAAO,IAA2B,MAAbF,EAAM,IAA0C,MAA5BA,EAAMA,EAAMlK,OAAS,GAC7E,IACA,IACJ,IAAMsK,GAAmC,IAAzBJ,EAAM9J,QAAQ,MAAe,OAAS,IACtD,IAAMmK,EAAUL,EAAMM,QAAQR,GAAY,QACvCQ,QAAQT,GAAcO,EAAUD,GAEnC,OAAOF,EAAQD,EAAMlK,SAAWoK,EAAOpK,OAAM,MAASuK,EAAO,KAAOA,CACtE,CAEA,SAASE,GAAaP,EAAeC,EAAeC,GAClD,GAAc,MAAVF,IAAwC,MAAtBE,EAAOD,EAAQ,IAAoC,MAAtBC,EAAOD,EAAQ,IAChE,OAAOD,EAGT,MAAA,KAAYA,CACd,CAEA,SAASQ,GAAcnI,GACrB,IAAMoI,EAAWpI,EAAOT,KAAI,SAAA2B,GAAK,OAAIA,EAClC+G,QAAQX,GAAsBY,IAC9BD,QAAQV,GAAYG,OACvB,IAAMM,EAAUI,EAAS3K,OAAS,EAAU2K,MAAAA,EAAS/J,KAAK,SAAU+J,EAAS,GAE7E,OAAO,IAAIC,OAAWL,IAAAA,EAAW,IACnC,KAEaM,GAAoC,SAApCA,EAAqCtI,GAChD,IAAIgI,EAEJ,OAAO,SAAC9G,GACN,GAAuB,qBAAZ8G,EACTA,EAAUhI,EAAOuI,OAAM,SAAAC,GAAC,OAAwB,IAApBA,EAAE3K,QAAQ,IAAW,IAC7C,KACAsK,GAAcnI,GAGpB,OAAmB,OAAZgI,GACwB,IAA3BhI,EAAOnC,QAAQqD,GACf8G,EAAQS,KAAKvH,GAErB,ECtCawH,IAAAA,YAAOC,GAAAhE,EAAA+D,QAAAC,GAIlB,SAAAD,QAAY7G,EAAiCrD,GAAoC,QAA3C,IAA1BqD,EAAAA,EAA6B,GAAE,QAA+B,IAA7BrD,EAAAA,EAAgC,CAAA,EAAE,OAC7EmK,EAAAxM,KAAM0F,KAAAA,EAAKuF,EAAA,CACTjH,kBAAmBkH,GACnBpH,aAAcqI,IACX9J,KACH6B,IACJ,CAAC,OAAAqI,OAAA,EAPOjE,IAuBH,SAASmE,mBAAmB/G,EAAmBrD,GAA+B,QAAtC,IAAZqD,EAAAA,EAAe,GAAE,QAAS,IAAPrD,EAAAA,EAAU,CAAA,EAC9D,OAAO,IAAIiG,GAAY5C,EAAKuF,EAAA,CAC1BjH,kBAAmBkH,GACnBpH,aAAcqI,IACX9J,GAEP,CClCA,SAASqK,eAAeC,GACtB,MAAqD,oBAAvCA,EAAQ5M,UAAUiH,gBAClC,CAAC,IAEK4F,GAAW,WAGf,SAAAA,EAAYhJ,GACVM,KAAK2I,EAAQjJ,CACf,CAAC,IAAAa,EAAAmI,EAAA7M,UAAA0E,EAEDqI,QAAA,SAAAA,EAAQzI,GACNH,KAAK2I,EAAMxI,OAASA,EACpB,OAAOH,MACR,OAAA0I,CAAA,CAVc,GAyEjB,IAAaG,GAAc,WAOzB,SAAAA,eAAYC,GAAgC,IAAAC,EAAA/I,KAAAA,KANrCwB,MAAwB,GAO7BxB,KAAKgJ,EAAiBF,EAEtB9I,KAAKyE,IAAM,SACTpH,EACAT,EACAqM,EACApJ,GAAsC,OACnCkJ,EAAKG,EAAS7L,EAAQT,EAASqM,EAAoBpJ,EAAY,MAAM,EAC1EG,KAAK2E,OAAS,SACZtH,EACAT,EACAqM,EACApJ,GAAsC,OACnCkJ,EAAKG,EAAS7L,EAAQT,EAASqM,EAAoBpJ,EAAY,KAAK,EAEzEG,KAAKmJ,MAAQ,SAAAhL,GAAO,OAAKqK,eAAeO,EAAKC,GACzC,IAAID,EAAKC,EAAeD,EAAKvH,MAAOrD,GACpC4K,EAAKC,EAAeD,EAAKvH,MAAOrD,GACtC,CAAC,IAAAiL,EAAAP,eAAAhN,UAAAuN,EAEOF,EAAR,SAAAA,EACE7L,EACAT,EACAqM,EACApJ,EACAK,GAEA,IAAMR,EAAO,CAAErC,OAAAA,GAEf,GAAI6C,EAAUR,EAAKQ,SAAWA,EAC9B,GAAItD,EAAS,CACX8C,EAAK9C,QAAUA,EAEf,GAAIpB,MAAMC,QAAQwN,IAAqD,kBAAvBA,EAC9CvJ,EAAKC,OAASsJ,OACT,GAAkC,qBAAvBA,EAChBvJ,EAAKG,WAAaoJ,EAGpB,GAA0B,qBAAfpJ,EACTH,EAAKG,WAAaA,CAEtB,CAEAG,KAAKwB,MAAM9C,KAAKgB,GAChB,OAAO,IAAIgJ,GAAYhJ,IACxB,OAAAmJ,cAAA,CAtDwB,GAoEpB,SAASQ,cAEdC,EAAsCnL,GACtC,IAAMoL,EAAU,IAAIV,GAAkBN,oBACtC,IAAMiB,EAASF,EAAOC,EAAQ9E,IAAK8E,EAAQ5E,QAE3C,GAAI6E,GAAiC,oBAAhBA,EAAOC,KAC1B,OAAOD,EAAOC,MAAK,WAAA,OAAMF,EAAQJ,MAAMhL,MAGzC,OAAOoL,EAAQJ,MAAMhL,EACvB,KC9JauL,GAA0C,SAA1CA,EAA0CC,GAAK,MAAA,mBAAuBA,EAAMtM,OAAesM,SAAAA,EAAM5G,YAAW,GAAA,EAEzH,IAAM6G,GAAc,SAASC,EAAoBC,GAC/C9J,KAAK8J,QAAUA,CACjB,EAEAF,GAAY/N,UAAYF,OAAOoO,OAAO1N,MAAMR,WAE/BmO,IAAAA,YAAcC,GAAA3F,EAAA0F,eAAAC,GAAAD,eASlBE,kBAAP,SAAAA,EAAyBC,GACvBnK,KAAKoK,EAA8C,kBAAhBD,EAA2B,WAAA,OAAMA,CAAW,EAAGA,GACnFH,eAEMzG,KAAP,SAAAA,EAAkChB,GAChC,OAAO,IAAIvC,KAAQuC,IAGrB,SAAAyH,eAAoBzH,GAAY,IAAAwG,EAC9BA,EAAAkB,EAAAnO,KAAAkE,KAAM,KAAGA,KACT+I,EAAKxG,QAAUA,EAEf,GAAuC,oBAA5BlG,MAAMgO,kBAAkC,CACjDtB,EAAKtM,KAAO,iBACZJ,MAAMgO,kBAAiBC,EAAAvB,GAAOA,EAAKlM,YACrC,CAAC,OAAAkM,CACH,CAAC,IAAAxI,EAAAyJ,eAAAnO,UAAA0E,EAEDgK,WAAA,SAAAA,EAAWT,GACT9J,KAAK8J,QAAUA,EACf,OAAO9J,MACRO,EAGDiK,eAAA,SAAAA,EAAenN,EAAgBT,EAAmBiE,GAChD,IAAM8I,EAAS3J,KAAayK,UAAUpN,EAAQT,EAASiE,GACvD,GAAI8I,EAAO,MAAMA,GAClBpJ,EAGDkK,UAAA,SAAAA,EAAUpN,EAAgBT,EAAmBiE,GAC3C,IAAMnB,EAAOM,KAAKuC,QAAQmC,gBAAgBrH,EAAQT,EAASiE,GAE3D,GAAInB,IAASA,EAAKQ,SAChB,OAGFF,KAAK3C,OAASA,EACd2C,KAAKpD,QAAUA,EACfoD,KAAK+C,YAAcrG,EAAmBsD,KAAKuC,QAAQ5F,kBAAkBC,IACrEoD,KAAKa,MAAQA,EAEb,IAAMV,EAAST,EAAOA,EAAKS,OAAS,GAEpCH,KAAK8J,QAAU9J,KAAK8J,SAAW3J,GAAWH,KAAKnD,YAAoBuN,EAAqBpK,MACxF,OAAOA,MACR,OAAAgK,cAAA,EAvDuDJ,IAA7CI,GAOJI,EAAuBV"}