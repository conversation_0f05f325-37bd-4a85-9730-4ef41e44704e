{"version": 3, "file": "useThrottledCallback.mjs", "sources": ["../../../../../admin/src/hooks/useThrottledCallback.ts"], "sourcesContent": ["import { useMemo } from 'react';\n\nimport throttle from 'lodash/throttle';\n\ntype ThrottleSettings = Parameters<typeof throttle>[2];\n\n/**\n * @internal\n * @description Create a throttled version of a callback\n * @example\n * ```tsx\n * // First create a callback using <PERSON><PERSON>’s `useCallback` hook\n * const myCallback = useCallback(() => {\n *   // this is not throttled\n * }, [])\n *\n * // Then make a throttled version using the `useThrottledCallback` hook\n * const myThrottledCallback = useThrottledCallback(myCallback, 100)\n *\n * // Call the throttled callback\n * <Button onClick={myThrottledCallback} />\n * ```\n */\nconst useThrottledCallback = <T extends (...args: any[]) => any>(\n  callback: T,\n  wait: number,\n  options: ThrottleSettings\n): T => {\n  const throttledCallback = useMemo(\n    () => throttle(callback, wait, options) as unknown as T,\n    [callback, options, wait]\n  );\n\n  return throttledCallback;\n};\n\nexport { useThrottledCallback };\n"], "names": ["useThrottledCallback", "callback", "wait", "options", "throttled<PERSON><PERSON><PERSON>", "useMemo", "throttle"], "mappings": ";;;AAMA;;;;;;;;;;;;;;;;AAgBC,IACKA,MAAAA,oBAAAA,GAAuB,CAC3BC,QAAAA,EACAC,IACAC,EAAAA,OAAAA,GAAAA;AAEA,IAAA,MAAMC,oBAAoBC,OACxB,CAAA,IAAMC,QAASL,CAAAA,QAAAA,EAAUC,MAAMC,OAC/B,CAAA,EAAA;AAACF,QAAAA,QAAAA;AAAUE,QAAAA,OAAAA;AAASD,QAAAA;AAAK,KAAA,CAAA;IAG3B,OAAOE,iBAAAA;AACT;;;;"}