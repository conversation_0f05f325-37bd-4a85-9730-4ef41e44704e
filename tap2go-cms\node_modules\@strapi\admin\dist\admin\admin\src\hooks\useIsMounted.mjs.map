{"version": 3, "file": "useIsMounted.mjs", "sources": ["../../../../../admin/src/hooks/useIsMounted.ts"], "sourcesContent": ["import * as React from 'react';\n\nconst useIsMounted = () => {\n  const isMounted = React.useRef(false);\n\n  React.useLayoutEffect(() => {\n    isMounted.current = true;\n\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  return isMounted;\n};\n\nexport { useIsMounted };\n"], "names": ["useIsMounted", "isMounted", "React", "useRef", "useLayoutEffect", "current"], "mappings": ";;AAEA,MAAMA,YAAe,GAAA,IAAA;IACnB,MAAMC,SAAAA,GAAYC,KAAMC,CAAAA,MAAM,CAAC,KAAA,CAAA;AAE/BD,IAAAA,KAAAA,CAAME,eAAe,CAAC,IAAA;AACpBH,QAAAA,SAAAA,CAAUI,OAAO,GAAG,IAAA;QAEpB,OAAO,IAAA;AACLJ,YAAAA,SAAAA,CAAUI,OAAO,GAAG,KAAA;AACtB,SAAA;AACF,KAAA,EAAG,EAAE,CAAA;IAEL,OAAOJ,SAAAA;AACT;;;;"}