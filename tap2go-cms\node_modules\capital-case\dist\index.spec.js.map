{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,sBAAgC;AAEhC,IAAM,UAAU,GAAuB;IACrC,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,QAAQ,EAAE,SAAS,CAAC;IACrB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;CACrC,CAAC;AAEF,QAAQ,CAAC,cAAc,EAAE;4BACX,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,cAAW,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { capitalCase } from \".\";\n\nconst TEST_CASES: [string, string][] = [\n  [\"\", \"\"],\n  [\"test\", \"Test\"],\n  [\"test string\", \"Test String\"],\n  [\"Test String\", \"Test String\"],\n  [\"TestV2\", \"Test V2\"],\n  [\"version 1.2.10\", \"Version 1 2 10\"],\n  [\"version 1.21.0\", \"Version 1 21 0\"],\n];\n\ndescribe(\"capital case\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(capitalCase(input)).toEqual(result);\n    });\n  }\n});\n"]}