{"name": "os-paths", "version": "7.4.0", "description": "Determine common OS/platform paths (home, temp, ...)", "license": "MIT", "repository": "rivy/js.os-paths", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 4.0"}, "packageManager": "yarn@1.22.19", "files": ["cjs", "dist/cjs", "dist/types", "CHANGELOG.mkd", "LICENSE", "README.md", "package.json"], "type": "commonjs", "main": "./dist/cjs/mod.cjs.js", "module": "./dist/cjs/esm-wrapper/mod.esm.js", "types": "./dist/types/mod.d.ts", "exports": {".": {"deno": "./src/mod.deno.ts", "import": "./dist/cjs/esm-wrapper/mod.esm.js", "require": "./dist/cjs/mod.cjs.js", "types": "./dist/types/mod.d.ts", "default": "./dist/cjs/mod.cjs.js"}, "./package.json": "./package.json", "./cjs": {"require": "./dist/cjs/mod.cjs.js", "types": "./dist/cjs/mod.cjs.d.ts"}}, "keywords": ["common", "cross-platform", "directory", "environment", "linux", "mac", "macos", "node4", "node6", "node-v4", "node-v6", "osx", "path", "paths", "portable", "unix", "windows"], "scripts": {"# build # build/compile package": "", "build": "run-s --silent \"build:*\"", "build:cjs": "exec-if-updated --source package.json --source tsconfig.json --source \"tsconfig/**\" --source \"rollup.*.config.js\" --source \"src/**\" --target build/.targets/build-cjs.succeeded \"run-s -n rebuild:cjs\"", "build:esm": "exec-if-updated --source package.json --source tsconfig.json --source \"tsconfig/**\" --source \"rollup.*.config.js\" --source \"src/**\" --target build/.targets/build-esm.succeeded \"run-s -n rebuild:esm\"", "build:umd": "exec-if-updated --source package.json --source tsconfig.json --source \"tsconfig/**\" --source \"rollup.*.config.js\" --source \"src/**\" --target build/.targets/build-umd.succeeded \"run-s -n rebuild:umd\"", "build:lab": "exec-if-updated --source package.json --source tsconfig.json --source \"tsconfig/**\" --source \"rollup.*.config.js\" --source \"src/**\" --target build/.targets/build-lab.succeeded \"run-s -n rebuild:lab\"", "build:types": "exec-if-updated --source package.json --source tsconfig.json --source \"tsconfig/**\" --source \"rollup.*.config.js\" --source \"src/**\" --target build/.targets/build-types.succeeded \"run-s -n rebuild:types\"", "# clean # remove build artifacts": "", "clean": "shx rm -fr build dist", "# coverage # calculate and display (or send) code coverage [alias: 'cov']": "", "coverage": "run-s --silent +:max-node-8 && shx echo \"[coverage] WARN Code coverage skipped [for NodeJS < v10]\" 1>&2 || run-s \"+:coverage\"", "cov": "run-s coverage", "cov:html": "nyc report --reporter=html --report-dir=.coverage", "#* cov:send # use `--cov-send=...` to pass options to coverage uploader": "", "cov:send": "shx mkdir -p .coverage && nyc report --reporter=text-lcov > \".coverage/@coverage.lcov\" && cross-env-shell codecov --disable=gcov --file=\".coverage/@coverage.lcov\" $npm_config_cov_send", "cov:text": "nyc report", "cov:view": "run-s cov:html && cd .coverage && open-cli index.html", "dist": "run-s update", "# fix # fix package issues (automated/non-interactive)": "", "fix": "run-s fix:*", "# fix:lint # fix ESLint issues": "", "fix:lint": "eslint . --fix", "# fix:style # fix Prettier formatting issues": "", "fix:style": "prettier . --write --list-different", "# help # display help": "", "help": "run-s --silent _:help", "# lint # check for package code 'lint'": "", "lint": "run-s --silent +:max-node-8 && shx echo \"[lint] WAR<PERSON> checks skipped [for NodeJS < v10]\" 1>&2 || run-p --print-name \"lint:*\"", "# lint:audit # check for `npm audit` violations in project code": "", "lint:audit": "run-s --silent -- npm audit --omit dev", "# lint:commits # check for commit flaws (using `commitlint` and `cspell`)": "", "lint:commits": "run-p --silent \"_:lint:commits:new:*\"", "# lint:editorconfig # check for EditorConfig format flaws (using `editorconfig-checker`)": "", "lint:editorconfig": "editorconfig-checker -config .ecrc.JS.json", "# lint:lint # check for code 'lint' (using `eslint`)": "", "lint:lint": "eslint .", "# lint:markdown # check for markdown errors (using `remark`)": "", "lint:markdown": "remark --quiet .", "# lint:spell # check for spelling errors (using `cspell`)": "", "lint:spell": "cspell {eg,examples,src,test}/**/* CHANGELOG{,.md,.mkd} README{,.md,.mkd} --no-summary --config \".vscode/cspell.json\"", "# lint:style # check for format imperfections (using `prettier`)": "", "lint:style": "prettier . --check --loglevel warn", "# prerelease # clean, rebuild, and fully test (useful prior to publish/release)": "", "prerelease": "run-s clean update verify", "# realclean # remove all generated files": "", "realclean": "run-s clean && shx rm -fr .coverage .nyc_output", "# rebuild # clean and (re-)build project": "", "rebuild": "run-s clean build", "rebuild:all": "run-s clean build update", "rebuild:cjs": "shx rm -fr build/cjs && tsc -p tsconfig/tsconfig.cjs.json && shx cp -r src/esm-wrapper build/cjs/src && shx mkdir -p build/.targets && shx touch build/.targets/build-cjs.succeeded", "rebuild:esm": "shx rm -fr build/esm && tsc -p tsconfig/tsconfig.esm.json && shx cp src/esm-wrapper/package.json build/esm/src && shx mkdir -p build/.targets && shx touch build/.targets/build-esm.succeeded", "rebuild:umd": "shx rm -fr build/umd && tsc -p tsconfig/tsconfig.umd.json && shx mkdir -p build/.targets && shx touch build/.targets/build-umd.succeeded", "rebuild:lab": "shx rm -fr build/lab && tsc -p tsconfig/tsconfig.lab.json && shx cp -r src/esm-wrapper build/lab/src && shx mkdir -p build/.targets && shx touch build/.targets/build-lab.succeeded", "rebuild:types": "shx rm -fr build/types && tsc -p tsconfig/tsconfig.types.json && shx mkdir -p build/.targets && shx touch build/.targets/build-types.succeeded", "# refresh # clean and rebuild/regenerate all project artifacts": "", "refresh": "run-s rebuild:all", "# refresh:dist # clean, rebuild, and regenerate project distribution": "", "refresh:dist": "run-s rebuild update:dist", "# retest # clean and (re-)test project": "", "retest": "run-s clean test", "# reset:hard # remove *all* generated files and reinstall dependencies": "", "reset:hard": "git clean -dfx && git reset --hard && npm install", "# show:deps # show package dependencies": "", "show:deps": "run-s --silent _:show:deps:prod _:show:deps:dev || shx true", "# test # test package": "", "test": "run-s --silent lint update:dist && run-p test:*", "# test:code # test package code (use `--test-code=...` to pass options to testing harness)": "", "test:code": "run-s --silent +:max-node-8 && cross-env-shell ava $npm_config_test_code || ( run-s --silent +:min-node-10 && cross-env-shell nyc --silent ava $npm_config_test_code )", "# test:types # test for type declaration errors (using `tsd`)": "", "test:types": "run-s --silent +:max-node-8 && shx echo \"[test:types] WARN Type testing skipped [for NodeJS < v10]\" 1>&2 || tsd", "# update # update/prepare for distribution [alias: 'dist']": "", "update": "run-s update:changelog update:dist", "# update:changelog # update CHANGELOG (using `git changelog ...`)": "", "update:changelog": "run-s --silent _:update:changelog && git diff --quiet --exit-code CHANGELOG.mkd || shx echo \"[update] info CHANGELOG updated\"", "# update:dist # update distribution content": "", "update:dist": "run-s --silent build && exec-if-updated --source \"build/**\" --target \"dist/**\" --target build/.targets/update-dist.succeeded \"run-s --silent _:update:dist:rebuild\"", "# verify # fully (and verbosely) test package": "", "verify": "cross-env npm_config_test_dist=true npm_config_test=--verbose run-s test", "## +:... == sub-scripts (may run 'visibly', but not user-facing)": "", "+:coverage": "run-s build test:code && ( is-ci && run-s cov:send ) || ( run-s --silent _:is-not-ci && run-s cov:view )", "+:max-node-8": "is-node-not-modern 10", "+:min-node-10": "is-node-modern 10", "## _:... == sub-scripts ('hidden'; generally should be run 'silently' using `run-s/run-p --silent ...`": "", "_:debug:env": "node -e \"console.log({env: process.env})\"", "_:exists:git-changelog": "node -e \"if (!require('command-exists').sync('git-changelog')){process.exit(1);};\" || ( shx echo \"WARN `git-changelog` missing (try `go get -u github.com/rivy-go/git-changelog/cmd/git-changelog`)\" & exit 1 )", "* _:help # print usage/TARGETs by matching lines containing leading double-quoted text like `# TARGET_NAME # HELP_TEXT`": "", "_:help": "< package.json node -e \"s = {p:'',e:'npm'}; if (new String(process.env.npm_execpath).match(/yarn.js$/)) { s = {p:'\\n',e:'yarn'}; }; console.log('%sUsage: \\`\\x1b[2m%s run TARGET\\x1b[m\\` or \\`\\x1b[2mnpx run-s TARGET [TARGET..]\\x1b[m\\`\\n\\nTARGETs:\\n', s.p, s.e); re = /^.*?\\x22(?:#\\s*)(\\w[^#\\x22]*)\\s+#+\\s+([^\\x22]+?)(\\s+#+)?\\x22.*$/; require('readline').createInterface({ input: process.stdin, output: process.stdout, terminal: false }).on('line', function(line){ if (match = re.exec(line)) { console.log('\\x1b[0;32m%s\\x1b[m %s', match[1].padEnd(19), match[2]); } }).on('close', () => { /^win/i.test(process.platform) || console.log(); });\"", "_:is-not-ci": "is-ci && exit 1 || exit 0", "_:lint:commits:all:spell": "node -e \"result=require('child_process').spawnSync('git log --color=never | cspell stdin --no-summary --config \".vscode/cspell.json\"',{shell:true,encoding:'utf-8'}); if (result.status != 0) {console.error('[cspell] ERR! Unknown words in commit(s)\\n'+result.stdout+'\\n'+result.stderr); process.exit(1);} else {console.log(result.stdout);};\"", "* _:lint:commits:new:... * note: review from 'origin/last' or tag just prior to version-sorted latest, with fallback to first commit": "", "_:lint:commits:new:commitlint": "node -e \"result=require('child_process').spawnSync('( git tag --list [#v]* --contains origin/last --sort=v:refname || shx true ) && ( git describe --tags --abbrev=0 HEAD~1 || shx true ) && ( git rev-list --max-parents=0 HEAD --abbrev-commit --abbrev=16 || shx true )',{shell:true,encoding:'utf-8'}); o=result.stdout.split(/\\r?\\n/).filter((s)=>!!s); vs=o; v=vs[0]; result=require('child_process').spawnSync('commitlint --config .commitlint.config.js --from '+v,{shell:true,encoding:'utf-8'}); if (result.status != 0) {console.error('[commitlint] ERR! Flawed commit(s) found (within \\'%s..HEAD\\')\\n'+result.stdout+'\\n'+result.stderr, v); process.exit(1);} else { (result.stdout.length > 0) && console.log(result.stdout);};\" || shx true", "_:lint:commits:new:spell": "node -e \"result=require('child_process').spawnSync('( git tag --list [#v]* --contains origin/last --sort=v:refname || shx true ) && ( git describe --tags --abbrev=0 HEAD~1 || shx true ) && ( git rev-list --max-parents=0 HEAD --abbrev-commit --abbrev=16 || shx true )',{shell:true,encoding:'utf-8'}); o=result.stdout.split(/\\r?\\n/).filter((s)=>!!s); vs=o; v=vs[0]; result=require('child_process').spawnSync('git log '+v+'.. --color=never | cspell stdin --no-summary --config \".vscode/cspell.json\"',{shell:true,encoding:'utf-8'}); if (result.status != 0) {console.error('[cspell] ERR! Unknown words in commit(s) (within \\'%s..HEAD\\')\\n'+result.stdout+'\\n'+result.stderr, v); process.exit(1);} else {(result.stdout.length > 0) && console.log(result.stdout);};\" || shx true", "_:show:deps:dev": "npm --silent ls --only development || shx true", "_:show:deps:prod": "npm --silent ls --only production || shx true", "_:vcs-clean": "git diff --quiet", "_:vcs-clean-err": "run-s --silent _:vcs-clean || ( shx echo \"[vcs] ERR! Uncommitted changes\" 1>&2 & exit 1 )", "_:vcs-strictly-clean": "git status --porcelain | node -e \"process.stdin.on('data',function(_){process.exit(1);});\"", "_:vcs-strictly-clean-err": "run-s --silent _:vcs-strictly-clean || ( shx echo \"[vcs] ERR! Uncommitted changes and/or untracked files\" 1>&2 & exit 1 )", "_:update:changelog": "run-s --silent _:exists:git-changelog && git changelog > CHANGELOG.mkd || shx echo \"[update] WARN CHANGELOG not updated\" 1>&2", "_:update:dist.build": "shx rm -fr dist/cjs dist/esm && shx mkdir -p dist/cjs dist/esm && shx cp -r build/cjs/src/* dist/cjs && shx cp -r build/esm/src/* dist/esm", "_:update:dist.normalizeEOL": "eolConverter lf dist/**/*.{cjs,js,mjs,ts,json}", "_:update:dist.pack": "node -e \"delete process.env.npm_config_dry_run; name=require('./package.json').name; name=name.replace(/^@/,'').replace('/','-'); result=require('child_process').spawnSync('npm pack && shx mkdir -p dist && shx mv '+name+'-*.tgz dist/'+name+'.tgz',{shell:true,encoding:'utf-8'}); if (result.status != 0) {console.error('[update] ERR! Unable to package (into *.tgz) for distribution\\n'+result.stdout+'\\n'+result.stderr); process.exit(1);} else {console.log(result.stdout);};\"", "_:update:dist.types": "shx mkdir -p dist && shx rm -fr dist/types && rollup --config .rollup.config.types.js && replace-in-file \"export { _default as default }\" \"export = _default\" dist/types/mod.cjs.d.ts --quiet && shx mkdir -p dist/cjs && shx cp dist/types/*.cjs.d.ts dist/cjs", "_:update:dist:rebuild": "shx rm -fr dist && run-s --silent _:update:dist.build _:update:dist.types _:update:dist.normalizeEOL _:update:dist.pack && shx mkdir -p dist/.targets && shx touch build/.targets/update-dist.succeeded", "_:version:spell:changelog_update": "run-s --silent _:exists:git-changelog && git changelog -u | cspell stdin --config \".vscode/cspell.json\" || shx echo \"[lint] WARN CHANGELOG update `cspell` exception\" 1>&2", "_:version:update:changelog": "run-s --silent _:exists:git-changelog && node -e \"v=require('./package.json').version; result=require('child_process').spawnSync('git changelog --next-tag-now --next-tag v'+v,{shell:true,encoding:'utf-8'}); if (result.status != 0) {console.error('ERR! '+result.stderr); process.exit(1);} else {m='fs';require(m).writeFileSync('CHANGELOG.mkd',result.stdout);};\" || shx echo \"[version] WARN CHANGELOG not updated\" 1>&2", "## npm lifecycle scripts ##": "", "prepublishOnly": "run-s clean update && cross-env npm_config_test_dist=true npm run test && run-s --silent update _:vcs-strictly-clean-err", "## npm-version scripts ##": "", "preversion": "run-s --silent _:version:spell:changelog_update && cross-env npm_config_test_dist=true npm run test", "version": "run-s --silent _:version:update:changelog && run-s lint:spell && run-s --silent update:dist && git add CHANGELOG.mkd dist"}, "dependencies": {}, "devDependencies:#": "* for testing, Node-v6 requires ava < v2 and nyc < v15", "devDependencies": {"@ava/typescript": "^1.1.1", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/node": "^14.14.20", "@typescript-eslint/eslint-plugin": "^4", "@typescript-eslint/parser": "^4", "ava": "^3.15.0", "codecov": "^3.5.0", "command-exists": "^1.2.9", "cross-env": "^7.0.3", "cross-spawn": "^7.0.3", "cspell": "^4.2.7", "editorconfig-checker": "^3.3.0", "eol-converter-cli": "^1.0.8", "eslint": "^7", "eslint-config-prettier": "^7", "eslint-plugin-eslint-comments": "^3", "eslint-plugin-functional": "^3", "eslint-plugin-import": "^2", "eslint-plugin-security": "^1", "eslint-plugin-security-node": "^1", "exec-if-updated": "https://cdn.jsdelivr.net/gh/rivy/js-cli.exec-if-updated@2.2.0/dist/pkg/exec-if-updated.tgz", "is-ci": "^2.0.0", "is-node-modern": "^1.0.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "open-cli": ">=6.0 <7.0", "prettier": "^2.1.1", "remark-cli": "=9.0.0", "remark-footnotes": "^3.0.0", "remark-preset-lint-consistent": "^4.0.0", "remark-preset-lint-markdown-style-guide": "^4.0.0", "remark-preset-lint-recommended": "^5.0.0", "remark-retext": "^4.0.0", "replace-in-file": "=6.3.0", "retext-english": "^3.0.4", "retext-passive": "^3.0.0", "retext-repeated-words": "^3.0.0", "retext-sentence-spacing": "^4.0.0", "retext-syntax-urls": "^2.0.0", "rollup": "^2.36.1", "rollup-plugin-dts": "^2.0.1", "rollup-plugin-typescript2": "^0.29.0", "shx": "^0.3.3", "ts-node": "^9.0.0", "tsd": "^0.14.0", "typedoc": "^0.20.27", "typescript": "~4.2.0", "unified": "^9.2.0"}, "optionalDependencies:#": "* 'fsevents' included to avoid `npm ci` errors with early npm versions; ref: <https://github.com/bahmutov/npm-install/issues/103>", "optionalDependencies": {"fsevents": "*"}, "ava": {"files": ["!**/*.test-d.ts"], "timeout": "60s", "typescript": {"rewritePaths": {"src/": "build/lab/src/"}}}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "exclude": ["build/cjs/**", "build/esm/**", "build/umd/**", "dist/**", "eg/**", "test/**", "**/*.test.*", "**/*.spec.*"], "reporter": ["html", "text"], "lines": "100", "branches": "96", "statements": "100"}, "tsd": {"directory": "test"}}