{"version": 3, "file": "transfer.mjs", "sources": ["../../../../../server/src/routes/transfer.ts"], "sourcesContent": ["import dataTransferAuthStrategy from '../strategies/data-transfer';\n\nexport default [\n  // Transfer Push\n  {\n    method: 'GET',\n    path: '/transfer/runner/push',\n    handler: 'transfer.runner-push',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      auth: { strategies: [dataTransferAuthStrategy], scope: ['push'] },\n    },\n  },\n  // Transfer Pull\n  {\n    method: 'GET',\n    path: '/transfer/runner/pull',\n    handler: 'transfer.runner-pull',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      auth: { strategies: [dataTransferAuthStrategy], scope: ['pull'] },\n    },\n  },\n  // Transfer Tokens\n  {\n    method: 'POST',\n    path: '/transfer/tokens',\n    handler: 'transfer.token-create',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::transfer.tokens.create'] } },\n      ],\n    },\n  },\n  {\n    method: 'GET',\n    path: '/transfer/tokens',\n    handler: 'transfer.token-list',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::transfer.tokens.read'] } },\n      ],\n    },\n  },\n  {\n    method: 'DELETE',\n    path: '/transfer/tokens/:id',\n    handler: 'transfer.token-revoke',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::transfer.tokens.delete'] } },\n      ],\n    },\n  },\n  {\n    method: 'GET',\n    path: '/transfer/tokens/:id',\n    handler: 'transfer.token-getById',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::transfer.tokens.read'] } },\n      ],\n    },\n  },\n  {\n    method: 'PUT',\n    path: '/transfer/tokens/:id',\n    handler: 'transfer.token-update',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::transfer.tokens.update'] } },\n      ],\n    },\n  },\n  {\n    method: 'POST',\n    path: '/transfer/tokens/:id/regenerate',\n    handler: 'transfer.token-regenerate',\n    config: {\n      middlewares: ['admin::data-transfer'],\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        {\n          name: 'admin::hasPermissions',\n          config: { actions: ['admin::transfer.tokens.regenerate'] },\n        },\n      ],\n    },\n  },\n];\n"], "names": ["method", "path", "handler", "config", "middlewares", "auth", "strategies", "dataTransferAuthStrategy", "scope", "policies", "name", "actions"], "mappings": ";;AAEA,eAAe;;AAEb,IAAA;QACEA,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,uBAAA;QACNC,OAAS,EAAA,sBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCC,IAAM,EAAA;gBAAEC,UAAY,EAAA;AAACC,oBAAAA;AAAyB,iBAAA;gBAAEC,KAAO,EAAA;AAAC,oBAAA;AAAO;AAAC;AAClE;AACF,KAAA;;AAEA,IAAA;QACER,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,uBAAA;QACNC,OAAS,EAAA,sBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCC,IAAM,EAAA;gBAAEC,UAAY,EAAA;AAACC,oBAAAA;AAAyB,iBAAA;gBAAEC,KAAO,EAAA;AAAC,oBAAA;AAAO;AAAC;AAClE;AACF,KAAA;;AAEA,IAAA;QACER,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,kBAAA;QACNC,OAAS,EAAA,uBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCK,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBP,MAAQ,EAAA;wBAAEQ,OAAS,EAAA;AAAC,4BAAA;AAAgC;AAAC;AAAE;AACzF;AACH;AACF,KAAA;AACA,IAAA;QACEX,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,kBAAA;QACNC,OAAS,EAAA,qBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCK,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBP,MAAQ,EAAA;wBAAEQ,OAAS,EAAA;AAAC,4BAAA;AAA8B;AAAC;AAAE;AACvF;AACH;AACF,KAAA;AACA,IAAA;QACEX,MAAQ,EAAA,QAAA;QACRC,IAAM,EAAA,sBAAA;QACNC,OAAS,EAAA,uBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCK,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBP,MAAQ,EAAA;wBAAEQ,OAAS,EAAA;AAAC,4BAAA;AAAgC;AAAC;AAAE;AACzF;AACH;AACF,KAAA;AACA,IAAA;QACEX,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,sBAAA;QACNC,OAAS,EAAA,wBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCK,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBP,MAAQ,EAAA;wBAAEQ,OAAS,EAAA;AAAC,4BAAA;AAA8B;AAAC;AAAE;AACvF;AACH;AACF,KAAA;AACA,IAAA;QACEX,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,sBAAA;QACNC,OAAS,EAAA,uBAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCK,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBP,MAAQ,EAAA;wBAAEQ,OAAS,EAAA;AAAC,4BAAA;AAAgC;AAAC;AAAE;AACzF;AACH;AACF,KAAA;AACA,IAAA;QACEX,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,iCAAA;QACNC,OAAS,EAAA,2BAAA;QACTC,MAAQ,EAAA;YACNC,WAAa,EAAA;AAAC,gBAAA;AAAuB,aAAA;YACrCK,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBACEC,IAAM,EAAA,uBAAA;oBACNP,MAAQ,EAAA;wBAAEQ,OAAS,EAAA;AAAC,4BAAA;AAAoC;AAAC;AAC3D;AACD;AACH;AACF;CACD;;;;"}