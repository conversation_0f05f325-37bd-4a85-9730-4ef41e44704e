{"version": 3, "file": "User.js", "sources": ["../../../../../server/src/content-types/User.ts"], "sourcesContent": ["export default {\n  collectionName: 'admin_users',\n  info: {\n    name: 'User',\n    description: '',\n    singularName: 'user',\n    pluralName: 'users',\n    displayName: 'User',\n  },\n  pluginOptions: {\n    'content-manager': {\n      visible: false,\n    },\n    'content-type-builder': {\n      visible: false,\n    },\n  },\n  attributes: {\n    firstname: {\n      type: 'string',\n      unique: false,\n      minLength: 1,\n      configurable: false,\n      required: false,\n    },\n    lastname: {\n      type: 'string',\n      unique: false,\n      minLength: 1,\n      configurable: false,\n      required: false,\n    },\n    username: {\n      type: 'string',\n      unique: false,\n      configurable: false,\n      required: false,\n    },\n    email: {\n      type: 'email',\n      minLength: 6,\n      configurable: false,\n      required: true,\n      unique: true,\n      private: true,\n    },\n    password: {\n      type: 'password',\n      minLength: 6,\n      configurable: false,\n      required: false,\n      private: true,\n      searchable: false,\n    },\n    resetPasswordToken: {\n      type: 'string',\n      configurable: false,\n      private: true,\n      searchable: false,\n    },\n    registrationToken: {\n      type: 'string',\n      configurable: false,\n      private: true,\n      searchable: false,\n    },\n    isActive: {\n      type: 'boolean',\n      default: false,\n      configurable: false,\n      private: true,\n    },\n    roles: {\n      configurable: false,\n      private: true,\n      type: 'relation',\n      relation: 'manyToMany',\n      inversedBy: 'users',\n      target: 'admin::role',\n      // FIXME: Allow setting this\n      collectionName: 'strapi_users_roles',\n    },\n    blocked: {\n      type: 'boolean',\n      default: false,\n      configurable: false,\n      private: true,\n    },\n    preferedLanguage: {\n      type: 'string',\n      configurable: false,\n      required: false,\n      searchable: false,\n    },\n  },\n  config: {\n    attributes: {\n      resetPasswordToken: {\n        hidden: true,\n      },\n      registrationToken: {\n        hidden: true,\n      },\n    },\n  },\n};\n"], "names": ["collectionName", "info", "name", "description", "singularName", "pluralName", "displayName", "pluginOptions", "visible", "attributes", "firstname", "type", "unique", "<PERSON><PERSON><PERSON><PERSON>", "configurable", "required", "lastname", "username", "email", "private", "password", "searchable", "resetPasswordToken", "registrationToken", "isActive", "default", "roles", "relation", "inversedBy", "target", "blocked", "preferedLanguage", "config", "hidden"], "mappings": ";;AAAA,WAAe;IACbA,cAAgB,EAAA,aAAA;IAChBC,IAAM,EAAA;QACJC,IAAM,EAAA,MAAA;QACNC,WAAa,EAAA,EAAA;QACbC,YAAc,EAAA,MAAA;QACdC,UAAY,EAAA,OAAA;QACZC,WAAa,EAAA;AACf,KAAA;IACAC,aAAe,EAAA;QACb,iBAAmB,EAAA;YACjBC,OAAS,EAAA;AACX,SAAA;QACA,sBAAwB,EAAA;YACtBA,OAAS,EAAA;AACX;AACF,KAAA;IACAC,UAAY,EAAA;QACVC,SAAW,EAAA;YACTC,IAAM,EAAA,QAAA;YACNC,MAAQ,EAAA,KAAA;YACRC,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAC,QAAU,EAAA;YACRL,IAAM,EAAA,QAAA;YACNC,MAAQ,EAAA,KAAA;YACRC,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAE,QAAU,EAAA;YACRN,IAAM,EAAA,QAAA;YACNC,MAAQ,EAAA,KAAA;YACRE,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAG,KAAO,EAAA;YACLP,IAAM,EAAA,OAAA;YACNE,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA,IAAA;YACVH,MAAQ,EAAA,IAAA;YACRO,OAAS,EAAA;AACX,SAAA;QACAC,QAAU,EAAA;YACRT,IAAM,EAAA,UAAA;YACNE,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA,KAAA;YACVI,OAAS,EAAA,IAAA;YACTE,UAAY,EAAA;AACd,SAAA;QACAC,kBAAoB,EAAA;YAClBX,IAAM,EAAA,QAAA;YACNG,YAAc,EAAA,KAAA;YACdK,OAAS,EAAA,IAAA;YACTE,UAAY,EAAA;AACd,SAAA;QACAE,iBAAmB,EAAA;YACjBZ,IAAM,EAAA,QAAA;YACNG,YAAc,EAAA,KAAA;YACdK,OAAS,EAAA,IAAA;YACTE,UAAY,EAAA;AACd,SAAA;QACAG,QAAU,EAAA;YACRb,IAAM,EAAA,SAAA;YACNc,OAAS,EAAA,KAAA;YACTX,YAAc,EAAA,KAAA;YACdK,OAAS,EAAA;AACX,SAAA;QACAO,KAAO,EAAA;YACLZ,YAAc,EAAA,KAAA;YACdK,OAAS,EAAA,IAAA;YACTR,IAAM,EAAA,UAAA;YACNgB,QAAU,EAAA,YAAA;YACVC,UAAY,EAAA,OAAA;YACZC,MAAQ,EAAA,aAAA;;YAER7B,cAAgB,EAAA;AAClB,SAAA;QACA8B,OAAS,EAAA;YACPnB,IAAM,EAAA,SAAA;YACNc,OAAS,EAAA,KAAA;YACTX,YAAc,EAAA,KAAA;YACdK,OAAS,EAAA;AACX,SAAA;QACAY,gBAAkB,EAAA;YAChBpB,IAAM,EAAA,QAAA;YACNG,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA,KAAA;YACVM,UAAY,EAAA;AACd;AACF,KAAA;IACAW,MAAQ,EAAA;QACNvB,UAAY,EAAA;YACVa,kBAAoB,EAAA;gBAClBW,MAAQ,EAAA;AACV,aAAA;YACAV,iBAAmB,EAAA;gBACjBU,MAAQ,EAAA;AACV;AACF;AACF;AACF,CAAE;;;;"}