{"version": 3, "file": "useQueryParams.js", "sources": ["../../../../../admin/src/hooks/useQueryParams.ts"], "sourcesContent": ["import { useCallback, useMemo } from 'react';\n\nimport { parse, stringify } from 'qs';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst useSearch = () => {\n  const { search } = useLocation();\n\n  return useMemo(() => search, [search]);\n};\n\nconst useQueryParams = <TQuery extends object>(initialParams?: TQuery) => {\n  const search = useSearch();\n  const navigate = useNavigate();\n\n  const query = useMemo(() => {\n    // TODO: investigate why sometimes we're getting the search with a leading `?` and sometimes not.\n    const searchQuery = search.startsWith('?') ? search.slice(1) : search;\n    if (!search && initialParams) {\n      return initialParams;\n    }\n\n    return { ...initialParams, ...parse(searchQuery) } as TQuery;\n  }, [search, initialParams]);\n\n  const setQuery = useCallback(\n    (nextParams: TQuery, method: 'push' | 'remove' = 'push', replace = false) => {\n      let nextQuery = { ...query };\n\n      if (method === 'remove') {\n        Object.keys(nextParams).forEach((key) => {\n          if (Object.prototype.hasOwnProperty.call(nextQuery, key)) {\n            // @ts-expect-error – this is fine, if you want to fix it, please do.\n            delete nextQuery[key];\n          }\n        });\n      } else {\n        nextQuery = { ...query, ...nextParams };\n      }\n\n      navigate({ search: stringify(nextQuery, { encode: false }) }, { replace });\n    },\n    [navigate, query]\n  );\n\n  return [{ query, rawQuery: search }, setQuery] as const;\n};\n\nexport { useQueryParams };\n"], "names": ["useSearch", "search", "useLocation", "useMemo", "useQueryParams", "initialParams", "navigate", "useNavigate", "query", "searchQuery", "startsWith", "slice", "parse", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "nextParams", "method", "replace", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "stringify", "encode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAKA,MAAMA,SAAY,GAAA,IAAA;IAChB,MAAM,EAAEC,MAAM,EAAE,GAAGC,0BAAAA,EAAAA;IAEnB,OAAOC,aAAAA,CAAQ,IAAMF,MAAQ,EAAA;AAACA,QAAAA;AAAO,KAAA,CAAA;AACvC,CAAA;AAEA,MAAMG,iBAAiB,CAAwBC,aAAAA,GAAAA;AAC7C,IAAA,MAAMJ,MAASD,GAAAA,SAAAA,EAAAA;AACf,IAAA,MAAMM,QAAWC,GAAAA,0BAAAA,EAAAA;AAEjB,IAAA,MAAMC,QAAQL,aAAQ,CAAA,IAAA;;QAEpB,MAAMM,WAAAA,GAAcR,OAAOS,UAAU,CAAC,OAAOT,MAAOU,CAAAA,KAAK,CAAC,CAAKV,CAAAA,GAAAA,MAAAA;QAC/D,IAAI,CAACA,UAAUI,aAAe,EAAA;YAC5B,OAAOA,aAAAA;AACT;QAEA,OAAO;AAAE,YAAA,GAAGA,aAAa;AAAE,YAAA,GAAGO,SAAMH,WAAY;AAAC,SAAA;KAChD,EAAA;AAACR,QAAAA,MAAAA;AAAQI,QAAAA;AAAc,KAAA,CAAA;IAE1B,MAAMQ,QAAAA,GAAWC,kBACf,CAACC,UAAAA,EAAoBC,SAA4B,MAAM,EAAEC,UAAU,KAAK,GAAA;AACtE,QAAA,IAAIC,SAAY,GAAA;AAAE,YAAA,GAAGV;AAAM,SAAA;AAE3B,QAAA,IAAIQ,WAAW,QAAU,EAAA;AACvBG,YAAAA,MAAAA,CAAOC,IAAI,CAACL,UAAYM,CAAAA,CAAAA,OAAO,CAAC,CAACC,GAAAA,GAAAA;gBAC/B,IAAIH,MAAAA,CAAOI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,WAAWI,GAAM,CAAA,EAAA;;oBAExD,OAAOJ,SAAS,CAACI,GAAI,CAAA;AACvB;AACF,aAAA,CAAA;SACK,MAAA;YACLJ,SAAY,GAAA;AAAE,gBAAA,GAAGV,KAAK;AAAE,gBAAA,GAAGO;AAAW,aAAA;AACxC;QAEAT,QAAS,CAAA;AAAEL,YAAAA,MAAAA,EAAQyB,aAAUR,SAAW,EAAA;gBAAES,MAAQ,EAAA;AAAM,aAAA;SAAM,EAAA;AAAEV,YAAAA;AAAQ,SAAA,CAAA;KAE1E,EAAA;AAACX,QAAAA,QAAAA;AAAUE,QAAAA;AAAM,KAAA,CAAA;IAGnB,OAAO;AAAC,QAAA;AAAEA,YAAAA,KAAAA;YAAOoB,QAAU3B,EAAAA;AAAO,SAAA;AAAGY,QAAAA;AAAS,KAAA;AAChD;;;;"}