{"version": 3, "file": "useOnce.js", "sources": ["../../../../../admin/src/hooks/useOnce.ts"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\nimport * as React from 'react';\n\nexport const useOnce = (effect: React.EffectCallback) => React.useEffect(effect, emptyDeps);\n\nconst emptyDeps: React.DependencyList = [];\n"], "names": ["useOnce", "effect", "React", "useEffect", "emptyDeps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAMA,UAAU,CAACC,MAAAA,GAAiCC,iBAAMC,SAAS,CAACF,QAAQG,SAAW;AAE5F,MAAMA,YAAkC,EAAE;;;;"}