(function(r,n){"object"===typeof exports&&"undefined"!==typeof module?n(exports,require("@ucast/mongo2js")):"function"===typeof define&&define.amd?define(["exports","@ucast/mongo2js"],n):(r="undefined"!==typeof globalThis?globalThis:r||self,n((r.casl=r.casl||{},r.casl.extra={}),r.ucast.mongo2js))})(this,(function(r,n){"use strict";function e(r){return Array.isArray(r)?r:[r]}function t(r,n,e){var t=r;var u=n;if(-1!==n.indexOf(".")){var o=n.split(".");u=o.pop();t=o.reduce((function(r,n){r[n]=r[n]||{};return r[n]}),r)}t[u]=e}Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function u(r,n,e,t){var u={};var o=r.rulesFor(n,e);for(var i=0;i<o.length;i++){var f=o[i];var a=f.inverted?"$and":"$or";if(!f.conditions)if(f.inverted)break;else{delete u[a];return u}else{u[a]=u[a]||[];u[a].push(t(f))}}return u.$or?u:null}function o(r){if(!r.ast)throw new Error('Ability rule "'+JSON.stringify(r)+'" does not have "ast" property. So, cannot be used to generate AST');return r.inverted?new n.CompoundCondition("not",[r.ast]):r.ast}function i(r,e,t){var i=u(r,e,t,o);if(null===i)return null;if(!i.$and)return i.$or?n.buildOr(i.$or):n.buildAnd([]);if(i.$or)i.$and.push(n.buildOr(i.$or));return n.buildAnd(i.$and)}function f(r,n,e){return r.rulesFor(n,e).reduce((function(r,n){if(n.inverted||!n.conditions)return r;return Object.keys(n.conditions).reduce((function(r,e){var u=n.conditions[e];if(!u||u.constructor!==Object)t(r,e,u);return r}),r)}),{})}function a(r,n,e,t){var u=r.detectSubjectType(e);var o=r.possibleRulesFor(n,u);var i=new Set;var f=i.delete.bind(i);var a=i.add.bind(i);var c=o.length;while(c--){var v=o[c];if(v.matchesConditions(e)){var l=v.inverted?f:a;t.fieldsFrom(v).forEach(l)}}return Array.from(i)}var c=function r(n){return Array.isArray(n)?n.join(","):n};function v(r,n){return r.map((function(r){var t=[c(r.action||r.actions),"function"===typeof n?e(r.subject).map(n).join(","):c(r.subject),r.conditions||0,r.inverted?1:0,r.fields?c(r.fields):0,r.reason||""];while(t.length>0&&!t[t.length-1])t.pop();return t}))}function l(r,n){return r.map((function(r){var e=r[0],t=r[1],u=r[2],o=r[3],i=r[4],f=r[5];var a=t.split(",");var c={inverted:!!o,action:e.split(","),subject:"function"===typeof n?a.map(n):a};if(u)c.conditions=u;if(i)c.fields=i.split(",");if(f)c.reason=f;return c}))}r.packRules=v;r.permittedFieldsOf=a;r.rulesToAST=i;r.rulesToFields=f;r.rulesToQuery=u;r.unpackRules=l;Object.defineProperty(r,"__esModule",{value:true})}));
//# sourceMappingURL=extra.js.map
