{"version": 3, "file": "report.js", "sources": ["../../../../src/cli/commands/report.ts"], "sourcesContent": ["import { createCommand } from 'commander';\nimport { E<PERSON> } from 'os';\nimport { createStrapi, compileStrapi } from '@strapi/core';\nimport type { StrapiCommand } from '../types';\nimport { runAction } from '../utils/helpers';\n\ninterface CmdOptions {\n  uuid: boolean;\n  dependencies: boolean;\n  all: boolean;\n}\n\nconst action = async ({ uuid, dependencies, all }: CmdOptions) => {\n  const config = {\n    reportUUID: Boolean(all || uuid),\n    reportDependencies: Boolean(all || dependencies),\n  };\n\n  const appContext = await compileStrapi();\n  const app = await createStrapi(appContext).register();\n\n  let debugInfo = `Launched In: ${Date.now() - app.config.launchedAt} ms\nEnvironment: ${app.config.environment}\nOS: ${process.platform}-${process.arch}\nStrapi Version: ${app.config.info.strapi}\nNode/Yarn Version: ${process.env.npm_config_user_agent}\nEdition: ${app.EE ? 'Enterprise' : 'Community'}\nDatabase: ${app?.config?.database?.connection?.client ?? 'unknown'}`;\n\n  if (config.reportUUID) {\n    debugInfo += `${EOL}UUID: ${app.config.uuid}`;\n  }\n\n  if (config.reportDependencies) {\n    debugInfo += `${EOL}Dependencies: ${JSON.stringify(app.config.info.dependencies, null, 2)}\nDev Dependencies: ${JSON.stringify(app.config.info.devDependencies, null, 2)}`;\n  }\n\n  console.log(debugInfo);\n\n  await app.destroy();\n};\n\n/**\n * `$ strapi report`\n */\nconst command: StrapiCommand = () => {\n  return createCommand('report')\n    .description('Get system stats for debugging and submitting issues')\n    .option('-u, --uuid', 'Include Project UUID')\n    .option('-d, --dependencies', 'Include Project Dependencies')\n    .option('--all', 'Include All Information')\n    .action(runAction('report', action));\n};\n\nexport { command };\n"], "names": ["action", "uuid", "dependencies", "all", "config", "reportUUID", "Boolean", "reportDependencies", "appContext", "compileStrapi", "app", "createStrapi", "register", "debugInfo", "Date", "now", "launchedAt", "environment", "process", "platform", "arch", "info", "strapi", "env", "npm_config_user_agent", "EE", "database", "connection", "client", "EOL", "JSON", "stringify", "devDependencies", "console", "log", "destroy", "command", "createCommand", "description", "option", "runAction"], "mappings": ";;;;;;;AAYA,MAAMA,MAAAA,GAAS,OAAO,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAc,GAAA;AAC3D,IAAA,MAAMC,MAAS,GAAA;AACbC,QAAAA,UAAAA,EAAYC,QAAQH,GAAOF,IAAAA,IAAAA,CAAAA;AAC3BM,QAAAA,kBAAAA,EAAoBD,QAAQH,GAAOD,IAAAA,YAAAA;AACrC,KAAA;AAEA,IAAA,MAAMM,aAAa,MAAMC,kBAAAA,EAAAA;AACzB,IAAA,MAAMC,GAAM,GAAA,MAAMC,iBAAaH,CAAAA,UAAAA,CAAAA,CAAYI,QAAQ,EAAA;IAEnD,IAAIC,SAAAA,GAAY,CAAC,aAAa,EAAEC,IAAAA,CAAKC,GAAG,EAAA,GAAKL,GAAIN,CAAAA,MAAM,CAACY,UAAU,CAAC;AACxD,aAAA,EAAEN,GAAIN,CAAAA,MAAM,CAACa,WAAW;AACjC,IAAA,EAAEC,QAAQC,QAAQ,CAAC,CAAC,EAAED,OAAAA,CAAQE,IAAI;AACtB,gBAAA,EAAEV,IAAIN,MAAM,CAACiB,IAAI,CAACC,MAAM;AACrB,mBAAA,EAAEJ,OAAQK,CAAAA,GAAG,CAACC,qBAAqB;AAC7C,SAAA,EAAEd,GAAIe,CAAAA,EAAE,GAAG,YAAA,GAAe,WAAY;AACrC,UAAA,EAAEf,KAAKN,MAAQsB,EAAAA,QAAAA,EAAUC,UAAYC,EAAAA,MAAAA,IAAU,UAAU,CAAC;IAElE,IAAIxB,MAAAA,CAAOC,UAAU,EAAE;QACrBQ,SAAa,IAAA,CAAC,EAAEgB,MAAAA,CAAI,MAAM,EAAEnB,IAAIN,MAAM,CAACH,IAAI,CAAC,CAAC;AAC/C;IAEA,IAAIG,MAAAA,CAAOG,kBAAkB,EAAE;AAC7BM,QAAAA,SAAAA,IAAa,CAAC,EAAEgB,MAAAA,CAAI,cAAc,EAAEC,KAAKC,SAAS,CAACrB,GAAIN,CAAAA,MAAM,CAACiB,IAAI,CAACnB,YAAY,EAAE,MAAM,CAAG,CAAA;AAC5E,kBAAA,EAAE4B,IAAKC,CAAAA,SAAS,CAACrB,GAAAA,CAAIN,MAAM,CAACiB,IAAI,CAACW,eAAe,EAAE,IAAM,EAAA,CAAA,CAAA,CAAG,CAAC;AAC5E;AAEAC,IAAAA,OAAAA,CAAQC,GAAG,CAACrB,SAAAA,CAAAA;AAEZ,IAAA,MAAMH,IAAIyB,OAAO,EAAA;AACnB,CAAA;AAEA;;AAEC,UACKC,OAAyB,GAAA,IAAA;IAC7B,OAAOC,uBAAAA,CAAc,UAClBC,WAAW,CAAC,wDACZC,MAAM,CAAC,cAAc,sBACrBA,CAAAA,CAAAA,MAAM,CAAC,oBAAsB,EAAA,8BAAA,CAAA,CAC7BA,MAAM,CAAC,OAAA,EAAS,2BAChBvC,MAAM,CAACwC,kBAAU,QAAUxC,EAAAA,MAAAA,CAAAA,CAAAA;AAChC;;;;"}