{"version": 3, "file": "transfer-token-permission.js", "sources": ["../../../../../server/src/content-types/transfer-token-permission.ts"], "sourcesContent": ["export default {\n  collectionName: 'strapi_transfer_token_permissions',\n  info: {\n    name: 'Transfer Token Permission',\n    description: '',\n    singularName: 'transfer-token-permission',\n    pluralName: 'transfer-token-permissions',\n    displayName: 'Transfer Token Permission',\n  },\n  options: {},\n  pluginOptions: {\n    'content-manager': {\n      visible: false,\n    },\n    'content-type-builder': {\n      visible: false,\n    },\n  },\n  attributes: {\n    action: {\n      type: 'string',\n      minLength: 1,\n      configurable: false,\n      required: true,\n    },\n    token: {\n      configurable: false,\n      type: 'relation',\n      relation: 'manyToOne',\n      inversedBy: 'permissions',\n      target: 'admin::transfer-token',\n    },\n  },\n};\n"], "names": ["collectionName", "info", "name", "description", "singularName", "pluralName", "displayName", "options", "pluginOptions", "visible", "attributes", "action", "type", "<PERSON><PERSON><PERSON><PERSON>", "configurable", "required", "token", "relation", "inversedBy", "target"], "mappings": ";;AAAA,8BAAe;IACbA,cAAgB,EAAA,mCAAA;IAChBC,IAAM,EAAA;QACJC,IAAM,EAAA,2BAAA;QACNC,WAAa,EAAA,EAAA;QACbC,YAAc,EAAA,2BAAA;QACdC,UAAY,EAAA,4BAAA;QACZC,WAAa,EAAA;AACf,KAAA;AACAC,IAAAA,OAAAA,EAAS,EAAC;IACVC,aAAe,EAAA;QACb,iBAAmB,EAAA;YACjBC,OAAS,EAAA;AACX,SAAA;QACA,sBAAwB,EAAA;YACtBA,OAAS,EAAA;AACX;AACF,KAAA;IACAC,UAAY,EAAA;QACVC,MAAQ,EAAA;YACNC,IAAM,EAAA,QAAA;YACNC,SAAW,EAAA,CAAA;YACXC,YAAc,EAAA,KAAA;YACdC,QAAU,EAAA;AACZ,SAAA;QACAC,KAAO,EAAA;YACLF,YAAc,EAAA,KAAA;YACdF,IAAM,EAAA,UAAA;YACNK,QAAU,EAAA,WAAA;YACVC,UAAY,EAAA,aAAA;YACZC,MAAQ,EAAA;AACV;AACF;AACF,CAAE;;;;"}