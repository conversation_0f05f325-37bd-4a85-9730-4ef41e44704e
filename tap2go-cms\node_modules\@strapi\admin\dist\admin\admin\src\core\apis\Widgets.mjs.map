{"version": 3, "file": "Widgets.mjs", "sources": ["../../../../../../admin/src/core/apis/Widgets.ts"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\n\nimport invariant from 'invariant';\nimport { To } from 'react-router-dom';\n\nimport { Permission } from '../../../../shared/contracts/shared';\n\nimport type { Internal, Utils } from '@strapi/types';\nimport type { MessageDescriptor } from 'react-intl';\n\ntype WidgetUID = Utils.String.Suffix<\n  | Internal.Namespace.WithSeparator<Internal.Namespace.Plugin>\n  | Internal.Namespace.WithSeparator<Internal.Namespace.Global>,\n  string\n>;\n\ntype WidgetArgs = {\n  icon?: typeof import('@strapi/icons').PuzzlePiece;\n  title: MessageDescriptor;\n  link?: {\n    label: MessageDescriptor;\n    href: To;\n  };\n  component: () => Promise<React.ComponentType>;\n  pluginId?: string;\n  id: string;\n  permissions?: Permission[];\n};\n\ntype Widget = Omit<WidgetArgs, 'id' | 'pluginId'> & { uid: WidgetUID };\n\nclass Widgets {\n  widgets: Record<string, Widget>;\n\n  constructor() {\n    this.widgets = {};\n  }\n\n  register = (widget: WidgetArgs | WidgetArgs[]) => {\n    if (Array.isArray(widget)) {\n      widget.forEach((newWidget) => {\n        this.register(newWidget);\n      });\n    } else {\n      invariant(widget.id, 'An id must be provided');\n      invariant(widget.component, 'A component must be provided');\n      invariant(widget.title, 'A title must be provided');\n      invariant(widget.icon, 'An icon must be provided');\n\n      // Replace id and pluginId with computed uid\n      const { id, pluginId, ...widgetToStore } = widget;\n      const uid: WidgetUID = pluginId ? `plugin::${pluginId}.${id}` : `global::${id}`;\n\n      this.widgets[uid] = { ...widgetToStore, uid };\n    }\n  };\n\n  getAll = () => {\n    return Object.values(this.widgets);\n  };\n}\n\nexport { Widgets };\nexport type { WidgetArgs, Widget };\n"], "names": ["Widgets", "constructor", "register", "widget", "Array", "isArray", "for<PERSON>ach", "newWidget", "invariant", "id", "component", "title", "icon", "pluginId", "widgetToStore", "uid", "widgets", "getAll", "Object", "values"], "mappings": ";;AA+BA,MAAMA,OAAAA,CAAAA;IAGJC,WAAc,EAAA;AAIdC,QAAAA,IAAAA,CAAAA,QAAAA,GAAW,CAACC,MAAAA,GAAAA;YACV,IAAIC,KAAAA,CAAMC,OAAO,CAACF,MAAS,CAAA,EAAA;gBACzBA,MAAOG,CAAAA,OAAO,CAAC,CAACC,SAAAA,GAAAA;oBACd,IAAI,CAACL,QAAQ,CAACK,SAAAA,CAAAA;AAChB,iBAAA,CAAA;aACK,MAAA;gBACLC,SAAUL,CAAAA,MAAAA,CAAOM,EAAE,EAAE,wBAAA,CAAA;gBACrBD,SAAUL,CAAAA,MAAAA,CAAOO,SAAS,EAAE,8BAAA,CAAA;gBAC5BF,SAAUL,CAAAA,MAAAA,CAAOQ,KAAK,EAAE,0BAAA,CAAA;gBACxBH,SAAUL,CAAAA,MAAAA,CAAOS,IAAI,EAAE,0BAAA,CAAA;;AAGvB,gBAAA,MAAM,EAAEH,EAAE,EAAEI,QAAQ,EAAE,GAAGC,eAAe,GAAGX,MAAAA;AAC3C,gBAAA,MAAMY,GAAiBF,GAAAA,QAAAA,GAAW,CAAC,QAAQ,EAAEA,QAAS,CAAA,CAAC,EAAEJ,EAAAA,CAAG,CAAC,GAAG,CAAC,QAAQ,EAAEA,GAAG,CAAC;AAE/E,gBAAA,IAAI,CAACO,OAAO,CAACD,GAAAA,CAAI,GAAG;AAAE,oBAAA,GAAGD,aAAa;AAAEC,oBAAAA;AAAI,iBAAA;AAC9C;AACF,SAAA;aAEAE,MAAS,GAAA,IAAA;AACP,YAAA,OAAOC,MAAOC,CAAAA,MAAM,CAAC,IAAI,CAACH,OAAO,CAAA;AACnC,SAAA;QAxBE,IAAI,CAACA,OAAO,GAAG,EAAC;AAClB;AAwBF;;;;"}