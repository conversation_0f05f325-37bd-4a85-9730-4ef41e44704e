{"version": 3, "file": "transformPermissionsData.mjs", "sources": ["../../../../../../../../../../admin/src/pages/Settings/pages/ApiTokens/EditView/utils/transformPermissionsData.ts"], "sourcesContent": ["import { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\n\ninterface Layout {\n  allActionsIds: string[];\n  permissions: {\n    apiId: string;\n    label: string;\n    controllers: { controller: string; actions: { action: string; actionId: string }[] }[];\n  }[];\n}\n\nexport const transformPermissionsData = (data: ContentApiPermission) => {\n  const layout: Layout = {\n    allActionsIds: [],\n    permissions: [],\n  };\n\n  layout.permissions = Object.entries(data).map(([apiId, permission]) => ({\n    apiId,\n    label: apiId.split('::')[1],\n    controllers: Object.keys(permission.controllers)\n      .map((controller) => ({\n        controller,\n        actions:\n          controller in permission.controllers\n            ? permission.controllers[controller]\n                .map((action: ContentApiPermission['controllers']) => {\n                  const actionId = `${apiId}.${controller}.${action}`;\n\n                  if (apiId.includes('api::')) {\n                    layout.allActionsIds.push(actionId);\n                  }\n\n                  return {\n                    action,\n                    actionId,\n                  };\n                })\n                .flat()\n            : [],\n      }))\n      .flat(),\n  }));\n\n  return layout;\n};\n"], "names": ["transformPermissionsData", "data", "layout", "allActionsIds", "permissions", "Object", "entries", "map", "apiId", "permission", "label", "split", "controllers", "keys", "controller", "actions", "action", "actionId", "includes", "push", "flat"], "mappings": "AAWO,MAAMA,2BAA2B,CAACC,IAAAA,GAAAA;AACvC,IAAA,MAAMC,MAAiB,GAAA;AACrBC,QAAAA,aAAAA,EAAe,EAAE;AACjBC,QAAAA,WAAAA,EAAa;AACf,KAAA;AAEAF,IAAAA,MAAAA,CAAOE,WAAW,GAAGC,MAAOC,CAAAA,OAAO,CAACL,IAAAA,CAAAA,CAAMM,GAAG,CAAC,CAAC,CAACC,KAAOC,EAAAA,UAAAA,CAAW,IAAM;AACtED,YAAAA,KAAAA;AACAE,YAAAA,KAAAA,EAAOF,KAAMG,CAAAA,KAAK,CAAC,IAAA,CAAK,CAAC,CAAE,CAAA;YAC3BC,WAAaP,EAAAA,MAAAA,CAAOQ,IAAI,CAACJ,UAAWG,CAAAA,WAAW,EAC5CL,GAAG,CAAC,CAACO,UAAAA,IAAgB;AACpBA,oBAAAA,UAAAA;oBACAC,OACED,EAAAA,UAAAA,IAAcL,UAAWG,CAAAA,WAAW,GAChCH,UAAAA,CAAWG,WAAW,CAACE,UAAW,CAAA,CAC/BP,GAAG,CAAC,CAACS,MAAAA,GAAAA;wBACJ,MAAMC,QAAAA,GAAW,CAAC,EAAET,KAAM,CAAA,CAAC,EAAEM,UAAW,CAAA,CAAC,EAAEE,MAAAA,CAAO,CAAC;wBAEnD,IAAIR,KAAAA,CAAMU,QAAQ,CAAC,OAAU,CAAA,EAAA;4BAC3BhB,MAAOC,CAAAA,aAAa,CAACgB,IAAI,CAACF,QAAAA,CAAAA;AAC5B;wBAEA,OAAO;AACLD,4BAAAA,MAAAA;AACAC,4BAAAA;AACF,yBAAA;qBAEDG,CAAAA,CAAAA,IAAI,KACP;AACR,iBAAA,GACCA,IAAI;SACT,CAAA,CAAA;IAEA,OAAOlB,MAAAA;AACT;;;;"}