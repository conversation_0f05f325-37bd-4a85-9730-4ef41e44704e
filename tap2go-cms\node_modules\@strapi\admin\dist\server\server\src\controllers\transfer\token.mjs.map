{"version": 3, "file": "token.mjs", "sources": ["../../../../../../server/src/controllers/transfer/token.ts"], "sourcesContent": ["import { Context } from 'koa';\nimport { trim, has } from 'lodash/fp';\nimport { errors, strings } from '@strapi/utils';\nimport { getService } from '../../utils';\nimport { token } from '../../validation/transfer';\n\nimport type {\n  TokenCreate,\n  TokenGetById,\n  TokenList,\n  TokenRegenerate,\n  TokenRevoke,\n  TokenUpdate,\n} from '../../../../shared/contracts/transfer';\n\nconst { ApplicationError } = errors;\n\nconst { validateTransferTokenCreationInput, validateTransferTokenUpdateInput } = token;\n\nexport default {\n  async list(ctx: Context) {\n    const transferService = getService('transfer');\n    const transferTokens = await transferService.token.list();\n\n    ctx.body = { data: transferTokens } satisfies TokenList.Response;\n  },\n\n  async getById(ctx: Context) {\n    const { id } = ctx.params as TokenGetById.Params;\n    const tokenService = getService('transfer').token;\n\n    const transferToken = await tokenService.getById(id);\n\n    if (!transferToken) {\n      ctx.notFound('Transfer token not found');\n      return;\n    }\n\n    ctx.body = { data: transferToken } satisfies TokenGetById.Response;\n  },\n\n  async create(ctx: Context) {\n    const { body } = ctx.request as TokenCreate.Request;\n    const { token: tokenService } = getService('transfer');\n\n    /**\n     * We trim fields to avoid having issues with either:\n     * - having a space at the end or start of the value\n     * - having only spaces as value (so that an empty field can be caught in validation)\n     */\n    const attributes = {\n      name: trim(body.name),\n      description: trim(body.description),\n      permissions: body.permissions,\n      lifespan: body.lifespan,\n    };\n\n    await validateTransferTokenCreationInput(attributes);\n\n    const alreadyExists = await tokenService.exists({ name: attributes.name });\n    if (alreadyExists) {\n      throw new ApplicationError('Name already taken');\n    }\n\n    const transferTokens = await tokenService.create(attributes);\n\n    ctx.created({ data: transferTokens } satisfies TokenCreate.Response);\n  },\n\n  async update(ctx: Context) {\n    const { body } = ctx.request as TokenUpdate.Request;\n    const { id } = ctx.params as TokenUpdate.Params;\n    const { token: tokenService } = getService('transfer');\n\n    const attributes = body;\n    /**\n     * We trim fields to avoid having issues with either:\n     * - having a space at the end or start of the value\n     * - having only spaces as value (so that an empty field can be caught in validation)\n     */\n    if (has('name', attributes)) {\n      attributes.name = trim(body.name);\n    }\n\n    if (has('description', attributes) || attributes.description === null) {\n      attributes.description = trim(body.description);\n    }\n\n    await validateTransferTokenUpdateInput(attributes);\n\n    const apiTokenExists = await tokenService.getById(id);\n    if (!apiTokenExists) {\n      return ctx.notFound('Transfer token not found');\n    }\n\n    if (has('name', attributes)) {\n      const nameAlreadyTaken = await tokenService.getByName(attributes.name);\n\n      /**\n       * We cast the ids as string as the one coming from the ctx isn't cast\n       * as a Number in case it is supposed to be an integer. It remains\n       * as a string. This way we avoid issues with integers in the db.\n       */\n      if (!!nameAlreadyTaken && !strings.isEqual(nameAlreadyTaken.id, id)) {\n        throw new ApplicationError('Name already taken');\n      }\n    }\n\n    const apiToken = await tokenService.update(id, attributes);\n\n    ctx.body = { data: apiToken } satisfies TokenUpdate.Response;\n  },\n\n  async revoke(ctx: Context) {\n    const { id } = ctx.params as TokenRevoke.Params;\n    const { token: tokenService } = getService('transfer');\n\n    const transferToken = await tokenService.revoke(id);\n\n    ctx.deleted({ data: transferToken } satisfies TokenRevoke.Response);\n  },\n\n  async regenerate(ctx: Context) {\n    const { id } = ctx.params as TokenRegenerate.Params;\n    const { token: tokenService } = getService('transfer');\n\n    const exists = await tokenService.getById(id);\n    if (!exists) {\n      ctx.notFound('Transfer token not found');\n      return;\n    }\n\n    const accessToken = await tokenService.regenerate(id);\n\n    ctx.created({ data: accessToken } satisfies TokenRegenerate.Response);\n  },\n};\n"], "names": ["ApplicationError", "errors", "validateTransferTokenCreationInput", "validateTransferTokenUpdateInput", "token", "list", "ctx", "transferService", "getService", "transferTokens", "body", "data", "getById", "id", "params", "tokenService", "transferToken", "notFound", "create", "request", "attributes", "name", "trim", "description", "permissions", "lifespan", "alreadyExists", "exists", "created", "update", "has", "apiTokenExists", "nameAlreadyTaken", "getByName", "strings", "isEqual", "apiToken", "revoke", "deleted", "regenerate", "accessToken"], "mappings": ";;;;;AAeA,MAAM,EAAEA,gBAAgB,EAAE,GAAGC,MAAAA;AAE7B,MAAM,EAAEC,kCAAkC,EAAEC,gCAAgC,EAAE,GAAGC,OAAAA;AAEjF,YAAe;AACb,IAAA,MAAMC,MAAKC,GAAY,EAAA;AACrB,QAAA,MAAMC,kBAAkBC,UAAW,CAAA,UAAA,CAAA;AACnC,QAAA,MAAMC,cAAiB,GAAA,MAAMF,eAAgBH,CAAAA,KAAK,CAACC,IAAI,EAAA;AAEvDC,QAAAA,GAAAA,CAAII,IAAI,GAAG;YAAEC,IAAMF,EAAAA;AAAe,SAAA;AACpC,KAAA;AAEA,IAAA,MAAMG,SAAQN,GAAY,EAAA;AACxB,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;QACzB,MAAMC,YAAAA,GAAeP,UAAW,CAAA,UAAA,CAAA,CAAYJ,KAAK;AAEjD,QAAA,MAAMY,aAAgB,GAAA,MAAMD,YAAaH,CAAAA,OAAO,CAACC,EAAAA,CAAAA;AAEjD,QAAA,IAAI,CAACG,aAAe,EAAA;AAClBV,YAAAA,GAAAA,CAAIW,QAAQ,CAAC,0BAAA,CAAA;AACb,YAAA;AACF;AAEAX,QAAAA,GAAAA,CAAII,IAAI,GAAG;YAAEC,IAAMK,EAAAA;AAAc,SAAA;AACnC,KAAA;AAEA,IAAA,MAAME,QAAOZ,GAAY,EAAA;AACvB,QAAA,MAAM,EAAEI,IAAI,EAAE,GAAGJ,IAAIa,OAAO;AAC5B,QAAA,MAAM,EAAEf,KAAAA,EAAOW,YAAY,EAAE,GAAGP,UAAW,CAAA,UAAA,CAAA;AAE3C;;;;AAIC,QACD,MAAMY,UAAa,GAAA;YACjBC,IAAMC,EAAAA,IAAAA,CAAKZ,KAAKW,IAAI,CAAA;YACpBE,WAAaD,EAAAA,IAAAA,CAAKZ,KAAKa,WAAW,CAAA;AAClCC,YAAAA,WAAAA,EAAad,KAAKc,WAAW;AAC7BC,YAAAA,QAAAA,EAAUf,KAAKe;AACjB,SAAA;AAEA,QAAA,MAAMvB,kCAAmCkB,CAAAA,UAAAA,CAAAA;AAEzC,QAAA,MAAMM,aAAgB,GAAA,MAAMX,YAAaY,CAAAA,MAAM,CAAC;AAAEN,YAAAA,IAAAA,EAAMD,WAAWC;AAAK,SAAA,CAAA;AACxE,QAAA,IAAIK,aAAe,EAAA;AACjB,YAAA,MAAM,IAAI1B,gBAAiB,CAAA,oBAAA,CAAA;AAC7B;AAEA,QAAA,MAAMS,cAAiB,GAAA,MAAMM,YAAaG,CAAAA,MAAM,CAACE,UAAAA,CAAAA;AAEjDd,QAAAA,GAAAA,CAAIsB,OAAO,CAAC;YAAEjB,IAAMF,EAAAA;AAAe,SAAA,CAAA;AACrC,KAAA;AAEA,IAAA,MAAMoB,QAAOvB,GAAY,EAAA;AACvB,QAAA,MAAM,EAAEI,IAAI,EAAE,GAAGJ,IAAIa,OAAO;AAC5B,QAAA,MAAM,EAAEN,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AACzB,QAAA,MAAM,EAAEV,KAAAA,EAAOW,YAAY,EAAE,GAAGP,UAAW,CAAA,UAAA,CAAA;AAE3C,QAAA,MAAMY,UAAaV,GAAAA,IAAAA;AACnB;;;;QAKA,IAAIoB,GAAI,CAAA,MAAA,EAAQV,UAAa,CAAA,EAAA;AAC3BA,YAAAA,UAAAA,CAAWC,IAAI,GAAGC,IAAKZ,CAAAA,IAAAA,CAAKW,IAAI,CAAA;AAClC;AAEA,QAAA,IAAIS,IAAI,aAAeV,EAAAA,UAAAA,CAAAA,IAAeA,UAAWG,CAAAA,WAAW,KAAK,IAAM,EAAA;AACrEH,YAAAA,UAAAA,CAAWG,WAAW,GAAGD,IAAKZ,CAAAA,IAAAA,CAAKa,WAAW,CAAA;AAChD;AAEA,QAAA,MAAMpB,gCAAiCiB,CAAAA,UAAAA,CAAAA;AAEvC,QAAA,MAAMW,cAAiB,GAAA,MAAMhB,YAAaH,CAAAA,OAAO,CAACC,EAAAA,CAAAA;AAClD,QAAA,IAAI,CAACkB,cAAgB,EAAA;YACnB,OAAOzB,GAAAA,CAAIW,QAAQ,CAAC,0BAAA,CAAA;AACtB;QAEA,IAAIa,GAAAA,CAAI,QAAQV,UAAa,CAAA,EAAA;AAC3B,YAAA,MAAMY,mBAAmB,MAAMjB,YAAAA,CAAakB,SAAS,CAACb,WAAWC,IAAI,CAAA;AAErE;;;;UAKA,IAAI,CAAC,CAACW,gBAAoB,IAAA,CAACE,OAAQC,CAAAA,OAAO,CAACH,gBAAAA,CAAiBnB,EAAE,EAAEA,EAAK,CAAA,EAAA;AACnE,gBAAA,MAAM,IAAIb,gBAAiB,CAAA,oBAAA,CAAA;AAC7B;AACF;AAEA,QAAA,MAAMoC,QAAW,GAAA,MAAMrB,YAAac,CAAAA,MAAM,CAAChB,EAAIO,EAAAA,UAAAA,CAAAA;AAE/Cd,QAAAA,GAAAA,CAAII,IAAI,GAAG;YAAEC,IAAMyB,EAAAA;AAAS,SAAA;AAC9B,KAAA;AAEA,IAAA,MAAMC,QAAO/B,GAAY,EAAA;AACvB,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AACzB,QAAA,MAAM,EAAEV,KAAAA,EAAOW,YAAY,EAAE,GAAGP,UAAW,CAAA,UAAA,CAAA;AAE3C,QAAA,MAAMQ,aAAgB,GAAA,MAAMD,YAAasB,CAAAA,MAAM,CAACxB,EAAAA,CAAAA;AAEhDP,QAAAA,GAAAA,CAAIgC,OAAO,CAAC;YAAE3B,IAAMK,EAAAA;AAAc,SAAA,CAAA;AACpC,KAAA;AAEA,IAAA,MAAMuB,YAAWjC,GAAY,EAAA;AAC3B,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AACzB,QAAA,MAAM,EAAEV,KAAAA,EAAOW,YAAY,EAAE,GAAGP,UAAW,CAAA,UAAA,CAAA;AAE3C,QAAA,MAAMmB,MAAS,GAAA,MAAMZ,YAAaH,CAAAA,OAAO,CAACC,EAAAA,CAAAA;AAC1C,QAAA,IAAI,CAACc,MAAQ,EAAA;AACXrB,YAAAA,GAAAA,CAAIW,QAAQ,CAAC,0BAAA,CAAA;AACb,YAAA;AACF;AAEA,QAAA,MAAMuB,WAAc,GAAA,MAAMzB,YAAawB,CAAAA,UAAU,CAAC1B,EAAAA,CAAAA;AAElDP,QAAAA,GAAAA,CAAIsB,OAAO,CAAC;YAAEjB,IAAM6B,EAAAA;AAAY,SAAA,CAAA;AAClC;AACF,CAAE;;;;"}