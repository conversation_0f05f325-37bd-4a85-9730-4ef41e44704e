{"version": 3, "file": "urls.js", "sources": ["../../../../../admin/src/utils/urls.ts"], "sourcesContent": ["import trimEnd from 'lodash/trimEnd';\n\nconst prefixFileUrlWithBackendUrl = (fileURL?: string): string | undefined => {\n  return !!fileURL && fileURL.startsWith('/') ? `${window.strapi.backendURL}${fileURL}` : fileURL;\n};\n\n/**\n * @description Creates an absolute URL, if there is no URL or it\n * is relative, we use the `window.location.origin` as a fallback.\n * IF it's an absolute URL, we return it as is.\n */\nconst createAbsoluteUrl = (url?: string): string => {\n  if (!url) {\n    return window.location.origin;\n  }\n  if (url.startsWith('/')) {\n    /**\n     * This will also manage protocol relative URLs which is fine because\n     * as we can see from the test, we still get the expected result.\n     */\n    return trimEnd(new URL(url, window.location.origin).toString(), '/');\n  } else {\n    return url;\n  }\n};\n\nexport { createAbsoluteUrl, prefixFileUrlWithBackendUrl };\n"], "names": ["prefixFileUrlWithBackendUrl", "fileURL", "startsWith", "window", "strapi", "backendURL", "createAbsoluteUrl", "url", "location", "origin", "trimEnd", "URL", "toString"], "mappings": ";;;;AAEA,MAAMA,8BAA8B,CAACC,OAAAA,GAAAA;AACnC,IAAA,OAAO,CAAC,CAACA,OAAAA,IAAWA,OAAQC,CAAAA,UAAU,CAAC,GAAO,CAAA,GAAA,CAAC,EAAEC,MAAAA,CAAOC,MAAM,CAACC,UAAU,CAAC,EAAEJ,OAAAA,CAAQ,CAAC,GAAGA,OAAAA;AAC1F;AAEA;;;;IAKA,MAAMK,oBAAoB,CAACC,GAAAA,GAAAA;AACzB,IAAA,IAAI,CAACA,GAAK,EAAA;QACR,OAAOJ,MAAAA,CAAOK,QAAQ,CAACC,MAAM;AAC/B;IACA,IAAIF,GAAAA,CAAIL,UAAU,CAAC,GAAM,CAAA,EAAA;AACvB;;;QAIA,OAAOQ,OAAQ,CAAA,IAAIC,GAAIJ,CAAAA,GAAAA,EAAKJ,MAAOK,CAAAA,QAAQ,CAACC,MAAM,CAAEG,CAAAA,QAAQ,EAAI,EAAA,GAAA,CAAA;KAC3D,MAAA;QACL,OAAOL,GAAAA;AACT;AACF;;;;;"}