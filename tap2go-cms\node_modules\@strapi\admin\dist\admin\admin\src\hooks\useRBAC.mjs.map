{"version": 3, "file": "useRBAC.mjs", "sources": ["../../../../../admin/src/hooks/useRBAC.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport isEqual from 'lodash/isEqual';\n\nimport { useAuth, Permission } from '../features/Auth';\nimport { once } from '../utils/once';\nimport { capitalise } from '../utils/strings';\n\nimport { usePrev } from './usePrev';\n\ntype AllowedActions = Record<string, boolean>;\n\n/**\n * @public\n * @description This hooks takes an object or array of permissions (the latter preferred) and\n * runs through them to match against the current user's permissions as well as the RBAC middleware\n * system checking any conditions that may be present. It returns the filtered permissions as the complete\n * object from the API and a set of actions that can be performed. An action is derived from the last part\n * of the permission action e.g. `admin::roles.create` would be `canCreate`. If there's a hyphen in the action\n * this is removed and capitalised e.g `admin::roles.create-draft` would be `canCreateDraft`.\n * @example\n * ```tsx\n * import { Page, useRBAC } from '@strapi/strapi/admin'\n *\n * const MyProtectedPage = () => {\n *  const { allowedActions, isLoading, error, permissions } = useRBAC([{ action: 'admin::roles.create' }])\n *\n *  if(isLoading) {\n *    return <Page.Loading />\n *  }\n *\n *  if(error){\n *    return <Page.Error />\n *  }\n *\n *  if(!allowedActions.canCreate) {\n *    return null\n *  }\n *\n *  return <MyPage permissions={permissions} />\n * }\n * ```\n */\nconst useRBAC = (\n  permissionsToCheck: Record<string, Permission[]> | Permission[] = [],\n  passedPermissions?: Permission[],\n  rawQueryContext?: string\n): {\n  allowedActions: AllowedActions;\n  isLoading: boolean;\n  error?: unknown;\n  permissions: Permission[];\n} => {\n  const isLoadingAuth = useAuth('useRBAC', (state) => state.isLoading);\n  const [isLoading, setIsLoading] = React.useState(true);\n  const [error, setError] = React.useState<unknown>();\n  const [data, setData] = React.useState<Record<string, boolean>>();\n\n  const warnOnce = React.useMemo(() => once(console.warn), []);\n\n  const actualPermissionsToCheck: Permission[] = React.useMemo(() => {\n    if (Array.isArray(permissionsToCheck)) {\n      return permissionsToCheck;\n    } else {\n      warnOnce(\n        'useRBAC: The first argument should be an array of permissions, not an object. This will be deprecated in the future.'\n      );\n\n      return Object.values(permissionsToCheck).flat();\n    }\n  }, [permissionsToCheck, warnOnce]);\n\n  /**\n   * This is the default value we return until the queryResults[i].data\n   * are all resolved with data. This preserves the original behaviour.\n   */\n  const defaultAllowedActions = React.useMemo(() => {\n    return actualPermissionsToCheck.reduce<Record<string, boolean>>((acc, permission) => {\n      return {\n        ...acc,\n        [getActionName(permission)]: false,\n      };\n    }, {});\n  }, [actualPermissionsToCheck]);\n\n  const checkUserHasPermissions = useAuth('useRBAC', (state) => state.checkUserHasPermissions);\n\n  const permssionsChecked = usePrev(actualPermissionsToCheck);\n  const contextChecked = usePrev(rawQueryContext);\n\n  React.useEffect(() => {\n    if (\n      !isEqual(permssionsChecked, actualPermissionsToCheck) ||\n      // TODO: also run this when the query context changes\n      contextChecked !== rawQueryContext\n    ) {\n      setIsLoading(true);\n      setData(undefined);\n      setError(undefined);\n\n      checkUserHasPermissions(actualPermissionsToCheck, passedPermissions, rawQueryContext)\n        .then((res) => {\n          if (res) {\n            setData(\n              res.reduce<Record<string, boolean>>((acc, permission) => {\n                return {\n                  ...acc,\n                  [getActionName(permission)]: true,\n                };\n              }, {})\n            );\n          }\n        })\n        .catch((err) => {\n          setError(err);\n        })\n        .finally(() => {\n          setIsLoading(false);\n        });\n    }\n  }, [\n    actualPermissionsToCheck,\n    checkUserHasPermissions,\n    passedPermissions,\n    permissionsToCheck,\n    permssionsChecked,\n    contextChecked,\n    rawQueryContext,\n  ]);\n\n  /**\n   * This hook originally would not return allowedActions\n   * until all the checks were complete.\n   */\n  const allowedActions = Object.entries({\n    ...defaultAllowedActions,\n    ...data,\n  }).reduce((acc, [name, allowed]) => {\n    acc[`can${capitalise(name)}`] = allowed;\n\n    return acc;\n  }, {} as AllowedActions);\n\n  return {\n    allowedActions,\n    permissions: actualPermissionsToCheck,\n    isLoading: isLoading || isLoadingAuth,\n    error,\n  };\n};\n\nconst getActionName = (permission: Permission): string => {\n  const [action = ''] = permission.action.split('.').slice(-1);\n  return action.split('-').map(capitalise).join('');\n};\n\nexport { useRBAC };\nexport type { AllowedActions };\n"], "names": ["useRBAC", "permissionsToCheck", "passedPermissions", "rawQueryContext", "isLoadingAuth", "useAuth", "state", "isLoading", "setIsLoading", "React", "useState", "error", "setError", "data", "setData", "warnOnce", "useMemo", "once", "console", "warn", "actualPermissionsToCheck", "Array", "isArray", "Object", "values", "flat", "defaultAllowedActions", "reduce", "acc", "permission", "getActionName", "checkUserHasPermissions", "permssionsChecked", "usePrev", "contextChecked", "useEffect", "isEqual", "undefined", "then", "res", "catch", "err", "finally", "allowedActions", "entries", "name", "allowed", "capitalise", "permissions", "action", "split", "slice", "map", "join"], "mappings": ";;;;;;;AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BC,UACKA,OAAU,GAAA,CACdC,kBAAkE,GAAA,EAAE,EACpEC,iBACAC,EAAAA,eAAAA,GAAAA;AAOA,IAAA,MAAMC,gBAAgBC,OAAQ,CAAA,SAAA,EAAW,CAACC,KAAAA,GAAUA,MAAMC,SAAS,CAAA;AACnE,IAAA,MAAM,CAACA,SAAWC,EAAAA,YAAAA,CAAa,GAAGC,KAAAA,CAAMC,QAAQ,CAAC,IAAA,CAAA;AACjD,IAAA,MAAM,CAACC,KAAAA,EAAOC,QAAS,CAAA,GAAGH,MAAMC,QAAQ,EAAA;AACxC,IAAA,MAAM,CAACG,IAAAA,EAAMC,OAAQ,CAAA,GAAGL,MAAMC,QAAQ,EAAA;IAEtC,MAAMK,QAAAA,GAAWN,MAAMO,OAAO,CAAC,IAAMC,IAAKC,CAAAA,OAAAA,CAAQC,IAAI,CAAA,EAAG,EAAE,CAAA;IAE3D,MAAMC,wBAAAA,GAAyCX,KAAMO,CAAAA,OAAO,CAAC,IAAA;QAC3D,IAAIK,KAAAA,CAAMC,OAAO,CAACrB,kBAAqB,CAAA,EAAA;YACrC,OAAOA,kBAAAA;SACF,MAAA;YACLc,QACE,CAAA,sHAAA,CAAA;AAGF,YAAA,OAAOQ,MAAOC,CAAAA,MAAM,CAACvB,kBAAAA,CAAAA,CAAoBwB,IAAI,EAAA;AAC/C;KACC,EAAA;AAACxB,QAAAA,kBAAAA;AAAoBc,QAAAA;AAAS,KAAA,CAAA;AAEjC;;;AAGC,MACD,MAAMW,qBAAAA,GAAwBjB,KAAMO,CAAAA,OAAO,CAAC,IAAA;AAC1C,QAAA,OAAOI,wBAAyBO,CAAAA,MAAM,CAA0B,CAACC,GAAKC,EAAAA,UAAAA,GAAAA;YACpE,OAAO;AACL,gBAAA,GAAGD,GAAG;gBACN,CAACE,aAAAA,CAAcD,cAAc;AAC/B,aAAA;AACF,SAAA,EAAG,EAAC,CAAA;KACH,EAAA;AAACT,QAAAA;AAAyB,KAAA,CAAA;AAE7B,IAAA,MAAMW,0BAA0B1B,OAAQ,CAAA,SAAA,EAAW,CAACC,KAAAA,GAAUA,MAAMyB,uBAAuB,CAAA;AAE3F,IAAA,MAAMC,oBAAoBC,OAAQb,CAAAA,wBAAAA,CAAAA;AAClC,IAAA,MAAMc,iBAAiBD,OAAQ9B,CAAAA,eAAAA,CAAAA;AAE/BM,IAAAA,KAAAA,CAAM0B,SAAS,CAAC,IAAA;AACd,QAAA,IACE,CAACC,OAAAA,CAAQJ,iBAAmBZ,EAAAA,wBAAAA,CAAAA;AAE5Bc,QAAAA,cAAAA,KAAmB/B,eACnB,EAAA;YACAK,YAAa,CAAA,IAAA,CAAA;YACbM,OAAQuB,CAAAA,SAAAA,CAAAA;YACRzB,QAASyB,CAAAA,SAAAA,CAAAA;AAETN,YAAAA,uBAAAA,CAAwBX,wBAA0BlB,EAAAA,iBAAAA,EAAmBC,eAClEmC,CAAAA,CAAAA,IAAI,CAAC,CAACC,GAAAA,GAAAA;AACL,gBAAA,IAAIA,GAAK,EAAA;AACPzB,oBAAAA,OAAAA,CACEyB,GAAIZ,CAAAA,MAAM,CAA0B,CAACC,GAAKC,EAAAA,UAAAA,GAAAA;wBACxC,OAAO;AACL,4BAAA,GAAGD,GAAG;4BACN,CAACE,aAAAA,CAAcD,cAAc;AAC/B,yBAAA;AACF,qBAAA,EAAG,EAAC,CAAA,CAAA;AAER;aAEDW,CAAAA,CAAAA,KAAK,CAAC,CAACC,GAAAA,GAAAA;gBACN7B,QAAS6B,CAAAA,GAAAA,CAAAA;AACX,aAAA,CAAA,CACCC,OAAO,CAAC,IAAA;gBACPlC,YAAa,CAAA,KAAA,CAAA;AACf,aAAA,CAAA;AACJ;KACC,EAAA;AACDY,QAAAA,wBAAAA;AACAW,QAAAA,uBAAAA;AACA7B,QAAAA,iBAAAA;AACAD,QAAAA,kBAAAA;AACA+B,QAAAA,iBAAAA;AACAE,QAAAA,cAAAA;AACA/B,QAAAA;AACD,KAAA,CAAA;AAED;;;AAGC,MACD,MAAMwC,cAAAA,GAAiBpB,MAAOqB,CAAAA,OAAO,CAAC;AACpC,QAAA,GAAGlB,qBAAqB;AACxB,QAAA,GAAGb;AACL,KAAA,CAAA,CAAGc,MAAM,CAAC,CAACC,GAAK,EAAA,CAACiB,MAAMC,OAAQ,CAAA,GAAA;QAC7BlB,GAAG,CAAC,CAAC,GAAG,EAAEmB,WAAWF,IAAM,CAAA,CAAA,CAAC,CAAC,GAAGC,OAAAA;QAEhC,OAAOlB,GAAAA;AACT,KAAA,EAAG,EAAC,CAAA;IAEJ,OAAO;AACLe,QAAAA,cAAAA;QACAK,WAAa5B,EAAAA,wBAAAA;AACbb,QAAAA,SAAAA,EAAWA,SAAaH,IAAAA,aAAAA;AACxBO,QAAAA;AACF,KAAA;AACF;AAEA,MAAMmB,gBAAgB,CAACD,UAAAA,GAAAA;AACrB,IAAA,MAAM,CAACoB,MAAAA,GAAS,EAAE,CAAC,GAAGpB,UAAAA,CAAWoB,MAAM,CAACC,KAAK,CAAC,GAAKC,CAAAA,CAAAA,KAAK,CAAC,CAAC,CAAA,CAAA;IAC1D,OAAOF,MAAAA,CAAOC,KAAK,CAAC,GAAA,CAAA,CAAKE,GAAG,CAACL,UAAAA,CAAAA,CAAYM,IAAI,CAAC,EAAA,CAAA;AAChD,CAAA;;;;"}