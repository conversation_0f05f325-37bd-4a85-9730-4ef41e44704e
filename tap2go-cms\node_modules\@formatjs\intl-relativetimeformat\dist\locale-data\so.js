/* @generated */	
// prettier-ignore
if (Intl.RelativeTimeFormat && typeof Intl.RelativeTimeFormat.__addLocaleData === 'function') {
  Intl.RelativeTimeFormat.__addLocaleData({"data":{"so":{"nu":["latn"],"year":{"0":"Sannadkan","1":"Sannadka danbe","future":{"one":"{0} sannad","other":"{0} sannadood"},"past":{"one":"{0} sannad kahor","other":"{0} sannadood kahor"},"-1":"Sannadkii hore"},"year-short":{"0":"Sannadkan","1":"Sannadka danbe","future":{"one":"{0} snd","other":"{0} snd"},"past":{"one":"{0} snd khr","other":"{0} Snd khr"},"-1":"Sannadkii hore"},"year-narrow":{"0":"Sannadkan","1":"Sannadka xiga","future":{"one":"{0} snd","other":"{0} snd"},"past":{"one":"{0} snd khr","other":"{0} Snd khr"},"-1":"Sannadkii la soo dhaafay"},"quarter":{"0":"Rubucan","1":"Rubuca danbe","future":{"one":"{0} rubuc","other":"{0} rubuc"},"past":{"one":"{0} rubuc kahor","other":"{0} rubuc kahor"},"-1":"Rubucii hore"},"quarter-short":{"0":"Rubucan","1":"Rubuca danbe","future":{"one":"{0} rbc","other":"{0} rbc"},"past":{"one":"{0} rbc khr","other":"{0} rbc khr"},"-1":"Rubucii hore"},"quarter-narrow":{"0":"Rubucan","1":"Rubuca danbe","future":{"one":"{0} rbc","other":"{0} rbc"},"past":{"one":"{0} rbc khr","other":"{0} rbc khr"},"-1":"Rubucii hore"},"month":{"0":"Bishan","1":"Bisha danbe","future":{"one":"{0} bil","other":"{0} bilood"},"past":{"one":"{0} bil kahor","other":"{0} bilood kahor"},"-1":"Bishii hore"},"month-short":{"0":"Bishan","1":"Bisha danbe","future":{"one":"{0} bil","other":"{0} bil"},"past":{"one":"{0} bil khr","other":"{0} bil khr"},"-1":"Bishii hore"},"month-narrow":{"0":"Bishan","1":"Bisha danbe","future":{"one":"{0} bil","other":"{0} bil"},"past":{"one":"{0} bil khr","other":"{0} bil khr"},"-1":"Bishii hore"},"week":{"0":"Usbuucan","1":"Toddobaadka danbe","future":{"one":"{0} toddobaad","other":"{0} toddobaad"},"past":{"one":"{0} toddobaad kahor","other":"{0} toddobaad kahor"},"-1":"Toddobaadkii hore"},"week-short":{"0":"Usbuucan","1":"Toddobaadka danbe","future":{"one":"{0} tdbd","other":"{0} tdbd"},"past":{"one":"{0} tdbd khr","other":"{0} tdbd khr"},"-1":"Toddobaadkii hore"},"week-narrow":{"0":"Toddobaadkan","1":"Toddobaadka danbe","future":{"one":"{0} tdbd","other":"{0} tdbd"},"past":{"one":"{0} tdbd khr","other":"{0} tdbd khr"},"-1":"Toddobaadkii hore"},"day":{"0":"Maanta","1":"Berri","future":{"one":"{0} maalin","other":"{0} maalmood"},"past":{"one":"{0} maalin kahor","other":"{0} maalmood kahor"},"-1":"Shalay"},"day-short":{"0":"Maanta","1":"Berri","future":{"one":"{0} mln","other":"{0} mlmd"},"past":{"one":"{0} mln khr","other":"{0} mlmd khr"},"-1":"Shalay"},"day-narrow":{"0":"Maanta","1":"Berri","future":{"one":"{0} mln","other":"{0} mlmd"},"past":{"one":"{0} mln khr","other":"{0} mlmd khr"},"-1":"Shalay"},"hour":{"0":"Saacadan","future":{"one":"{0} saacad","other":"{0} saacadood"},"past":{"one":"{0} saacad kahor","other":"{0} saacadood kahor"}},"hour-short":{"0":"Saacadan","future":{"one":"{0} scd","other":"{0} scd"},"past":{"one":"{0} scd khr","other":"{0} scd khr"}},"hour-narrow":{"0":"Saacadan","future":{"one":"{0} scd","other":"{0} scd"},"past":{"one":"{0} scd khr","other":"{0} scd khr"}},"minute":{"0":"Daqiiqadan","future":{"one":"{0} daqiiqad","other":"{0} daqiidadood"},"past":{"one":"{0} daqiiqad kahor","other":"{0} daqiiqadood kahor"}},"minute-short":{"0":"Daqiiqadan","future":{"one":"{0} dqqd","other":"{0} dqqd"},"past":{"one":"{0} dqqd khr","other":"{0} daqiiqadood kahor"}},"minute-narrow":{"0":"Daqiiqadan","future":{"one":"{0} dqqd","other":"{0} dqqd"},"past":{"one":"{0} dqqd khr","other":"{0} daqiiqadood kahor"}},"second":{"0":"Imika","future":{"one":"{0} ilbiriqsi","other":"{0} ilbiriqsi"},"past":{"one":"{0} ilbiriqsi kahor","other":"{0} ilbiriqsi kahor"}},"second-short":{"0":"Imika","future":{"one":"{0} ilbrqsi","other":"{0} ilbrqsi"},"past":{"one":"{0} ilbrqsi khr","other":"{0} ilbrqsi khr"}},"second-narrow":{"0":"Imika","future":{"one":"{0} ilbrqsi","other":"{0} ilbrqsi"},"past":{"one":"{0} ilbrqsi khr","other":"{0} ilbrqsi khr"}}}},"availableLocales":["so-DJ","so-ET","so-KE","so"],"aliases":{},"parentLocales":{}})
}