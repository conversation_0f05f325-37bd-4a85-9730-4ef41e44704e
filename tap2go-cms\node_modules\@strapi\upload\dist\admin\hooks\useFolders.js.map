{"version": 3, "file": "useFolders.js", "sources": ["../../../admin/src/hooks/useFolders.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useNotification, useFetchClient } from '@strapi/admin/strapi-admin';\nimport { useNotifyAT } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useQuery } from 'react-query';\n\nimport { GetFolders } from '../../../shared/contracts/folders';\nimport { pluginId } from '../pluginId';\n\nimport type { Query } from '../../../shared/contracts/files';\n\ninterface UseFoldersOptions {\n  enabled?: boolean;\n  query?: Query;\n}\n\nexport const useFolders = ({ enabled = true, query = {} }: UseFoldersOptions = {}) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { notifyStatus } = useNotifyAT();\n  const { folder, _q, ...paramsExceptFolderAndQ } = query;\n  const { get } = useFetchClient();\n\n  let params: Query;\n\n  if (_q) {\n    params = {\n      ...paramsExceptFolderAndQ,\n      pagination: {\n        pageSize: -1,\n      },\n      _q,\n    };\n  } else {\n    params = {\n      ...paramsExceptFolderAndQ,\n      pagination: {\n        pageSize: -1,\n      },\n      filters: {\n        $and: [\n          ...(paramsExceptFolderAndQ?.filters?.$and ?? []),\n          {\n            parent: {\n              id: folder ?? {\n                $null: true,\n              },\n            },\n          },\n        ],\n      },\n    };\n  }\n\n  const { data, error, isLoading } = useQuery<\n    GetFolders.Response['data'],\n    GetFolders.Response['error']\n  >(\n    [pluginId, 'folders', stringify(params)],\n    async () => {\n      const {\n        data: { data },\n      } = await get<GetFolders.Response>('/upload/folders', { params });\n\n      return data;\n    },\n    {\n      enabled,\n      staleTime: 0,\n      cacheTime: 0,\n      onError() {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error' }),\n        });\n      },\n    }\n  );\n\n  React.useEffect(() => {\n    if (data) {\n      notifyStatus(\n        formatMessage({\n          id: 'list.asset.at.finished',\n          defaultMessage: 'The folders have finished loading.',\n        })\n      );\n    }\n  }, [data, formatMessage, notifyStatus]);\n\n  return { data, error, isLoading };\n};\n"], "names": ["useFolders", "enabled", "query", "formatMessage", "useIntl", "toggleNotification", "useNotification", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "folder", "_q", "paramsExceptFolderAndQ", "get", "useFetchClient", "params", "pagination", "pageSize", "filters", "$and", "parent", "id", "$null", "data", "error", "isLoading", "useQuery", "pluginId", "stringify", "staleTime", "cacheTime", "onError", "type", "message", "React", "useEffect", "defaultMessage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBaA,MAAAA,UAAAA,GAAa,CAAC,EAAEC,OAAU,GAAA,IAAI,EAAEC,KAAAA,GAAQ,EAAE,EAAqB,GAAG,EAAE,GAAA;IAC/E,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;IAC1B,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,2BAAAA,EAAAA;IAC/B,MAAM,EAAEC,YAAY,EAAE,GAAGC,wBAAAA,EAAAA;AACzB,IAAA,MAAM,EAAEC,MAAM,EAAEC,EAAE,EAAE,GAAGC,wBAAwB,GAAGT,KAAAA;IAClD,MAAM,EAAEU,GAAG,EAAE,GAAGC,0BAAAA,EAAAA;IAEhB,IAAIC,MAAAA;AAEJ,IAAA,IAAIJ,EAAI,EAAA;QACNI,MAAS,GAAA;AACP,YAAA,GAAGH,sBAAsB;YACzBI,UAAY,EAAA;AACVC,gBAAAA,QAAAA,EAAU,CAAC;AACb,aAAA;AACAN,YAAAA;AACF,SAAA;KACK,MAAA;QACLI,MAAS,GAAA;AACP,YAAA,GAAGH,sBAAsB;YACzBI,UAAY,EAAA;AACVC,gBAAAA,QAAAA,EAAU,CAAC;AACb,aAAA;YACAC,OAAS,EAAA;gBACPC,IAAM,EAAA;uBACAP,sBAAwBM,EAAAA,OAAAA,EAASC,QAAQ,EAAE;AAC/C,oBAAA;wBACEC,MAAQ,EAAA;AACNC,4BAAAA,EAAAA,EAAIX,MAAU,IAAA;gCACZY,KAAO,EAAA;AACT;AACF;AACF;AACD;AACH;AACF,SAAA;AACF;IAEA,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE,GAAGC,mBAIjC,CAAA;AAACC,QAAAA,iBAAAA;AAAU,QAAA,SAAA;QAAWC,YAAUb,CAAAA,MAAAA;KAAQ,EACxC,UAAA;QACE,MAAM,EACJQ,MAAM,EAAEA,IAAI,EAAE,EACf,GAAG,MAAMV,GAAAA,CAAyB,iBAAmB,EAAA;AAAEE,YAAAA;AAAO,SAAA,CAAA;QAE/D,OAAOQ,IAAAA;KAET,EAAA;AACErB,QAAAA,OAAAA;QACA2B,SAAW,EAAA,CAAA;QACXC,SAAW,EAAA,CAAA;AACXC,QAAAA,OAAAA,CAAAA,GAAAA;YACEzB,kBAAmB,CAAA;gBACjB0B,IAAM,EAAA,QAAA;AACNC,gBAAAA,OAAAA,EAAS7B,aAAc,CAAA;oBAAEiB,EAAI,EAAA;AAAqB,iBAAA;AACpD,aAAA,CAAA;AACF;AACF,KAAA,CAAA;AAGFa,IAAAA,gBAAAA,CAAMC,SAAS,CAAC,IAAA;AACd,QAAA,IAAIZ,IAAM,EAAA;AACRf,YAAAA,YAAAA,CACEJ,aAAc,CAAA;gBACZiB,EAAI,EAAA,wBAAA;gBACJe,cAAgB,EAAA;AAClB,aAAA,CAAA,CAAA;AAEJ;KACC,EAAA;AAACb,QAAAA,IAAAA;AAAMnB,QAAAA,aAAAA;AAAeI,QAAAA;AAAa,KAAA,CAAA;IAEtC,OAAO;AAAEe,QAAAA,IAAAA;AAAMC,QAAAA,KAAAA;AAAOC,QAAAA;AAAU,KAAA;AAClC;;;;"}