import{createContext as e,useContext as r,useReducer as t,createElement as n,useEffect as o,useLayoutEffect as c,useRef as u,useState as s}from"react";import{unstable_runWithPriority as i,unstable_NormalPriority as p}from"scheduler";import{unstable_batchedUpdates as l}from"react-dom";const d=Symbol(),f=Symbol(),v="undefined"==typeof window||/ServerSideRendering/.test(window.navigator&&window.navigator.userAgent)?o:c,a=i?e=>i(p,e):e=>e(),w=e=>e;function E(r){const t=e({[d]:{v:{current:r},n:{current:-1},l:new Set,u:e=>e()}});var o;return t[f]=t.Provider,t.Provider=(o=t.Provider,({value:e,children:r})=>{const t=u(e),c=u(0),[i,p]=s(null);i&&(i(e),p(null));const f=u();if(!f.current){const e=new Set,r=(r,t)=>{l(()=>{c.current+=1;const n={n:c.current};null!=t&&t.suspense&&(n.n*=-1,n.p=new Promise(e=>{p(()=>r=>{n.v=r,delete n.p,e(r)})})),e.forEach(e=>e(n)),r()})};f.current={[d]:{v:t,n:c,l:e,u:r}}}return v(()=>{t.current=e,c.current+=1,a(()=>{f.current[d].l.forEach(r=>{r({n:c.current,v:e})})})},[e]),n(o,{value:f.current},r)}),delete t.Consumer,t}function h(e,n){const o=r(e)[d];if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!o)throw new Error("useContextSelector requires special context");const{v:{current:c},n:{current:u},l:s}=o,i=n(c),[p,l]=t((e,r)=>{if(!r)return[c,i];if("p"in r)throw r.p;if(r.n===u)return Object.is(e[1],i)?e:[c,i];try{if("v"in r){if(Object.is(e[0],r.v))return e;const t=n(r.v);return Object.is(e[1],t)?e:[r.v,t]}}catch(e){}return[...e]},[c,i]);return Object.is(p[1],i)||l(),v(()=>(s.add(l),()=>{s.delete(l)}),[s]),p[1]}function m(e){return h(e,w)}function b(e){const t=r(e)[d];if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!t)throw new Error("useContextUpdate requires special context");const{u:n}=t;return n}const x=({context:e,value:r,children:t})=>{const{[f]:o}=e;if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!o)throw new Error("BridgeProvider requires special context");return n(o,{value:r},t)},j=e=>{const t=r(e);if("object"==typeof process&&"production"!==process.env.NODE_ENV&&!t[d])throw new Error("useBridgeValue requires special context");return t};export{x as BridgeProvider,E as createContext,j as useBridgeValue,m as useContext,h as useContextSelector,b as useContextUpdate};
//# sourceMappingURL=index.modern.js.map
