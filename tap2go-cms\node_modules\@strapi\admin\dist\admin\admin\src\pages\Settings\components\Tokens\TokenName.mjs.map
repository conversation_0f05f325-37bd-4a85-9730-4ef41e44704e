{"version": 3, "file": "TokenName.mjs", "sources": ["../../../../../../../../admin/src/pages/Settings/components/Tokens/TokenName.tsx"], "sourcesContent": ["import { Field, TextInput, TextInputProps } from '@strapi/design-system';\nimport { MessageDescriptor, useIntl } from 'react-intl';\n\nimport { isErrorMessageMessageDescriptor } from '../../utils/forms';\n\ninterface TokenNameProps extends Pick<TextInputProps, 'onChange' | 'value'> {\n  error?: string | MessageDescriptor;\n  canEditInputs: boolean;\n}\n\nexport const TokenName = ({ error, value, onChange, canEditInputs }: TokenNameProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Field.Root\n      name=\"name\"\n      error={\n        error\n          ? formatMessage(\n              isErrorMessageMessageDescriptor(error) ? error : { id: error, defaultMessage: error }\n            )\n          : undefined\n      }\n      required\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'Settings.tokens.form.name',\n          defaultMessage: 'Name',\n        })}\n      </Field.Label>\n      <TextInput onChange={onChange} value={value} disabled={!canEditInputs} />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n"], "names": ["TokenName", "error", "value", "onChange", "canEditInputs", "formatMessage", "useIntl", "_jsxs", "Field", "Root", "name", "isErrorMessageMessageDescriptor", "id", "defaultMessage", "undefined", "required", "_jsx", "Label", "TextInput", "disabled", "Error"], "mappings": ";;;;;AAUO,MAAMA,SAAY,GAAA,CAAC,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAkB,GAAA;IACjF,MAAM,EAAEC,aAAa,EAAE,GAAGC,OAAAA,EAAAA;IAE1B,qBACEC,IAAA,CAACC,MAAMC,IAAI,EAAA;QACTC,IAAK,EAAA,MAAA;AACLT,QAAAA,KAAAA,EACEA,KACII,GAAAA,aAAAA,CACEM,+BAAgCV,CAAAA,KAAAA,CAAAA,GAASA,KAAQ,GAAA;YAAEW,EAAIX,EAAAA,KAAAA;YAAOY,cAAgBZ,EAAAA;SAEhFa,CAAAA,GAAAA,SAAAA;QAENC,QAAQ,EAAA,IAAA;;AAER,0BAAAC,GAAA,CAACR,MAAMS,KAAK,EAAA;0BACTZ,aAAc,CAAA;oBACbO,EAAI,EAAA,2BAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;;0BAEFG,GAACE,CAAAA,SAAAA,EAAAA;gBAAUf,QAAUA,EAAAA,QAAAA;gBAAUD,KAAOA,EAAAA,KAAAA;AAAOiB,gBAAAA,QAAAA,EAAU,CAACf;;AACxD,0BAAAY,GAAA,CAACR,MAAMY,KAAK,EAAA,EAAA;;;AAGlB;;;;"}