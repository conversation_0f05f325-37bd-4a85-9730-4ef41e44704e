{"version": 3, "file": "user.mjs", "sources": ["../../../../../server/src/domain/user.ts"], "sourcesContent": ["import constants from '../services/constants';\n\nimport type {\n  AdminUser,\n  AdminRole,\n  AdminUserCreationPayload,\n} from '../../../shared/contracts/shared';\n\nconst { SUPER_ADMIN_CODE } = constants;\n\n/**\n * Create a new user model by merging default and specified attributes\n * @param attributes A partial user object\n */\nexport function createUser(attributes: Partial<AdminUserCreationPayload>) {\n  return {\n    roles: [],\n    isActive: false,\n    username: null,\n    ...attributes,\n  };\n}\n\nexport const hasSuperAdminRole = (user: AdminUser) => {\n  return user.roles.filter((role: AdminRole) => role.code === SUPER_ADMIN_CODE).length > 0;\n};\n\nexport const ADMIN_USER_ALLOWED_FIELDS = ['id', 'firstname', 'lastname', 'username'];\n\nexport default {\n  createUser,\n  hasSuperAdminRole,\n  ADMIN_USER_ALLOWED_FIELDS,\n};\n"], "names": ["SUPER_ADMIN_CODE", "constants", "createUser", "attributes", "roles", "isActive", "username", "hasSuperAdminRole", "user", "filter", "role", "code", "length", "ADMIN_USER_ALLOWED_FIELDS"], "mappings": ";;AAQA,MAAM,EAAEA,gBAAgB,EAAE,GAAGC,SAAAA;AAE7B;;;IAIO,SAASC,UAAAA,CAAWC,UAA6C,EAAA;IACtE,OAAO;AACLC,QAAAA,KAAAA,EAAO,EAAE;QACTC,QAAU,EAAA,KAAA;QACVC,QAAU,EAAA,IAAA;AACV,QAAA,GAAGH;AACL,KAAA;AACF;AAEO,MAAMI,oBAAoB,CAACC,IAAAA,GAAAA;AAChC,IAAA,OAAOA,IAAKJ,CAAAA,KAAK,CAACK,MAAM,CAAC,CAACC,IAAoBA,GAAAA,IAAAA,CAAKC,IAAI,KAAKX,gBAAkBY,CAAAA,CAAAA,MAAM,GAAG,CAAA;AACzF;MAEaC,yBAA4B,GAAA;AAAC,IAAA,IAAA;AAAM,IAAA,WAAA;AAAa,IAAA,UAAA;AAAY,IAAA;;;;;"}