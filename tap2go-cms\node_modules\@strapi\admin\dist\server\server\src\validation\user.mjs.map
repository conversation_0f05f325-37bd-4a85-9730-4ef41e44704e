{"version": 3, "file": "user.mjs", "sources": ["../../../../../server/src/validation/user.ts"], "sourcesContent": ["import { isUndefined } from 'lodash/fp';\nimport { yup, validateYupSchema } from '@strapi/utils';\nimport validators from './common-validators';\n\nconst userCreationSchema = yup\n  .object()\n  .shape({\n    email: validators.email.required(),\n    firstname: validators.firstname.required(),\n    lastname: validators.lastname,\n    roles: validators.roles.min(1),\n    preferedLanguage: yup.string().nullable(),\n  })\n  .noUnknown();\n\nconst profileUpdateSchema = yup\n  .object()\n  .shape({\n    email: validators.email.notNull(),\n    firstname: validators.firstname.notNull(),\n    lastname: validators.lastname.nullable(),\n    username: validators.username.nullable(),\n    password: validators.password.notNull(),\n    currentPassword: yup\n      .string()\n      .when('password', (password: string, schema: any) =>\n        !isUndefined(password) ? schema.required() : schema\n      )\n      .notNull(),\n    preferedLanguage: yup.string().nullable(),\n  })\n  .noUnknown();\n\nconst userUpdateSchema = yup\n  .object()\n  .shape({\n    email: validators.email.notNull(),\n    firstname: validators.firstname.notNull(),\n    lastname: validators.lastname.nullable(),\n    username: validators.username.nullable(),\n    password: validators.password.notNull(),\n    isActive: yup.bool().notNull(),\n    roles: validators.roles.min(1).notNull(),\n  })\n  .noUnknown();\n\nconst usersDeleteSchema = yup\n  .object()\n  .shape({\n    ids: yup.array().of(yup.strapiID()).min(1).required(),\n  })\n  .noUnknown();\n\nexport const validateUserCreationInput = validateYupSchema(userCreationSchema);\nexport const validateProfileUpdateInput = validateYupSchema(profileUpdateSchema);\nexport const validateUserUpdateInput = validateYupSchema(userUpdateSchema);\nexport const validateUsersDeleteInput = validateYupSchema(usersDeleteSchema);\nexport const schemas = {\n  userCreationSchema,\n  usersDeleteSchema,\n  userUpdateSchema,\n};\n\nexport default {\n  validateUserCreationInput,\n  validateProfileUpdateInput,\n  validateUserUpdateInput,\n  validateUsersDeleteInput,\n  schemas,\n};\n"], "names": ["userCreationSchema", "yup", "object", "shape", "email", "validators", "required", "firstname", "lastname", "roles", "min", "preferedLanguage", "string", "nullable", "noUnknown", "profileUpdateSchema", "notNull", "username", "password", "currentPassword", "when", "schema", "isUndefined", "userUpdateSchema", "isActive", "bool", "usersDeleteSchema", "ids", "array", "of", "strapiID", "validateUserCreationInput", "validateYupSchema", "validateProfileUpdateInput", "validateUserUpdateInput", "validateUsersDeleteInput", "schemas"], "mappings": ";;;;AAIA,MAAMA,kBAAqBC,GAAAA,GAAAA,CACxBC,MAAM,EAAA,CACNC,KAAK,CAAC;IACLC,KAAOC,EAAAA,UAAAA,CAAWD,KAAK,CAACE,QAAQ,EAAA;IAChCC,SAAWF,EAAAA,UAAAA,CAAWE,SAAS,CAACD,QAAQ,EAAA;AACxCE,IAAAA,QAAAA,EAAUH,WAAWG,QAAQ;AAC7BC,IAAAA,KAAAA,EAAOJ,UAAWI,CAAAA,KAAK,CAACC,GAAG,CAAC,CAAA,CAAA;IAC5BC,gBAAkBV,EAAAA,GAAAA,CAAIW,MAAM,EAAA,CAAGC,QAAQ;AACzC,CAAA,CAAA,CACCC,SAAS,EAAA;AAEZ,MAAMC,mBAAsBd,GAAAA,GAAAA,CACzBC,MAAM,EAAA,CACNC,KAAK,CAAC;IACLC,KAAOC,EAAAA,UAAAA,CAAWD,KAAK,CAACY,OAAO,EAAA;IAC/BT,SAAWF,EAAAA,UAAAA,CAAWE,SAAS,CAACS,OAAO,EAAA;IACvCR,QAAUH,EAAAA,UAAAA,CAAWG,QAAQ,CAACK,QAAQ,EAAA;IACtCI,QAAUZ,EAAAA,UAAAA,CAAWY,QAAQ,CAACJ,QAAQ,EAAA;IACtCK,QAAUb,EAAAA,UAAAA,CAAWa,QAAQ,CAACF,OAAO,EAAA;AACrCG,IAAAA,eAAAA,EAAiBlB,IACdW,MAAM,EAAA,CACNQ,IAAI,CAAC,YAAY,CAACF,QAAAA,EAAkBG,MACnC,GAAA,CAACC,YAAYJ,QAAYG,CAAAA,GAAAA,MAAAA,CAAOf,QAAQ,EAAA,GAAKe,QAE9CL,OAAO,EAAA;IACVL,gBAAkBV,EAAAA,GAAAA,CAAIW,MAAM,EAAA,CAAGC,QAAQ;AACzC,CAAA,CAAA,CACCC,SAAS,EAAA;AAEZ,MAAMS,gBAAmBtB,GAAAA,GAAAA,CACtBC,MAAM,EAAA,CACNC,KAAK,CAAC;IACLC,KAAOC,EAAAA,UAAAA,CAAWD,KAAK,CAACY,OAAO,EAAA;IAC/BT,SAAWF,EAAAA,UAAAA,CAAWE,SAAS,CAACS,OAAO,EAAA;IACvCR,QAAUH,EAAAA,UAAAA,CAAWG,QAAQ,CAACK,QAAQ,EAAA;IACtCI,QAAUZ,EAAAA,UAAAA,CAAWY,QAAQ,CAACJ,QAAQ,EAAA;IACtCK,QAAUb,EAAAA,UAAAA,CAAWa,QAAQ,CAACF,OAAO,EAAA;IACrCQ,QAAUvB,EAAAA,GAAAA,CAAIwB,IAAI,EAAA,CAAGT,OAAO,EAAA;AAC5BP,IAAAA,KAAAA,EAAOJ,WAAWI,KAAK,CAACC,GAAG,CAAC,GAAGM,OAAO;AACxC,CAAA,CAAA,CACCF,SAAS,EAAA;AAEZ,MAAMY,iBAAoBzB,GAAAA,GAAAA,CACvBC,MAAM,EAAA,CACNC,KAAK,CAAC;IACLwB,GAAK1B,EAAAA,GAAAA,CAAI2B,KAAK,EAAA,CAAGC,EAAE,CAAC5B,GAAI6B,CAAAA,QAAQ,EAAIpB,CAAAA,CAAAA,GAAG,CAAC,CAAA,CAAA,CAAGJ,QAAQ;AACrD,CAAA,CAAA,CACCQ,SAAS,EAAA;AAEL,MAAMiB,yBAA4BC,GAAAA,iBAAAA,CAAkBhC,kBAAoB;AACxE,MAAMiC,0BAA6BD,GAAAA,iBAAAA,CAAkBjB,mBAAqB;AAC1E,MAAMmB,uBAA0BF,GAAAA,iBAAAA,CAAkBT,gBAAkB;AACpE,MAAMY,wBAA2BH,GAAAA,iBAAAA,CAAkBN,iBAAmB;MAChEU,OAAU,GAAA;AACrBpC,IAAAA,kBAAAA;AACA0B,IAAAA,iBAAAA;AACAH,IAAAA;AACF;;;;"}