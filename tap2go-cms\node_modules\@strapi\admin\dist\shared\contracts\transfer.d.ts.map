{"version": 3, "file": "transfer.d.ts", "sourceRoot": "", "sources": ["../../../shared/contracts/transfer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAEvC,MAAM,WAAW,uBAAuB;IACtC,EAAE,EAAE,MAAM,GAAG,GAAG,MAAM,EAAE,CAAC;IACzB,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;IACtC,KAAK,EAAE,aAAa,GAAG,MAAM,CAAC;CAC/B;AAED,MAAM,WAAW,qBAAqB;IACpC,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IACjC,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,uBAAuB,EAAE,CAAC;CACxC;AAED,MAAM,WAAW,aAAc,SAAQ,IAAI,CAAC,qBAAqB,EAAE,aAAa,CAAC;IAC/E,WAAW,EAAE,KAAK,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;CACvD;AAED,MAAM,MAAM,sBAAsB,GAAG,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAEtE,MAAM,MAAM,kBAAkB,GAAG,IAAI,CACnC,aAAa,EACb,MAAM,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,GAAG,UAAU,CACnE,GAAG;IAAE,SAAS,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAE3B;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,UAAiB,OAAO;QACtB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,iBAAiB,CAAC;KAC5D;CACF;AACD;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,UAAiB,OAAO;QACtB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,iBAAiB,CAAC;KAC5D;CACF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC,UAAiB,OAAO;QACtB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,sBAAsB,EAAE,CAAC;QAC/B,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC;KACjC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,UAAiB,OAAO;QACtB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,MAAM;QACrB,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;KACrB;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,sBAAsB,CAAC;QAC7B,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC;KACjC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,UAAiB,OAAO;QACtB,IAAI,EAAE,kBAAkB,CAAC;QACzB,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,aAAa,CAAC;QACpB,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC;KAC7D;CACF;AAED,MAAM,WAAW,kBACf,SAAQ,IAAI,CAAC,kBAAkB,EAAE,MAAM,GAAG,aAAa,CAAC,EACtD,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,GAAG,aAAa,CAAC,CAAC;CAAG;AAEhE;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,UAAiB,OAAO;QACtB,IAAI,EAAE,kBAAkB,CAAC;QACzB,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,MAAM;QACrB,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;KACrB;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,sBAAsB,CAAC;QAC7B,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC;KACjC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,UAAiB,OAAO;QACtB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,MAAM;QACrB,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;KACrB;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,sBAAsB,CAAC;QAC7B,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC;KACjC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,eAAe,CAAC;IACvC,UAAiB,OAAO;QACtB,IAAI,EAAE,EAAE,CAAC;QACT,KAAK,EAAE,EAAE,CAAC;KACX;IAED,UAAiB,MAAM;QACrB,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;KACrB;IAED,UAAiB,QAAQ;QACvB,IAAI,EAAE,aAAa,CAAC;QACpB,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC;KACjC;CACF"}