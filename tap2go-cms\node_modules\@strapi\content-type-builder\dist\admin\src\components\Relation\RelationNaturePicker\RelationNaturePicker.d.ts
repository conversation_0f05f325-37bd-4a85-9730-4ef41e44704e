interface RelationNaturePickerProps {
    naturePickerType: string;
    oneThatIsCreatingARelationWithAnother: string;
    relationType: string;
    target: string;
    targetUid: string;
}
export declare const RelationNaturePicker: ({ naturePickerType, oneThatIsCreatingARelationWithAnother, relationType, target, targetUid, }: RelationNaturePickerProps) => import("react/jsx-runtime").JSX.Element | null;
export {};
