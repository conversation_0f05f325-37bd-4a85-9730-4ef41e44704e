{"version": 3, "file": "core.js", "sourceRoot": "", "sources": ["../src/core.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,OAAO,EACL,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,EAEhB,mBAAmB,EACnB,mBAAmB,EAEnB,eAAe,EACf,eAAe,EACf,SAAS,EAIT,gBAAgB,EAChB,aAAa,GAEd,MAAM,sBAAsB,CAAC;AAmD9B,SAAS,UAAU,CAAC,MAAc,EAAE,UAAkC;IACpE,IAAM,eAAe,GAAG,kBAAkB,CACxC,MAAM,EACN,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,aAAa,CACzB,CAAC;IACF,IAAM,WAAW,GAAG,eAAe;SAChC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC;SAC5B,MAAM,CAAC,OAAO,CAAC,CAAC;IACnB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QACvB,MAAM,IAAI,KAAK,CACb,+BAA4B,MAAM,8BAAwB,eAAe,CAAC,IAAI,CAC5E,IAAI,CACH,CACJ,CAAC;KACH;IACD,WAAW,CAAC,OAAO,EAAE,CAAC;IACtB,OAAO,WAAW,CAAC,MAAM,CACvB,UAAC,GAA6B,EAAE,CAAC,IAAK,OAAA,uBACjC,GAAG,GACH,CAAC,EACJ,EAHoC,CAGpC,EACF,EAAC,EAAE,EAAE,EAAE,EAAC,CACT,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,wBAAwB,CAAC,IAAqB;IACrD,SAAS,CACP,OAAO,IAAI,KAAK,QAAQ,EACxB,wCAAsC,OAAO,IAAM,EACnD,SAAS,CACV,CAAC;IACF,IAAI,IAAI,KAAK,SAAS;QAAE,OAAO,QAAQ,CAAC;IACxC,IAAI,IAAI,KAAK,SAAS;QAAE,OAAO,QAAQ,CAAC;IACxC,IAAI,IAAI,KAAK,OAAO;QAAE,OAAO,MAAM,CAAC;IACpC,IAAI,IAAI,KAAK,MAAM;QAAE,OAAO,KAAK,CAAC;IAClC,IAAI,IAAI,KAAK,OAAO;QAAE,OAAO,MAAM,CAAC;IACpC,IAAI,IAAI,KAAK,QAAQ;QAAE,OAAO,OAAO,CAAC;IACtC,IAAI,IAAI,KAAK,UAAU;QAAE,OAAO,SAAS,CAAC;IAC1C,IAAI,IAAI,KAAK,OAAO;QAAE,OAAO,MAAM,CAAC;IACpC,IACE,IAAI,KAAK,QAAQ;QACjB,IAAI,KAAK,QAAQ;QACjB,IAAI,KAAK,MAAM;QACf,IAAI,KAAK,KAAK;QACd,IAAI,KAAK,MAAM;QACf,IAAI,KAAK,OAAO;QAChB,IAAI,KAAK,SAAS;QAClB,IAAI,KAAK,MAAM,EACf;QACA,MAAM,IAAI,UAAU,CAAC,kBAAgB,IAAM,CAAC,CAAC;KAC9C;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAM,sBAAsB,GAAG,mCAAmC,CAAC;AAanE;;;;;GAKG;AACH,SAAS,aAAa,CACpB,OAAe,EACf,IAAU,EACV,KAA8B;;IAE9B,IAAM,YAAY,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC/C,IAAM,MAAM,GAAW,EAAE,CAAC;;QAC1B,KAA0B,IAAA,iBAAA,SAAA,YAAY,CAAA,0CAAA,oEAAE;YAAnC,IAAM,WAAW,yBAAA;YACpB,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE;gBAC9B,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,WAAW,CAAC,KAAK;iBACzB,CAAC,CAAC;aACJ;iBAAM;gBACL,SAAS,CAAC,WAAW,CAAC,IAAI,KAAK,GAAG,EAAE,uBAAqB,OAAS,CAAC,CAAC;;oBACpE,KAAmB,IAAA,yBAAA,SAAA,KAAK,CAAA,CAAA,4BAAA,+CAAE;wBAArB,IAAM,IAAI,kBAAA;wBACb,MAAM,CAAC,IAAI,CAAC;4BACV,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,IAAI,MAAA;yBACL,CAAC,CAAC;qBACJ;;;;;;;;;aACF;SACF;;;;;;;;;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,QAAQ,CAAC,CAAM,EAAE,CAAM;IAC9B,IAAI,MAAM,CAAC,EAAE,EAAE;QACb,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACxB;IACD,sBAAsB;IACtB,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,kBAAkB;QAClB,0BAA0B;QAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACnC;IACD,uBAAuB;IACvB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,QAAQ,CAAC,GAAQ;IACxB,OAAO,GAAG,GAAG,EAAE,CAAC;AAClB,CAAC;AAED;;;;;GAKG;AACH,SAAS,4BAA4B,CACnC,eAAwE,EACxE,GAAuB,EACvB,KAAa,EACb,IAAqB;IAErB,SAAS,CACP,OAAO,KAAK,KAAK,QAAQ,EACzB,uCAAqC,OAAO,KAAO,EACnD,SAAS,CACV,CAAC;IACF,SAAS,CACP,OAAO,IAAI,KAAK,QAAQ,EACxB,sCAAoC,OAAO,KAAO,EAClD,SAAS,CACV,CAAC;IACF,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,CAAC,QAAQ,EAAE;QAC7D,MAAM,IAAI,UAAU,CAAC,mBAAiB,KAAO,CAAC,CAAC;KAChD;IACD,IAAM,YAAY,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAM,MAAM,GAAG,eAAe,CAAC,eAAe,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC/D,IAAM,KAAK,GAAG,eAAe,CAAC,eAAe,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAC7D,IAAI,KAAK,GAAsB,YAAY,CAAC;IAC5C,IAAI,KAAK,KAAK,OAAO,EAAE;QACrB,KAAK,GAAM,IAAI,WAA6B,CAAC;KAC9C;SAAM,IAAI,KAAK,KAAK,QAAQ,EAAE;QAC7B,KAAK,GAAM,IAAI,YAA8B,CAAC;KAC/C;IACD,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,EAAE;QACtB,KAAK,GAAG,IAAyB,CAAC;KACnC;IACD,IAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAE,CAAC;IAChC,IAAM,OAAO,GAAG,eAAe,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;IACjE,IAAI,OAAO,KAAK,MAAM,EAAE;QACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,EAAE;YAC/B,OAAO;gBACL;oBACE,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAS,CAAE;iBAC1C;aACF,CAAC;SACH;KACF;IACD,IAAI,EAAE,GAAoB,QAAQ,CAAC;IACnC,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;QACpC,EAAE,GAAG,MAAM,CAAC;KACb;IACD,IAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxB,IAAM,WAAW,GAAG,eAAe,CAAC,eAAe,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;IACzE,IAAM,YAAY,GAAG,eAAe,CAAC,eAAe,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAC3E,IAAM,EAAE,GACN,OAAO,YAAY,CAAC,aAAa,KAAK,UAAU;QAC9C,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,yEAAyE;YACzE,mBAAmB;YACnB;gBACE;oBACE,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC3C,IAAI,MAAA;iBAC2B;aAClC,CAAC;IACR,IAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAmB,CAAC;IACvD,IAAM,OAAO,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACvB,OAAO,aAAa,CAAC,OAAQ,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC;AAED;IACE,4BACE,OAA2B,EAC3B,OAAuC;QAEvC,yFAAyF;QACzF,0EAA0E;QAC1E,IAAM,SAAS,GACb,IAAI,IAAI,IAAI,YAAY,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;QACD,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,+BAA+B,EAC/B,IAAI,CACL,CAAC;QACF,IAAM,gBAAgB,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACtD,IAAM,GAAG,GAAQ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrC,IAAM,IAAI,GACR,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAM,OAAO,GAAG,SAAS,CACvB,IAAI,EACJ,eAAe,EACf,QAAQ,EACR,CAAC,UAAU,EAAE,QAAQ,CAAC,EACtB,UAAU,CACX,CAAC;QACF,GAAG,CAAC,aAAa,GAAG,OAAO,CAAC;QAC5B,IAAM,eAAe,GAAW,SAAS,CACvC,IAAI,EACJ,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,SAAS,CACV,CAAC;QACF,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBACjD,MAAM,IAAI,UAAU,CAAC,8BAA4B,eAAiB,CAAC,CAAC;aACrE;SACF;QACD,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC;QACzB,IAAM,CAAC,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAChE,kBAAkB,CAAC,gBAAgB,EACnC,gBAAgB,EAChB,GAAG,EACH,kBAAkB,CAAC,qBAAqB,EACxC,kBAAkB,CAAC,UAAU,CAC9B,CAAC;QACK,IAAA,iBAAM,EAAE,SAAE,CAAM;QACvB,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,QAAQ,EACR,MAAM,CACP,CAAC;QACF,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,OAAO,EACP,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,CACxE,CAAC;QACF,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,SAAS,EACT,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,CACnE,CAAC;QACF,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,QAAQ,EACR,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,CACtC,CAAC;QACF,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,cAAc,EACd,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAC/B,CAAC;QACF,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,aAAa,EACb,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAC9B,CAAC;QACF,eAAe,CACb,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,iBAAiB,EACjB,EAAE,CACH,CAAC;IACJ,CAAC;IACD,mCAAM,GAAN,UAAO,KAAa,EAAE,IAAqB;QACzC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;SAC1D;QACD,IACE,CAAC,eAAe,CACd,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,+BAA+B,CAChC,EACD;YACA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;SAC/D;QACD,OAAO,4BAA4B,CACjC,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,MAAM,CAAC,KAAK,CAAC,EACb,QAAQ,CAAC,IAAI,CAAoB,CAClC;aACE,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,KAAK,EAAR,CAAQ,CAAC;aACnB,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC;IACD,0CAAa,GAAb,UAAc,KAAa,EAAE,IAAqB;QAChD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;SACjE;QACD,IACE,CAAC,eAAe,CACd,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,+BAA+B,CAChC,EACD;YACA,MAAM,IAAI,SAAS,CAAC,+CAA+C,CAAC,CAAC;SACtE;QACD,OAAO,4BAA4B,CACjC,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,MAAM,CAAC,KAAK,CAAC,EACb,QAAQ,CAAC,IAAI,CAAoB,CAClC,CAAC;IACJ,CAAC;IAED,4CAAe,GAAf;QACE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;SACnE;QACD,IACE,CAAC,eAAe,CACd,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,+BAA+B,CAChC,EACD;YACA,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC;SACxE;QAED,4EAA4E;QAC5E,OAAO;YACL,MAAM,EAAE,eAAe,CACrB,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,QAAQ,CACT;YACD,KAAK,EAAE,eAAe,CACpB,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,OAAO,CACR;YACD,OAAO,EAAE,eAAe,CACtB,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,SAAS,CACV;YACD,eAAe,EAAE,eAAe,CAC9B,kBAAkB,CAAC,qBAAqB,EACxC,IAAI,EACJ,iBAAiB,CAClB;SACF,CAAC;IACJ,CAAC;IAEa,qCAAkB,GAAhC,UACE,OAA0B,EAC1B,OAA8D;QAE9D,OAAO,gBAAgB,CACrB,kBAAkB,CAAC,gBAAgB,EACnC,mBAAmB,CAAC,OAAO,CAAC,EAC5B,OAAO,CACR,CAAC;IACJ,CAAC;IAEa,kCAAe,GAA7B;;QAA8B,cAAiC;aAAjC,UAAiC,EAAjC,qBAAiC,EAAjC,IAAiC;YAAjC,yBAAiC;;gCAClD,KAAK;YACd,IAAM,gBAAgB,GAAa,MAAM,CAAC,IAAI,CAC5C,SACK,KAAK,CAAC,gBAAgB,EACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EACnC,MAAM,CAAC,UAAC,GAAyB,EAAE,CAAC;gBACpC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBACd,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CACP,CAAC;YACF,gBAAgB,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC7B,IAAI;oBACF,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;iBACnE;gBAAC,OAAO,CAAC,EAAE;oBACV,kDAAkD;iBACnD;YACH,CAAC,CAAC,CAAC;;;YAjBL,KAAoB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA;gBAAnB,IAAM,KAAK,iBAAA;wBAAL,KAAK;aAkBf;;;;;;;;;QACD,kBAAkB,CAAC,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAC/C,kBAAkB,CAAC,UAAU,CAC9B,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE;YACvC,kBAAkB,CAAC,eAAe;gBAChC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC1C;IACH,CAAC;IAIc,mCAAgB,GAA/B;QACE,OAAO,kBAAkB,CAAC,eAAe,CAAC;IAC5C,CAAC;IALM,6BAAU,GAA6C,EAAE,CAAC;IAClD,mCAAgB,GAAa,EAAE,CAAC;IAChC,kCAAe,GAAG,IAAI,CAAC;IAIvB,wCAAqB,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,6BAAU,GAAG,IAAI,CAAC;IACR,wCAAqB,GAAG,IAAI,OAAO,EAGxD,CAAC;IACN,yBAAC;CAAA,AAnOD,IAmOC;eAnOoB,kBAAkB;AAqOvC,IAAI;IACF,4BAA4B;IAC5B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE;YACtE,KAAK,EAAE,yBAAyB;YAChC,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;KACJ;IAED,oGAAoG;IACpG,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,EAAE,QAAQ,EAAE;QACxE,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,KAAK;QACf,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;IACH,uHAAuH;IACvH,MAAM,CAAC,cAAc,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,QAAQ,EAAE;QACrE,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,KAAK;QACf,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAAC,OAAO,CAAC,EAAE;IACV,qDAAqD;CACtD"}