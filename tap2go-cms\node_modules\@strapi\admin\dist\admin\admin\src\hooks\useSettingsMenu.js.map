{"version": 3, "file": "useSettingsMenu.js", "sources": ["../../../../../admin/src/hooks/useSettingsMenu.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport sortBy from 'lodash/sortBy';\nimport { useSelector } from 'react-redux';\n\nimport { SETTINGS_LINKS_CE, SettingsMenuLink } from '../constants';\nimport { useAppInfo } from '../features/AppInfo';\nimport { useAuth } from '../features/Auth';\nimport { useStrapiApp } from '../features/StrapiApp';\nimport { selectAdminPermissions } from '../selectors';\nimport { PermissionMap } from '../types/permissions';\n\nimport { useEnterprise } from './useEnterprise';\n\nimport type {\n  StrapiAppSetting,\n  StrapiAppSettingLink as IStrapiAppSettingLink,\n} from '../core/apis/router';\n\nconst formatLinks = (menu: SettingsMenuSection[]): SettingsMenuSectionWithDisplayedLinks[] =>\n  menu.map((menuSection) => {\n    const formattedLinks = menuSection.links.map((link) => ({\n      ...link,\n      isDisplayed: false,\n    }));\n\n    return { ...menuSection, links: formattedLinks };\n  });\n\ninterface SettingsMenuLinkWithPermissions extends SettingsMenuLink {\n  permissions: IStrapiAppSettingLink['permissions'];\n  hasNotification?: boolean;\n}\n\ninterface StrapiAppSettingsLink extends IStrapiAppSettingLink {\n  licenseOnly?: never;\n  hasNotification?: never;\n}\n\ninterface SettingsMenuSection extends Omit<StrapiAppSetting, 'links'> {\n  links: Array<SettingsMenuLinkWithPermissions | StrapiAppSettingsLink>;\n}\n\ninterface SettingsMenuLinkWithPermissionsAndDisplayed extends SettingsMenuLinkWithPermissions {\n  isDisplayed: boolean;\n}\n\ninterface StrapiAppSettingLinkWithDisplayed extends StrapiAppSettingsLink {\n  isDisplayed: boolean;\n}\n\ninterface SettingsMenuSectionWithDisplayedLinks extends Omit<SettingsMenuSection, 'links'> {\n  links: Array<SettingsMenuLinkWithPermissionsAndDisplayed | StrapiAppSettingLinkWithDisplayed>;\n}\n\ntype SettingsMenu = SettingsMenuSectionWithDisplayedLinks[];\n\nconst useSettingsMenu = (): {\n  isLoading: boolean;\n  menu: SettingsMenu;\n} => {\n  const [{ isLoading, menu }, setData] = React.useState<{\n    isLoading: boolean;\n    menu: SettingsMenu;\n  }>({\n    isLoading: true,\n    menu: [],\n  });\n  const checkUserHasPermission = useAuth(\n    'useSettingsMenu',\n    (state) => state.checkUserHasPermissions\n  );\n  const shouldUpdateStrapi = useAppInfo('useSettingsMenu', (state) => state.shouldUpdateStrapi);\n  const settings = useStrapiApp('useSettingsMenu', (state) => state.settings);\n  const permissions = useSelector(selectAdminPermissions);\n\n  /**\n   * memoize the return value of this function to avoid re-computing it on every render\n   * because it's used in an effect it ends up re-running recursively.\n   */\n  const ceLinks = React.useMemo(() => SETTINGS_LINKS_CE(), []);\n\n  const { admin: adminLinks, global: globalLinks } = useEnterprise(\n    ceLinks,\n    async () => (await import('../../../ee/admin/src/constants')).SETTINGS_LINKS_EE(),\n    {\n      combine(ceLinks, eeLinks) {\n        return {\n          admin: [...eeLinks.admin, ...ceLinks.admin],\n          global: [...ceLinks.global, ...eeLinks.global],\n        };\n      },\n      defaultValue: {\n        admin: [],\n        global: [],\n      },\n    }\n  );\n\n  const addPermissions = React.useCallback(\n    (link: SettingsMenuLink) => {\n      if (!link.id) {\n        throw new Error('The settings menu item must have an id attribute.');\n      }\n\n      return {\n        ...link,\n        permissions: permissions.settings?.[link.id as keyof PermissionMap['settings']]?.main ?? [],\n      } satisfies SettingsMenuLinkWithPermissions;\n    },\n    [permissions.settings]\n  );\n\n  React.useEffect(() => {\n    const getData = async () => {\n      interface MenuLinkPermission {\n        hasPermission: boolean;\n        sectionIndex: number;\n        linkIndex: number;\n      }\n\n      const buildMenuPermissions = (sections: SettingsMenuSectionWithDisplayedLinks[]) =>\n        Promise.all(\n          sections.reduce<Promise<MenuLinkPermission>[]>((acc, section, sectionIndex) => {\n            const linksWithPermissions = section.links.map(async (link, linkIndex) => ({\n              hasPermission: (await checkUserHasPermission(link.permissions)).length > 0,\n              sectionIndex,\n              linkIndex,\n            }));\n\n            return [...acc, ...linksWithPermissions];\n          }, [])\n        );\n\n      const menuPermissions = await buildMenuPermissions(sections);\n\n      setData((prev) => {\n        return {\n          ...prev,\n          isLoading: false,\n          menu: sections.map((section, sectionIndex) => ({\n            ...section,\n            links: section.links.map((link, linkIndex) => {\n              const permission = menuPermissions.find(\n                (permission) =>\n                  permission.sectionIndex === sectionIndex && permission.linkIndex === linkIndex\n              );\n\n              return {\n                ...link,\n                isDisplayed: Boolean(permission?.hasPermission),\n              };\n            }),\n          })),\n        };\n      });\n    };\n\n    const { global, ...otherSections } = settings;\n    const sections = formatLinks([\n      {\n        ...global,\n        links: sortBy([...global.links, ...globalLinks.map(addPermissions)], (link) => link.id).map(\n          (link) => ({\n            ...link,\n            hasNotification: link.id === '000-application-infos' && shouldUpdateStrapi,\n          })\n        ),\n      },\n      {\n        id: 'permissions',\n        intlLabel: { id: 'Settings.permissions', defaultMessage: 'Administration Panel' },\n        links: adminLinks.map(addPermissions),\n      },\n      ...Object.values(otherSections),\n    ]);\n\n    getData();\n  }, [\n    adminLinks,\n    globalLinks,\n    settings,\n    shouldUpdateStrapi,\n    addPermissions,\n    checkUserHasPermission,\n  ]);\n\n  return {\n    isLoading,\n    menu: menu.map((menuItem) => ({\n      ...menuItem,\n      links: menuItem.links.filter((link) => link.isDisplayed),\n    })),\n  };\n};\n\nexport { useSettingsMenu };\nexport type { SettingsMenu };\n"], "names": ["formatLinks", "menu", "map", "menuSection", "formattedLinks", "links", "link", "isDisplayed", "useSettingsMenu", "isLoading", "setData", "React", "useState", "checkUserHasPermission", "useAuth", "state", "checkUserHasPermissions", "shouldUpdateStrapi", "useAppInfo", "settings", "useStrapiApp", "permissions", "useSelector", "selectAdminPermissions", "ceLinks", "useMemo", "SETTINGS_LINKS_CE", "admin", "adminLinks", "global", "globalLinks", "useEnterprise", "SETTINGS_LINKS_EE", "combine", "eeLinks", "defaultValue", "addPermissions", "useCallback", "id", "Error", "main", "useEffect", "getData", "buildMenuPermissions", "sections", "Promise", "all", "reduce", "acc", "section", "sectionIndex", "linksWithPermissions", "linkIndex", "hasPermission", "length", "menuPermissions", "prev", "permission", "find", "Boolean", "otherSections", "sortBy", "hasNotification", "intlLabel", "defaultMessage", "Object", "values", "menuItem", "filter"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,MAAMA,cAAc,CAACC,IAAAA,GACnBA,IAAKC,CAAAA,GAAG,CAAC,CAACC,WAAAA,GAAAA;QACR,MAAMC,cAAAA,GAAiBD,YAAYE,KAAK,CAACH,GAAG,CAAC,CAACI,QAAU;AACtD,gBAAA,GAAGA,IAAI;gBACPC,WAAa,EAAA;aACf,CAAA,CAAA;QAEA,OAAO;AAAE,YAAA,GAAGJ,WAAW;YAAEE,KAAOD,EAAAA;AAAe,SAAA;AACjD,KAAA,CAAA;AA8BF,MAAMI,eAAkB,GAAA,IAAA;IAItB,MAAM,CAAC,EAAEC,SAAS,EAAER,IAAI,EAAE,EAAES,OAAQ,CAAA,GAAGC,gBAAMC,CAAAA,QAAQ,CAGlD;QACDH,SAAW,EAAA,IAAA;AACXR,QAAAA,IAAAA,EAAM;AACR,KAAA,CAAA;AACA,IAAA,MAAMY,yBAAyBC,YAC7B,CAAA,iBAAA,EACA,CAACC,KAAAA,GAAUA,MAAMC,uBAAuB,CAAA;AAE1C,IAAA,MAAMC,qBAAqBC,kBAAW,CAAA,iBAAA,EAAmB,CAACH,KAAAA,GAAUA,MAAME,kBAAkB,CAAA;AAC5F,IAAA,MAAME,WAAWC,sBAAa,CAAA,iBAAA,EAAmB,CAACL,KAAAA,GAAUA,MAAMI,QAAQ,CAAA;AAC1E,IAAA,MAAME,cAAcC,sBAAYC,CAAAA,gCAAAA,CAAAA;AAEhC;;;AAGC,MACD,MAAMC,OAAUb,GAAAA,gBAAAA,CAAMc,OAAO,CAAC,IAAMC,+BAAqB,EAAE,CAAA;AAE3D,IAAA,MAAM,EAAEC,KAAOC,EAAAA,UAAU,EAAEC,MAAQC,EAAAA,WAAW,EAAE,GAAGC,2BAAAA,CACjDP,SACA,UAAa,CAAA,MAAM,oDAAO,oCAAiC,KAAA,EAAGQ,iBAAiB,EAC/E,EAAA;QACEC,OAAQT,CAAAA,CAAAA,OAAO,EAAEU,OAAO,EAAA;YACtB,OAAO;gBACLP,KAAO,EAAA;AAAIO,oBAAAA,GAAAA,OAAAA,CAAQP,KAAK;AAAKH,oBAAAA,GAAAA,OAAAA,CAAQG;AAAM,iBAAA;gBAC3CE,MAAQ,EAAA;AAAIL,oBAAAA,GAAAA,OAAAA,CAAQK,MAAM;AAAKK,oBAAAA,GAAAA,OAAAA,CAAQL;AAAO;AAChD,aAAA;AACF,SAAA;QACAM,YAAc,EAAA;AACZR,YAAAA,KAAAA,EAAO,EAAE;AACTE,YAAAA,MAAAA,EAAQ;AACV;AACF,KAAA,CAAA;AAGF,IAAA,MAAMO,cAAiBzB,GAAAA,gBAAAA,CAAM0B,WAAW,CACtC,CAAC/B,IAAAA,GAAAA;QACC,IAAI,CAACA,IAAKgC,CAAAA,EAAE,EAAE;AACZ,YAAA,MAAM,IAAIC,KAAM,CAAA,mDAAA,CAAA;AAClB;QAEA,OAAO;AACL,YAAA,GAAGjC,IAAI;YACPe,WAAaA,EAAAA,WAAAA,CAAYF,QAAQ,GAAGb,KAAKgC,EAAE,CAAoC,EAAEE,IAAAA,IAAQ;AAC3F,SAAA;KAEF,EAAA;AAACnB,QAAAA,WAAAA,CAAYF;AAAS,KAAA,CAAA;AAGxBR,IAAAA,gBAAAA,CAAM8B,SAAS,CAAC,IAAA;AACd,QAAA,MAAMC,OAAU,GAAA,UAAA;YAOd,MAAMC,oBAAAA,GAAuB,CAACC,QAAAA,GAC5BC,OAAQC,CAAAA,GAAG,CACTF,QAAAA,CAASG,MAAM,CAAgC,CAACC,GAAAA,EAAKC,OAASC,EAAAA,YAAAA,GAAAA;oBAC5D,MAAMC,oBAAAA,GAAuBF,QAAQ5C,KAAK,CAACH,GAAG,CAAC,OAAOI,IAAM8C,EAAAA,SAAAA,IAAe;4BACzEC,aAAe,EAAC,CAAA,MAAMxC,sBAAAA,CAAuBP,KAAKe,WAAW,CAAA,EAAGiC,MAAM,GAAG,CAAA;AACzEJ,4BAAAA,YAAAA;AACAE,4BAAAA;yBACF,CAAA,CAAA;oBAEA,OAAO;AAAIJ,wBAAAA,GAAAA,GAAAA;AAAQG,wBAAAA,GAAAA;AAAqB,qBAAA;AAC1C,iBAAA,EAAG,EAAE,CAAA,CAAA;YAGT,MAAMI,eAAAA,GAAkB,MAAMZ,oBAAqBC,CAAAA,QAAAA,CAAAA;AAEnDlC,YAAAA,OAAAA,CAAQ,CAAC8C,IAAAA,GAAAA;gBACP,OAAO;AACL,oBAAA,GAAGA,IAAI;oBACP/C,SAAW,EAAA,KAAA;AACXR,oBAAAA,IAAAA,EAAM2C,SAAS1C,GAAG,CAAC,CAAC+C,OAAAA,EAASC,gBAAkB;AAC7C,4BAAA,GAAGD,OAAO;AACV5C,4BAAAA,KAAAA,EAAO4C,QAAQ5C,KAAK,CAACH,GAAG,CAAC,CAACI,IAAM8C,EAAAA,SAAAA,GAAAA;AAC9B,gCAAA,MAAMK,UAAaF,GAAAA,eAAAA,CAAgBG,IAAI,CACrC,CAACD,UAAAA,GACCA,UAAWP,CAAAA,YAAY,KAAKA,YAAAA,IAAgBO,UAAWL,CAAAA,SAAS,KAAKA,SAAAA,CAAAA;gCAGzE,OAAO;AACL,oCAAA,GAAG9C,IAAI;AACPC,oCAAAA,WAAAA,EAAaoD,QAAQF,UAAYJ,EAAAA,aAAAA;AACnC,iCAAA;AACF,6BAAA;yBACF,CAAA;AACF,iBAAA;AACF,aAAA,CAAA;AACF,SAAA;AAEA,QAAA,MAAM,EAAExB,MAAM,EAAE,GAAG+B,eAAe,GAAGzC,QAAAA;AACrC,QAAA,MAAMyB,WAAW5C,WAAY,CAAA;AAC3B,YAAA;AACE,gBAAA,GAAG6B,MAAM;AACTxB,gBAAAA,KAAAA,EAAOwD,MAAO,CAAA;AAAIhC,oBAAAA,GAAAA,MAAAA,CAAOxB,KAAK;AAAKyB,oBAAAA,GAAAA,WAAAA,CAAY5B,GAAG,CAACkC,cAAAA;iBAAgB,EAAE,CAAC9B,OAASA,IAAKgC,CAAAA,EAAE,EAAEpC,GAAG,CACzF,CAACI,IAAAA,IAAU;AACT,wBAAA,GAAGA,IAAI;wBACPwD,eAAiBxD,EAAAA,IAAAA,CAAKgC,EAAE,KAAK,uBAA2BrB,IAAAA;qBAC1D,CAAA;AAEJ,aAAA;AACA,YAAA;gBACEqB,EAAI,EAAA,aAAA;gBACJyB,SAAW,EAAA;oBAAEzB,EAAI,EAAA,sBAAA;oBAAwB0B,cAAgB,EAAA;AAAuB,iBAAA;gBAChF3D,KAAOuB,EAAAA,UAAAA,CAAW1B,GAAG,CAACkC,cAAAA;AACxB,aAAA;AACG6B,YAAAA,GAAAA,MAAAA,CAAOC,MAAM,CAACN,aAAAA;AAClB,SAAA,CAAA;AAEDlB,QAAAA,OAAAA,EAAAA;KACC,EAAA;AACDd,QAAAA,UAAAA;AACAE,QAAAA,WAAAA;AACAX,QAAAA,QAAAA;AACAF,QAAAA,kBAAAA;AACAmB,QAAAA,cAAAA;AACAvB,QAAAA;AACD,KAAA,CAAA;IAED,OAAO;AACLJ,QAAAA,SAAAA;AACAR,QAAAA,IAAAA,EAAMA,IAAKC,CAAAA,GAAG,CAAC,CAACiE,YAAc;AAC5B,gBAAA,GAAGA,QAAQ;gBACX9D,KAAO8D,EAAAA,QAAAA,CAAS9D,KAAK,CAAC+D,MAAM,CAAC,CAAC9D,IAAAA,GAASA,KAAKC,WAAW;aACzD,CAAA;AACF,KAAA;AACF;;;;"}