function t(t,r){for(var n=0;n<r.length;n++){var i=r[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function r(){return(r=Object.assign||function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}function n(t,r){t.prototype=Object.create(r.prototype),t.prototype.constructor=t,t.__proto__=r}var i=function(){function r(t,r){this.operator=t,this.value=r,Object.defineProperty(this,"t",{writable:!0})}var n,i,e;return r.prototype.addNote=function(t){this.t=this.t||[],this.t.push(t)},n=r,(i=[{key:"notes",get:function(){return this.t}}])&&t(n.prototype,i),e&&t(n,e),r}(),e=function(t){function r(){return t.apply(this,arguments)||this}return n(r,t),r}(i),o=function(t){function r(r,n){if(!Array.isArray(n))throw new Error('"'+r+'" operator expects to receive an array of conditions');return t.call(this,r,n)||this}return n(r,t),r}(e),u="__itself__",f=function(t){function r(r,n,i){var e;return(e=t.call(this,r,i)||this).field=n,e}return n(r,t),r}(i),a=new e("__null__",null),c=Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function s(t,r){return r instanceof o&&r.operator===t}function h(t,r){return 1===r.length?r[0]:new o(t,function t(r,n,i){for(var e=i||[],o=0,u=n.length;o<u;o++){var f=n[o];s(r,f)?t(r,f.value,e):e.push(f)}return e}(t,r))}var v=function(t){return t},d=function(){return Object.create(null)},l=Object.defineProperty(d(),"__@type@__",{value:"ignore value"});function p(t,r,n){if(void 0===n&&(n=!1),!t||t&&t.constructor!==Object)return!1;for(var i in t){if(c(t,i)&&c(r,i)&&(!n||t[i]!==l))return!0}return!1}function w(t){var r=[];for(var n in t)c(t,n)&&t[n]!==l&&r.push(n);return r}function b(t,r){r!==a&&t.push(r)}var y=function(t){return h("and",t)},O=function(t){return h("or",t)},j={compound:function(t,r,n){var i=(Array.isArray(r)?r:[r]).map((function(t){return n.parse(t)}));return new o(t.name,i)},field:function(t,r,n){return new f(t.name,n.field,r)},document:function(t,r){return new e(t.name,r)}},_=function(){function t(t,n){var i=this;void 0===n&&(n=d()),this.i=void 0,this.o=void 0,this.u=void 0,this.s=void 0,this.h=void 0,this.parse=this.parse.bind(this),this.s={operatorToConditionName:n.operatorToConditionName||v,defaultOperatorName:n.defaultOperatorName||"eq",mergeFinalConditions:n.mergeFinalConditions||y},this.i=Object.keys(t).reduce((function(n,e){return n[e]=r({name:i.s.operatorToConditionName(e)},t[e]),n}),{}),this.o=r({},n.fieldContext,{field:"",query:{},parse:this.parse,hasOperators:function(t){return p(t,i.i,n.useIgnoreValue)}}),this.u=r({},n.documentContext,{parse:this.parse,query:{}}),this.h=n.useIgnoreValue?w:Object.keys}var n=t.prototype;return n.setParse=function(t){this.parse=t,this.o.parse=t,this.u.parse=t},n.parseField=function(t,r,n,i){var e=this.i[r];if(!e)throw new Error('Unsupported operator "'+r+'"');if("field"!==e.type)throw new Error("Unexpected "+e.type+' operator "'+r+'" at field level');return this.o.field=t,this.o.query=i,this.parseInstruction(e,n,this.o)},n.parseInstruction=function(t,r,n){return"function"==typeof t.validate&&t.validate(t,r),(t.parse||j[t.type])(t,r,n)},n.parseFieldOperators=function(t,r){for(var n=[],i=this.h(r),e=0,o=i.length;e<o;e++){var u=i[e];if(!this.i[u])throw new Error('Field query for "'+t+'" may contain only operators or a plain object as a value');b(n,this.parseField(t,u,r[u],r))}return n},n.parse=function(t){var r=[],n=this.h(t);this.u.query=t;for(var i=0,e=n.length;i<e;i++){var o=n[i],u=t[o],f=this.i[o];if(f){if("document"!==f.type&&"compound"!==f.type)throw new Error('Cannot use parsing instruction for operator "'+o+'" in "document" context as it is supposed to be used in  "'+f.type+'" context');b(r,this.parseInstruction(f,u,this.u))}else this.o.hasOperators(u)?r.push.apply(r,this.parseFieldOperators(o,u)):b(r,this.parseField(o,this.s.defaultOperatorName,u,t))}return this.s.mergeFinalConditions(r)},t}();function m(t,r){var n=t[r];if("function"!=typeof n)throw new Error('Unable to interpret "'+r+'" condition. Did you forget to register interpreter for it?');return n}function g(t){return t.operator}function E(t,n){var i,e=n,o=e&&e.getInterpreterName||g;switch(e?e.numberOfArguments:0){case 1:i=function(r){var n=o(r,e);return m(t,n)(r,u)};break;case 3:i=function(r,n,i){var f=o(r,e);return m(t,f)(r,n,i,u)};break;default:i=function(r,n){var i=o(r,e);return m(t,i)(r,n,u)}}var u=r({},e,{interpret:i});return u.interpret}function x(t,r){return function(n){for(var i=arguments.length,e=new Array(i>1?i-1:0),o=1;o<i;o++)e[o-1]=arguments[o];var u=t.apply(void 0,[n].concat(e)),f=r.bind(null,u);return f.ast=u,f}}var q=_.prototype.parseInstruction;export{o as CompoundCondition,i as Condition,e as DocumentCondition,f as FieldCondition,u as ITSELF,a as NULL_CONDITION,_ as ObjectQueryParser,y as buildAnd,O as buildOr,E as createInterpreter,x as createTranslatorFactory,j as defaultInstructionParsers,p as hasOperators,v as identity,l as ignoreValue,s as isCompound,d as object,h as optimizedCompoundCondition,q as parseInstruction};
//# sourceMappingURL=index.js.map
