import type { SVGProps } from 'react';
import { Ref, forwardRef } from 'react';
const SvgX = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width={16} height={16} ref={ref} {...props}>
    <g fillRule="evenodd" clipRule="evenodd">
      <path
        fill="#FAFAFA"
        d="M6.566 6.533c.064.092 1.557 2.264 3.317 4.828l3.617 5.268c.23.334.418.614.418.622s-.086.114-.19.234-.4.462-.654.76l-3.258 3.787c-1.153 1.34-1.32 1.534-2.197 2.556-.47.546-.919 1.068-1 1.16s-.146.177-.146.189c0 .014.295.021.83.021h.83l.911-1.062 1.1-1.279a888 888 0 0 0 2.243-2.61c.043-.048.377-.437.744-.864s.676-.787.689-.8l.431-.502a9 9 0 0 1 .424-.478c.009 0 1.164 1.672 2.567 3.717l2.608 3.797.055.08h2.846c2.34.001 2.843-.004 2.834-.027-.01-.025-1.373-2.013-4.87-7.103-2.517-3.665-2.852-4.157-2.843-4.182.01-.024.353-.425 2.607-3.049l1.779-2.07c.062-.07.388-.45.724-.84l1.96-2.283c.027-.035-.02-.038-.814-.038h-.842l-.375.437a1174 1174 0 0 1-2.23 2.594c-.084.096-.506.586-.938 1.09a129 129 0 0 1-1.004 1.167c-.186.22-.374.44-1.239 1.442-.38.44-.399.459-.43.418-.02-.023-1.132-1.64-2.473-3.594L12.16 6.366H6.45zm2.228 1.165 1.186 1.7c1.196 1.71 5.895 8.436 8.917 12.763a421 421 0 0 0 1.783 2.54c.02.022.301.026 1.314.022l1.287-.005-3.37-4.823-5.963-8.534-2.593-3.712-1.3-.005-1.3-.006z"
      />
      <path
        fill="#040404"
        d="M0 16v16l16.005-.005 16.006-.006.005-15.994L32.022 0H0zm.01.01c0 8.8.003 12.4.006 8s.003-11.6 0-16-.005-.8-.005 8m6.556-9.477c.064.092 1.557 2.264 3.317 4.828l3.617 5.268c.23.334.418.614.418.622s-.086.114-.19.234-.399.462-.654.76l-2.014 2.34-1.244 1.447c-1.153 1.34-1.32 1.534-2.197 2.556-.469.546-.918 1.068-1 1.16-.08.092-.146.177-.146.189 0 .014.295.021.83.021h.83l.911-1.062c.502-.585.996-1.16 1.1-1.279a888 888 0 0 0 2.243-2.61c.043-.048.377-.437.744-.864l.689-.8.431-.502a9 9 0 0 1 .424-.478c.009 0 1.164 1.672 2.567 3.717l2.608 3.797.056.08h2.845c2.34.001 2.843-.004 2.834-.027-.01-.025-1.373-2.013-4.87-7.103-2.517-3.665-2.852-4.157-2.842-4.182.009-.024.352-.425 2.606-3.049l1.78-2.07.723-.84 1.96-2.283c.027-.035-.02-.038-.814-.038h-.842l-.375.437a1129 1129 0 0 1-2.23 2.594c-.084.096-.506.586-.938 1.09a129 129 0 0 1-1.004 1.167c-.186.22-.374.44-1.239 1.442-.38.44-.399.459-.43.418-.02-.023-1.132-1.64-2.473-3.594L12.16 6.366H6.45zm2.228 1.165 1.186 1.7 8.918 12.763a416 416 0 0 0 1.782 2.54c.02.022.301.026 1.314.022l1.287-.005-3.37-4.823-5.963-8.534-2.593-3.712-1.3-.005-1.3-.006z"
      />
    </g>
  </svg>
);
const ForwardRef = forwardRef(SvgX);
export default ForwardRef;
