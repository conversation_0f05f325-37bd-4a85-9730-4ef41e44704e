{"name": "@ucast/core", "version": "1.10.2", "description": "**************:stalniy/ucast.git", "sideEffects": false, "main": "dist/es6c/index.js", "module": "dist/es5m/index.js", "es2015": "dist/es6m/index.mjs", "legacy": "dist/umd/index.js", "typings": "dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/es6m/index.mjs", "require": "./dist/es6c/index.js"}}, "scripts": {"build.types": "tsc", "prebuild": "rm -rf dist/* && npm run build.types", "build": "rollup -c ../../rollup.config.js -n ucast.core", "lint": "eslint --ext .js,.ts src/ spec/", "test": "mocha -r ts-node/register spec/*", "coverage": "nyc -n src npm run test && nyc report --reporter=lcov", "prerelease": "npm run lint && npm test && NODE_ENV=production npm run build", "release": "semantic-release -e ../../semantic-release.js"}, "publishConfig": {"access": "public"}, "files": ["dist", "NOTICE", "*.d.ts"], "repository": {"type": "git", "url": "https://github.com/stalniy/ucast.git", "directory": "packages/core"}, "keywords": ["where", "sql", "mongo", "conditions", "query", "builder", "ast"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/stalniy/ucast/issues"}, "homepage": "https://github.com/stalniy/ucast#readme", "devDependencies": {"@babel/core": "^7.10.2", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.10.4", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/preset-env": "^7.10.2", "@rollup/plugin-babel": "^5.0.3", "@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.0.1", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@semantic-release/github": "^7.0.7", "@semantic-release/npm": "^7.0.5", "@types/chai": "^4.2.11", "@types/chai-spies": "^1.0.1", "@types/mocha": "^7.0.2", "@typescript-eslint/eslint-plugin": "^3.6.0", "babel-register": "^6.26.0", "chai": "^4.2.0", "chai-spies": "^1.0.0", "eslint": "^7.4.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-airbnb-typescript": "^8.0.2", "eslint-plugin-import": "^2.22.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "rollup": "^2.15.0", "rollup-plugin-terser": "^6.1.0", "semantic-release": "^17.4.7", "ts-node": "^8.10.2", "typescript": "^3.9.5"}}