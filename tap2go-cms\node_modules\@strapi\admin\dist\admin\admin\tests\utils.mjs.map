{"version": 3, "file": "utils.mjs", "sources": ["../../../../admin/tests/utils.tsx"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { ConfigureStoreOptions, configureStore } from '@reduxjs/toolkit';\nimport { fixtures } from '@strapi/admin-test-utils';\nimport { darkTheme, lightTheme } from '@strapi/design-system';\nimport {\n  fireEvent,\n  renderHook as renderHookRTL,\n  render as renderRTL,\n  waitFor,\n  RenderOptions as RTLRenderOptions,\n  RenderResult,\n  act,\n  screen,\n  RenderHookOptions,\n  RenderHookResult,\n  Queries,\n} from '@testing-library/react';\nimport { userEvent } from '@testing-library/user-event';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { QueryClient, QueryClientProvider, setLogger } from 'react-query';\nimport { Provider } from 'react-redux';\nimport { MemoryRouterProps, RouterProvider, createMemoryRouter } from 'react-router-dom';\n\nimport { GuidedTourProvider } from '../src/components/GuidedTour/Provider';\nimport { LanguageProvider } from '../src/components/LanguageProvider';\nimport { Theme } from '../src/components/Theme';\nimport { RBAC } from '../src/core/apis/rbac';\nimport { AppInfoProvider } from '../src/features/AppInfo';\nimport { AuthProvider, type Permission } from '../src/features/Auth';\nimport { _internalConfigurationContextProvider as ConfigurationContextProvider } from '../src/features/Configuration';\nimport { NotificationsProvider } from '../src/features/Notifications';\nimport { StrapiAppProvider } from '../src/features/StrapiApp';\nimport { reducer as appReducer } from '../src/reducer';\nimport { adminApi } from '../src/services/api';\n\nimport { server } from './server';\nimport { initialState } from './store';\n\nsetLogger({\n  log: () => {},\n  warn: () => {},\n  error: () => {},\n});\n\ninterface ProvidersProps {\n  children: React.ReactNode;\n  initialEntries?: MemoryRouterProps['initialEntries'];\n  storeConfig?: Partial<ConfigureStoreOptions>;\n  permissions?: Permission[] | ((defaultPermissions: Permission[]) => Permission[] | undefined);\n}\n\nconst defaultTestStoreConfig = () => ({\n  preloadedState: initialState(),\n  reducer: {\n    [adminApi.reducerPath]: adminApi.reducer,\n    admin_app: appReducer,\n  },\n  // @ts-expect-error – this fails.\n  middleware: (getDefaultMiddleware) => [\n    ...getDefaultMiddleware({\n      // Disable timing checks for test env\n      immutableCheck: false,\n      serializableCheck: false,\n    }),\n    adminApi.middleware,\n  ],\n});\n\nconst DEFAULT_PERMISSIONS = [\n  ...fixtures.permissions.allPermissions,\n  {\n    id: 314,\n    action: 'admin::users.read',\n    subject: null,\n    properties: {},\n    conditions: [],\n    actionParameters: {},\n  },\n];\n\nconst Providers = ({ children, initialEntries, storeConfig, permissions = [] }: ProvidersProps) => {\n  const queryClient = new QueryClient({\n    defaultOptions: {\n      queries: {\n        retry: false,\n      },\n    },\n  });\n\n  const store = configureStore({\n    ...defaultTestStoreConfig(),\n    ...storeConfig,\n  });\n\n  const allPermissions =\n    typeof permissions === 'function'\n      ? permissions(DEFAULT_PERMISSIONS)\n      : [...DEFAULT_PERMISSIONS, ...permissions];\n\n  const router = createMemoryRouter(\n    [\n      {\n        path: '/*',\n        element: (\n          <StrapiAppProvider\n            components={{}}\n            rbac={new RBAC()}\n            widgets={{\n              widgets: {},\n              getAll: jest.fn(),\n              register: jest.fn(),\n            }}\n            customFields={{\n              customFields: {},\n              get: jest.fn().mockReturnValue({\n                name: 'color',\n                pluginId: 'mycustomfields',\n                type: 'text',\n                icon: jest.fn(),\n                intlLabel: {\n                  id: 'mycustomfields.color.label',\n                  defaultMessage: 'Color',\n                },\n                intlDescription: {\n                  id: 'mycustomfields.color.description',\n                  defaultMessage: 'Select any color',\n                },\n                components: {\n                  Input: jest.fn().mockResolvedValue({ default: jest.fn() }),\n                },\n              }),\n              getAll: jest.fn(),\n              register: jest.fn(),\n            }}\n            fields={{}}\n            menu={[]}\n            getAdminInjectedComponents={jest.fn()}\n            getPlugin={jest.fn()}\n            plugins={{}}\n            runHookParallel={jest.fn()}\n            runHookWaterfall={jest.fn().mockImplementation((_name, initialValue) => initialValue)}\n            runHookSeries={jest.fn()}\n            settings={{}}\n          >\n            <Provider store={store}>\n              <AuthProvider _defaultPermissions={allPermissions} _disableRenewToken={true}>\n                <QueryClientProvider client={queryClient}>\n                  <DndProvider backend={HTML5Backend}>\n                    <LanguageProvider messages={{}}>\n                      <Theme\n                        themes={{\n                          dark: darkTheme,\n                          light: lightTheme,\n                        }}\n                      >\n                        <NotificationsProvider>\n                          <GuidedTourProvider>\n                            <ConfigurationContextProvider\n                              showReleaseNotification={false}\n                              logos={{\n                                auth: { default: 'default' },\n                                menu: { default: 'default' },\n                              }}\n                              updateProjectSettings={jest.fn()}\n                            >\n                              <AppInfoProvider\n                                autoReload\n                                useYarn\n                                dependencies={{\n                                  '@strapi/plugin-documentation': '4.2.0',\n                                  '@strapi/provider-upload-cloudinary': '4.2.0',\n                                }}\n                                strapiVersion=\"4.1.0\"\n                                communityEdition\n                                shouldUpdateStrapi={false}\n                              >\n                                {children}\n                              </AppInfoProvider>\n                            </ConfigurationContextProvider>\n                          </GuidedTourProvider>\n                        </NotificationsProvider>\n                      </Theme>\n                    </LanguageProvider>\n                  </DndProvider>\n                </QueryClientProvider>\n              </AuthProvider>\n            </Provider>\n          </StrapiAppProvider>\n        ),\n      },\n    ],\n    {\n      initialEntries,\n    }\n  );\n\n  // en is the default locale of the admin app.\n  return <RouterProvider router={router} />;\n};\n\n// eslint-disable-next-line react/jsx-no-useless-fragment\nconst fallbackWrapper = ({ children }: { children: React.ReactNode }) => <>{children}</>;\n\nexport interface RenderOptions {\n  renderOptions?: RTLRenderOptions;\n  userEventOptions?: Parameters<typeof userEvent.setup>[0];\n  initialEntries?: MemoryRouterProps['initialEntries'];\n  providerOptions?: Pick<ProvidersProps, 'storeConfig' | 'permissions'>;\n}\n\n/**\n * @alpha\n * @description A custom render function that wraps the component with the necessary providers,\n * for use of testing components within the Strapi Admin.\n */\nconst render = (\n  ui: React.ReactElement,\n  { renderOptions, userEventOptions, initialEntries, providerOptions }: RenderOptions = {}\n): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {\n  const { wrapper: Wrapper = fallbackWrapper, ...restOptions } = renderOptions ?? {};\n\n  return {\n    ...renderRTL(ui, {\n      wrapper: ({ children }) => (\n        <Providers initialEntries={initialEntries} {...providerOptions}>\n          <Wrapper>{children}</Wrapper>\n        </Providers>\n      ),\n      ...restOptions,\n    }),\n    user: userEvent.setup({\n      skipHover: true,\n      ...userEventOptions,\n    }),\n  };\n};\n\n/**\n * @alpha\n * @description A custom render-hook function that wraps the component with the necessary providers,\n * for use of testing hooks within the Strapi Admin.\n */\nconst renderHook = <\n  Result,\n  Props,\n  Q extends Queries,\n  Container extends Element | DocumentFragment = HTMLElement,\n  BaseElement extends Element | DocumentFragment = Container,\n>(\n  hook: (initialProps: Props) => Result,\n  options?: RenderHookOptions<Props, Q, Container, BaseElement> &\n    Pick<RenderOptions, 'initialEntries' | 'providerOptions'>\n): RenderHookResult<Result, Props> => {\n  const {\n    wrapper: Wrapper = fallbackWrapper,\n    initialEntries,\n    providerOptions,\n    ...restOptions\n  } = options ?? {};\n\n  return renderHookRTL(hook, {\n    wrapper: ({ children }) => (\n      <Providers initialEntries={initialEntries} {...providerOptions}>\n        <Wrapper>{children}</Wrapper>\n      </Providers>\n    ),\n    ...restOptions,\n  });\n};\n\nexport { render, renderHook, waitFor, server, act, screen, fireEvent, defaultTestStoreConfig };\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "log", "warn", "error", "defaultTestStoreConfig", "preloadedState", "initialState", "reducer", "adminApi", "reducerPath", "admin_app", "appReducer", "middleware", "getDefaultMiddleware", "immutableCheck", "serializableCheck", "DEFAULT_PERMISSIONS", "fixtures", "permissions", "allPermissions", "id", "action", "subject", "properties", "conditions", "actionParameters", "Providers", "children", "initialEntries", "storeConfig", "queryClient", "QueryClient", "defaultOptions", "queries", "retry", "store", "configureStore", "router", "createMemoryRouter", "path", "element", "_jsx", "StrapiAppProvider", "components", "rbac", "RBAC", "widgets", "getAll", "jest", "fn", "register", "customFields", "get", "mockReturnValue", "name", "pluginId", "type", "icon", "intlLabel", "defaultMessage", "intlDescription", "Input", "mockResolvedValue", "default", "fields", "menu", "getAdminInjectedComponents", "getPlugin", "plugins", "runHookParallel", "runHookWaterfall", "mockImplementation", "_name", "initialValue", "runHookSeries", "settings", "Provider", "<PERSON>th<PERSON><PERSON><PERSON>", "_defaultPermissions", "_disableRenewToken", "QueryClientProvider", "client", "DndProvider", "backend", "HTML5Backend", "LanguageProvider", "messages", "Theme", "themes", "dark", "darkTheme", "light", "lightTheme", "NotificationsProvider", "GuidedTourProvider", "ConfigurationContextProvider", "showReleaseNotification", "logos", "auth", "updateProjectSettings", "AppInfoProvider", "autoReload", "<PERSON><PERSON><PERSON><PERSON>", "dependencies", "strapiVersion", "communityEdition", "shouldUpdateStrapi", "RouterProvider", "fallbackWrapper", "_Fragment", "render", "ui", "renderOptions", "userEventOptions", "providerOptions", "wrapper", "Wrapper", "restOptions", "renderRTL", "user", "userEvent", "setup", "skipHover", "renderHook", "hook", "options", "renderHookRTL"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCAA,SAAU,CAAA;AACRC,IAAAA,GAAAA,EAAK,IAAO,EAAA;AACZC,IAAAA,IAAAA,EAAM,IAAO,EAAA;AACbC,IAAAA,KAAAA,EAAO,IAAO;AAChB,CAAA,CAAA;AASMC,MAAAA,sBAAAA,GAAyB,KAAO;QACpCC,cAAgBC,EAAAA,YAAAA,EAAAA;QAChBC,OAAS,EAAA;AACP,YAAA,CAACC,QAASC,CAAAA,WAAW,GAAGD,SAASD,OAAO;YACxCG,SAAWC,EAAAA;AACb,SAAA;;AAEAC,QAAAA,UAAAA,EAAY,CAACC,oBAAyB,GAAA;mBACjCA,oBAAqB,CAAA;;oBAEtBC,cAAgB,EAAA,KAAA;oBAChBC,iBAAmB,EAAA;AACrB,iBAAA,CAAA;AACAP,gBAAAA,QAAAA,CAASI;AACV;KACH;AAEA,MAAMI,mBAAsB,GAAA;OACvBC,QAASC,CAAAA,WAAW,CAACC,cAAc;AACtC,IAAA;QACEC,EAAI,EAAA,GAAA;QACJC,MAAQ,EAAA,mBAAA;QACRC,OAAS,EAAA,IAAA;AACTC,QAAAA,UAAAA,EAAY,EAAC;AACbC,QAAAA,UAAAA,EAAY,EAAE;AACdC,QAAAA,gBAAAA,EAAkB;AACpB;AACD,CAAA;AAED,MAAMC,SAAAA,GAAY,CAAC,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,WAAW,EAAEX,WAAc,GAAA,EAAE,EAAkB,GAAA;IAC5F,MAAMY,WAAAA,GAAc,IAAIC,WAAY,CAAA;QAClCC,cAAgB,EAAA;YACdC,OAAS,EAAA;gBACPC,KAAO,EAAA;AACT;AACF;AACF,KAAA,CAAA;AAEA,IAAA,MAAMC,QAAQC,cAAe,CAAA;AAC3B,QAAA,GAAGhC,sBAAwB,EAAA;AAC3B,QAAA,GAAGyB;AACL,KAAA,CAAA;AAEA,IAAA,MAAMV,cACJ,GAAA,OAAOD,WAAgB,KAAA,UAAA,GACnBA,YAAYF,mBACZ,CAAA,GAAA;AAAIA,QAAAA,GAAAA,mBAAAA;AAAwBE,QAAAA,GAAAA;AAAY,KAAA;AAE9C,IAAA,MAAMmB,SAASC,kBACb,CAAA;AACE,QAAA;YACEC,IAAM,EAAA,IAAA;AACNC,YAAAA,OAAAA,gBACEC,GAACC,CAAAA,iBAAAA,EAAAA;AACCC,gBAAAA,UAAAA,EAAY,EAAC;AACbC,gBAAAA,IAAAA,EAAM,IAAIC,IAAAA,EAAAA;gBACVC,OAAS,EAAA;AACPA,oBAAAA,OAAAA,EAAS,EAAC;AACVC,oBAAAA,MAAAA,EAAQC,KAAKC,EAAE,EAAA;AACfC,oBAAAA,QAAAA,EAAUF,KAAKC,EAAE;AACnB,iBAAA;gBACAE,YAAc,EAAA;AACZA,oBAAAA,YAAAA,EAAc,EAAC;AACfC,oBAAAA,GAAAA,EAAKJ,IAAKC,CAAAA,EAAE,EAAGI,CAAAA,eAAe,CAAC;wBAC7BC,IAAM,EAAA,OAAA;wBACNC,QAAU,EAAA,gBAAA;wBACVC,IAAM,EAAA,MAAA;AACNC,wBAAAA,IAAAA,EAAMT,KAAKC,EAAE,EAAA;wBACbS,SAAW,EAAA;4BACTtC,EAAI,EAAA,4BAAA;4BACJuC,cAAgB,EAAA;AAClB,yBAAA;wBACAC,eAAiB,EAAA;4BACfxC,EAAI,EAAA,kCAAA;4BACJuC,cAAgB,EAAA;AAClB,yBAAA;wBACAhB,UAAY,EAAA;AACVkB,4BAAAA,KAAAA,EAAOb,IAAKC,CAAAA,EAAE,EAAGa,CAAAA,iBAAiB,CAAC;AAAEC,gCAAAA,OAAAA,EAASf,KAAKC,EAAE;AAAG,6BAAA;AAC1D;AACF,qBAAA,CAAA;AACAF,oBAAAA,MAAAA,EAAQC,KAAKC,EAAE,EAAA;AACfC,oBAAAA,QAAAA,EAAUF,KAAKC,EAAE;AACnB,iBAAA;AACAe,gBAAAA,MAAAA,EAAQ,EAAC;AACTC,gBAAAA,IAAAA,EAAM,EAAE;AACRC,gBAAAA,0BAAAA,EAA4BlB,KAAKC,EAAE,EAAA;AACnCkB,gBAAAA,SAAAA,EAAWnB,KAAKC,EAAE,EAAA;AAClBmB,gBAAAA,OAAAA,EAAS,EAAC;AACVC,gBAAAA,eAAAA,EAAiBrB,KAAKC,EAAE,EAAA;AACxBqB,gBAAAA,gBAAAA,EAAkBtB,KAAKC,EAAE,EAAA,CAAGsB,kBAAkB,CAAC,CAACC,OAAOC,YAAiBA,GAAAA,YAAAA,CAAAA;AACxEC,gBAAAA,aAAAA,EAAe1B,KAAKC,EAAE,EAAA;AACtB0B,gBAAAA,QAAAA,EAAU,EAAC;AAEX,gBAAA,QAAA,gBAAAlC,GAACmC,CAAAA,QAAAA,EAAAA;oBAASzC,KAAOA,EAAAA,KAAAA;AACf,oBAAA,QAAA,gBAAAM,GAACoC,CAAAA,YAAAA,EAAAA;wBAAaC,mBAAqB3D,EAAAA,cAAAA;wBAAgB4D,kBAAoB,EAAA,IAAA;AACrE,wBAAA,QAAA,gBAAAtC,GAACuC,CAAAA,mBAAAA,EAAAA;4BAAoBC,MAAQnD,EAAAA,WAAAA;AAC3B,4BAAA,QAAA,gBAAAW,GAACyC,CAAAA,WAAAA,EAAAA;gCAAYC,OAASC,EAAAA,YAAAA;AACpB,gCAAA,QAAA,gBAAA3C,GAAC4C,CAAAA,gBAAAA,EAAAA;AAAiBC,oCAAAA,QAAAA,EAAU,EAAC;AAC3B,oCAAA,QAAA,gBAAA7C,GAAC8C,CAAAA,KAAAA,EAAAA;wCACCC,MAAQ,EAAA;4CACNC,IAAMC,EAAAA,SAAAA;4CACNC,KAAOC,EAAAA;AACT,yCAAA;AAEA,wCAAA,QAAA,gBAAAnD,GAACoD,CAAAA,qBAAAA,EAAAA;AACC,4CAAA,QAAA,gBAAApD,GAACqD,CAAAA,kBAAAA,EAAAA;AACC,gDAAA,QAAA,gBAAArD,GAACsD,CAAAA,4BAAAA,EAAAA;oDACCC,uBAAyB,EAAA,KAAA;oDACzBC,KAAO,EAAA;wDACLC,IAAM,EAAA;4DAAEnC,OAAS,EAAA;AAAU,yDAAA;wDAC3BE,IAAM,EAAA;4DAAEF,OAAS,EAAA;AAAU;AAC7B,qDAAA;AACAoC,oDAAAA,qBAAAA,EAAuBnD,KAAKC,EAAE,EAAA;AAE9B,oDAAA,QAAA,gBAAAR,GAAC2D,CAAAA,eAAAA,EAAAA;wDACCC,UAAU,EAAA,IAAA;wDACVC,OAAO,EAAA,IAAA;wDACPC,YAAc,EAAA;4DACZ,8BAAgC,EAAA,OAAA;4DAChC,oCAAsC,EAAA;AACxC,yDAAA;wDACAC,aAAc,EAAA,OAAA;wDACdC,gBAAgB,EAAA,IAAA;wDAChBC,kBAAoB,EAAA,KAAA;AAEnB/E,wDAAAA,QAAAA,EAAAA;;;;;;;;;;;;AAa3B;KACD,EACD;AACEC,QAAAA;AACF,KAAA,CAAA;;AAIF,IAAA,qBAAOa,GAACkE,CAAAA,cAAAA,EAAAA;QAAetE,MAAQA,EAAAA;;AACjC,CAAA;AAEA;AACA,MAAMuE,eAAkB,GAAA,CAAC,EAAEjF,QAAQ,EAAiC,iBAAKc,GAAA,CAAAoE,QAAA,EAAA;AAAGlF,QAAAA,QAAAA,EAAAA;;AAS5E;;;;AAIC,IACKmF,MAAAA,MAAAA,GAAS,CACbC,EAAAA,EACA,EAAEC,aAAa,EAAEC,gBAAgB,EAAErF,cAAc,EAAEsF,eAAe,EAAiB,GAAG,EAAE,GAAA;IAExF,MAAM,EAAEC,SAASC,OAAUR,GAAAA,eAAe,EAAE,GAAGS,WAAAA,EAAa,GAAGL,aAAAA,IAAiB,EAAC;IAEjF,OAAO;AACL,QAAA,GAAGM,SAAUP,EAAI,EAAA;AACfI,YAAAA,OAAAA,EAAS,CAAC,EAAExF,QAAQ,EAAE,iBACpBc,GAACf,CAAAA,SAAAA,EAAAA;oBAAUE,cAAgBA,EAAAA,cAAAA;AAAiB,oBAAA,GAAGsF,eAAe;AAC5D,oBAAA,QAAA,gBAAAzE,GAAC2E,CAAAA,OAAAA,EAAAA;AAASzF,wBAAAA,QAAAA,EAAAA;;;AAGd,YAAA,GAAG0F;SACH,CAAA;QACFE,IAAMC,EAAAA,SAAAA,CAAUC,KAAK,CAAC;YACpBC,SAAW,EAAA,IAAA;AACX,YAAA,GAAGT;AACL,SAAA;AACF,KAAA;AACF;AAEA;;;;IAKA,MAAMU,UAAa,GAAA,CAOjBC,IACAC,EAAAA,OAAAA,GAAAA;AAGA,IAAA,MAAM,EACJV,OAAAA,EAASC,OAAUR,GAAAA,eAAe,EAClChF,cAAc,EACdsF,eAAe,EACf,GAAGG,WACJ,EAAA,GAAGQ,WAAW,EAAC;AAEhB,IAAA,OAAOC,aAAcF,IAAM,EAAA;AACzBT,QAAAA,OAAAA,EAAS,CAAC,EAAExF,QAAQ,EAAE,iBACpBc,GAACf,CAAAA,SAAAA,EAAAA;gBAAUE,cAAgBA,EAAAA,cAAAA;AAAiB,gBAAA,GAAGsF,eAAe;AAC5D,gBAAA,QAAA,gBAAAzE,GAAC2E,CAAAA,OAAAA,EAAAA;AAASzF,oBAAAA,QAAAA,EAAAA;;;AAGd,QAAA,GAAG0F;AACL,KAAA,CAAA;AACF;;;;"}