{"name": "use-context-selector", "description": "React useContextSelector hook in userland", "version": "1.4.1", "author": "<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/dai-shi/use-context-selector.git"}, "source": "./src/index.ts", "main": "./dist/index.umd.js", "module": "./dist/index.modern.js", "react-native": "./dist/index.native.modern.js", "types": "./dist/src/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/src/index.d.ts", "module": "./dist/index.modern.js", "import": "./dist/index.modern.mjs", "default": "./dist/index.umd.js"}}, "sideEffects": false, "files": ["src", "dist"], "scripts": {"compile": "run-s compile:*", "compile:dom": "microbundle build -f modern,umd --globals react=React,react-dom=ReactDOM", "postcompile:dom": "cp dist/index.modern.js dist/index.modern.mjs && cp dist/index.modern.js.map dist/index.modern.mjs.map", "precompile:native": "mkdir src/native && cp src/index.ts src/native/index.ts && cp src/batchedUpdates.native.ts src/native/batchedUpdates.ts", "compile:native": "microbundle build -f modern,umd -i ./src/native/index.ts -o ./dist/index.native.js", "postcompile:native": "rm -r src/native", "test": "run-s eslint tsc-test jest", "eslint": "eslint --ext .js,.ts,.tsx --ignore-pattern dist .", "jest": "jest", "tsc-test": "tsc --project . --noEmit", "apidoc": "documentation readme --section API --markdown-toc false --parse-extension ts --require-extension ts src/*.ts", "examples:01_minimal": "DIR=01_minimal EXT=js webpack serve", "examples:02_typescript": "DIR=02_typescript webpack serve", "examples:03_suspense": "DIR=03_suspense webpack serve"}, "jest": {"testEnvironment": "jsdom", "preset": "ts-jest/presets/js-with-ts"}, "keywords": ["react", "context", "hooks"], "license": "MIT", "dependencies": {}, "devDependencies": {"@testing-library/react": "^13.3.0", "@types/jest": "^28.1.1", "@types/react": "^18.0.12", "@types/react-dom": "^18.0.5", "@types/scheduler": "^0.16.2", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "documentation": "^13.2.5", "eslint": "^8.17.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.5.0", "html-webpack-plugin": "^5.5.0", "jest": "^28.1.1", "jest-environment-jsdom": "^28.1.1", "microbundle": "^0.15.0", "npm-run-all": "^4.1.5", "react": "^18.1.0", "react-dom": "^18.1.0", "scheduler": "^0.22.0", "ts-jest": "^28.0.4", "ts-loader": "^9.3.0", "typescript": "^4.7.3", "webpack": "^5.73.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.9.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": "*", "react-native": "*", "scheduler": ">=0.19.0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}