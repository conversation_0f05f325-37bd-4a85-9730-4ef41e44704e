{"version": 3, "file": "Settings.mjs", "sources": ["../../../../../admin/src/pages/ListConfiguration/components/Settings.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { useForm, InputRenderer, type InputProps } from '@strapi/admin/strapi-admin';\nimport { Flex, Grid, Typography, useCollator } from '@strapi/design-system';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { type EditFieldLayout } from '../../../hooks/useDocumentLayout';\nimport { getTranslation } from '../../../utils/translations';\nimport { type FormData } from '../ListConfigurationPage';\n\nimport type { DistributiveOmit } from 'react-redux';\n\nexport type InputPropsWithMessageDescriptors = DistributiveOmit<\n  InputProps,\n  'hint' | 'label' | 'placeholder'\n> & {\n  hint?: MessageDescriptor;\n  label: MessageDescriptor;\n  placeholder?: MessageDescriptor;\n};\n\n/**\n * @internal\n * @description Form inputs are always displayed in a grid, so we need\n * to use the size property to determine how many columns the input should\n * take up.\n */\nexport type FormLayoutInputProps = InputPropsWithMessageDescriptors & { size: number };\n\nconst EXCLUDED_SORT_ATTRIBUTE_TYPES = [\n  'media',\n  'richtext',\n  'dynamiczone',\n  'relation',\n  'component',\n  'json',\n  'blocks',\n];\n\ninterface SortOption {\n  value: string;\n  label: string;\n}\n\nconst Settings = () => {\n  const { formatMessage, locale } = useIntl();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n  const { schema } = useDoc();\n\n  const layout = useForm<FormData['layout']>('Settings', (state) => state.values.layout ?? []);\n  const currentSortBy = useForm<FormData['settings']['defaultSortBy']>(\n    'Settings',\n    (state) => state.values.settings.defaultSortBy\n  );\n  const onChange = useForm('Settings', (state) => state.onChange);\n\n  const sortOptions = React.useMemo(\n    () =>\n      Object.values(layout).reduce<SortOption[]>((acc, field) => {\n        if (schema && !EXCLUDED_SORT_ATTRIBUTE_TYPES.includes(schema.attributes[field.name].type)) {\n          acc.push({\n            value: field.name,\n            label: typeof field.label !== 'string' ? formatMessage(field.label) : field.label,\n          });\n        }\n\n        return acc;\n      }, []),\n    [formatMessage, layout, schema]\n  );\n\n  const sortOptionsSorted = sortOptions.sort((a, b) => formatter.compare(a.label, b.label));\n\n  React.useEffect(() => {\n    if (sortOptionsSorted.findIndex((opt) => opt.value === currentSortBy) === -1) {\n      onChange('settings.defaultSortBy', sortOptionsSorted[0]?.value);\n    }\n  }, [currentSortBy, onChange, sortOptionsSorted]);\n\n  const formLayout = React.useMemo(\n    () =>\n      SETTINGS_FORM_LAYOUT.map((row) =>\n        row.map((field) => {\n          if (field.type === 'enumeration') {\n            return {\n              ...field,\n              hint: field.hint ? formatMessage(field.hint) : undefined,\n              label: formatMessage(field.label),\n              options: field.name === 'settings.defaultSortBy' ? sortOptionsSorted : field.options,\n            };\n          } else {\n            return {\n              ...field,\n              hint: field.hint ? formatMessage(field.hint) : undefined,\n              label: formatMessage(field.label),\n            };\n          }\n        })\n      ) as [top: EditFieldLayout[], bottom: EditFieldLayout[]],\n    [formatMessage, sortOptionsSorted]\n  );\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n      <Typography variant=\"delta\" tag=\"h2\">\n        {formatMessage({\n          id: getTranslation('containers.SettingPage.settings'),\n          defaultMessage: 'Settings',\n        })}\n      </Typography>\n      <Grid.Root key=\"bottom\" gap={4}>\n        {formLayout.map((row) =>\n          row.map(({ size, ...field }) => (\n            <Grid.Item key={field.name} s={12} col={size} direction=\"column\" alignItems=\"stretch\">\n              {/* @ts-expect-error – issue with EnumerationProps conflicting with InputProps */}\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))\n        )}\n      </Grid.Root>\n    </Flex>\n  );\n};\n\nconst SETTINGS_FORM_LAYOUT: FormLayoutInputProps[][] = [\n  [\n    {\n      label: {\n        id: getTranslation('form.Input.search'),\n        defaultMessage: 'Enable search',\n      },\n      name: 'settings.searchable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.filters'),\n        defaultMessage: 'Enable filters',\n      },\n      name: 'settings.filterable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.bulkActions'),\n        defaultMessage: 'Enable bulk actions',\n      },\n      name: 'settings.bulkable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n  ],\n  [\n    {\n      hint: {\n        id: getTranslation('form.Input.pageEntries.inputDescription'),\n        defaultMessage: 'Note: You can override this value in the Collection Type settings page.',\n      },\n      label: {\n        id: getTranslation('form.Input.pageEntries'),\n        defaultMessage: 'Entries per page',\n      },\n      name: 'settings.pageSize',\n      options: ['10', '20', '50', '100'].map((value) => ({ value, label: value })),\n      size: 6,\n      type: 'enumeration' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.defaultSort'),\n        defaultMessage: 'Default sort attribute',\n      },\n      name: 'settings.defaultSortBy',\n      options: [],\n      size: 3,\n      type: 'enumeration' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.sort.order'),\n        defaultMessage: 'Default sort order',\n      },\n      name: 'settings.defaultSortOrder',\n      options: ['ASC', 'DESC'].map((value) => ({ value, label: value })),\n      size: 3,\n      type: 'enumeration' as const,\n    },\n  ],\n];\n\nexport { Settings };\n"], "names": ["EXCLUDED_SORT_ATTRIBUTE_TYPES", "Settings", "formatMessage", "locale", "useIntl", "formatter", "useCollator", "sensitivity", "schema", "useDoc", "layout", "useForm", "state", "values", "currentSortBy", "settings", "defaultSortBy", "onChange", "sortOptions", "React", "useMemo", "Object", "reduce", "acc", "field", "includes", "attributes", "name", "type", "push", "value", "label", "sortOptionsSorted", "sort", "a", "b", "compare", "useEffect", "findIndex", "opt", "formLayout", "SETTINGS_FORM_LAYOUT", "map", "row", "hint", "undefined", "options", "_jsxs", "Flex", "direction", "alignItems", "gap", "_jsx", "Typography", "variant", "tag", "id", "getTranslation", "defaultMessage", "Grid", "Root", "size", "<PERSON><PERSON>", "s", "col", "InputR<PERSON><PERSON>"], "mappings": ";;;;;;;;AA8BA,MAAMA,6BAAgC,GAAA;AACpC,IAAA,OAAA;AACA,IAAA,UAAA;AACA,IAAA,aAAA;AACA,IAAA,UAAA;AACA,IAAA,WAAA;AACA,IAAA,MAAA;AACA,IAAA;AACD,CAAA;AAOD,MAAMC,QAAW,GAAA,IAAA;AACf,IAAA,MAAM,EAAEC,aAAa,EAAEC,MAAM,EAAE,GAAGC,OAAAA,EAAAA;IAClC,MAAMC,SAAAA,GAAYC,YAAYH,MAAQ,EAAA;QACpCI,WAAa,EAAA;AACf,KAAA,CAAA;IACA,MAAM,EAAEC,MAAM,EAAE,GAAGC,MAAAA,EAAAA;IAEnB,MAAMC,MAAAA,GAASC,OAA4B,CAAA,UAAA,EAAY,CAACC,KAAAA,GAAUA,MAAMC,MAAM,CAACH,MAAM,IAAI,EAAE,CAAA;IAC3F,MAAMI,aAAAA,GAAgBH,OACpB,CAAA,UAAA,EACA,CAACC,KAAAA,GAAUA,MAAMC,MAAM,CAACE,QAAQ,CAACC,aAAa,CAAA;AAEhD,IAAA,MAAMC,WAAWN,OAAQ,CAAA,UAAA,EAAY,CAACC,KAAAA,GAAUA,MAAMK,QAAQ,CAAA;AAE9D,IAAA,MAAMC,WAAcC,GAAAA,KAAAA,CAAMC,OAAO,CAC/B,IACEC,MAAAA,CAAOR,MAAM,CAACH,MAAQY,CAAAA,CAAAA,MAAM,CAAe,CAACC,GAAKC,EAAAA,KAAAA,GAAAA;AAC/C,YAAA,IAAIhB,MAAU,IAAA,CAACR,6BAA8ByB,CAAAA,QAAQ,CAACjB,MAAAA,CAAOkB,UAAU,CAACF,KAAMG,CAAAA,IAAI,CAAC,CAACC,IAAI,CAAG,EAAA;AACzFL,gBAAAA,GAAAA,CAAIM,IAAI,CAAC;AACPC,oBAAAA,KAAAA,EAAON,MAAMG,IAAI;oBACjBI,KAAO,EAAA,OAAOP,KAAMO,CAAAA,KAAK,KAAK,QAAA,GAAW7B,cAAcsB,KAAMO,CAAAA,KAAK,CAAIP,GAAAA,KAAAA,CAAMO;AAC9E,iBAAA,CAAA;AACF;YAEA,OAAOR,GAAAA;AACT,SAAA,EAAG,EAAE,CACP,EAAA;AAACrB,QAAAA,aAAAA;AAAeQ,QAAAA,MAAAA;AAAQF,QAAAA;AAAO,KAAA,CAAA;AAGjC,IAAA,MAAMwB,iBAAoBd,GAAAA,WAAAA,CAAYe,IAAI,CAAC,CAACC,CAAGC,EAAAA,CAAAA,GAAM9B,SAAU+B,CAAAA,OAAO,CAACF,CAAAA,CAAEH,KAAK,EAAEI,EAAEJ,KAAK,CAAA,CAAA;AAEvFZ,IAAAA,KAAAA,CAAMkB,SAAS,CAAC,IAAA;QACd,IAAIL,iBAAAA,CAAkBM,SAAS,CAAC,CAACC,GAAAA,GAAQA,IAAIT,KAAK,KAAKhB,aAAmB,CAAA,KAAA,CAAC,CAAG,EAAA;AAC5EG,YAAAA,QAAAA,CAAS,wBAA0Be,EAAAA,iBAAiB,CAAC,CAAA,CAAE,EAAEF,KAAAA,CAAAA;AAC3D;KACC,EAAA;AAAChB,QAAAA,aAAAA;AAAeG,QAAAA,QAAAA;AAAUe,QAAAA;AAAkB,KAAA,CAAA;AAE/C,IAAA,MAAMQ,UAAarB,GAAAA,KAAAA,CAAMC,OAAO,CAC9B,IACEqB,oBAAAA,CAAqBC,GAAG,CAAC,CAACC,GAAAA,GACxBA,GAAID,CAAAA,GAAG,CAAC,CAAClB,KAAAA,GAAAA;gBACP,IAAIA,KAAAA,CAAMI,IAAI,KAAK,aAAe,EAAA;oBAChC,OAAO;AACL,wBAAA,GAAGJ,KAAK;AACRoB,wBAAAA,IAAAA,EAAMpB,MAAMoB,IAAI,GAAG1C,aAAcsB,CAAAA,KAAAA,CAAMoB,IAAI,CAAIC,GAAAA,SAAAA;wBAC/Cd,KAAO7B,EAAAA,aAAAA,CAAcsB,MAAMO,KAAK,CAAA;AAChCe,wBAAAA,OAAAA,EAAStB,MAAMG,IAAI,KAAK,wBAA2BK,GAAAA,iBAAAA,GAAoBR,MAAMsB;AAC/E,qBAAA;iBACK,MAAA;oBACL,OAAO;AACL,wBAAA,GAAGtB,KAAK;AACRoB,wBAAAA,IAAAA,EAAMpB,MAAMoB,IAAI,GAAG1C,aAAcsB,CAAAA,KAAAA,CAAMoB,IAAI,CAAIC,GAAAA,SAAAA;wBAC/Cd,KAAO7B,EAAAA,aAAAA,CAAcsB,MAAMO,KAAK;AAClC,qBAAA;AACF;aAGN,CAAA,CAAA,EAAA;AAAC7B,QAAAA,aAAAA;AAAe8B,QAAAA;AAAkB,KAAA,CAAA;AAGpC,IAAA,qBACEe,IAACC,CAAAA,IAAAA,EAAAA;QAAKC,SAAU,EAAA,QAAA;QAASC,UAAW,EAAA,SAAA;QAAUC,GAAK,EAAA,CAAA;;0BACjDC,GAACC,CAAAA,UAAAA,EAAAA;gBAAWC,OAAQ,EAAA,OAAA;gBAAQC,GAAI,EAAA,IAAA;0BAC7BrD,aAAc,CAAA;AACbsD,oBAAAA,EAAAA,EAAIC,cAAe,CAAA,iCAAA,CAAA;oBACnBC,cAAgB,EAAA;AAClB,iBAAA;;AAEF,0BAAAN,GAAA,CAACO,KAAKC,IAAI,EAAA;gBAAcT,GAAK,EAAA,CAAA;AAC1BX,gBAAAA,QAAAA,EAAAA,UAAAA,CAAWE,GAAG,CAAC,CAACC,GACfA,GAAAA,GAAAA,CAAID,GAAG,CAAC,CAAC,EAAEmB,IAAI,EAAE,GAAGrC,KAAAA,EAAO,iBACzB4B,GAAA,CAACO,KAAKG,IAAI,EAAA;4BAAkBC,CAAG,EAAA,EAAA;4BAAIC,GAAKH,EAAAA,IAAAA;4BAAMZ,SAAU,EAAA,QAAA;4BAASC,UAAW,EAAA,SAAA;AAE1E,4BAAA,QAAA,gBAAAE,GAACa,CAAAA,aAAAA,EAAAA;AAAe,gCAAA,GAAGzC;;AAFLA,yBAAAA,EAAAA,KAAAA,CAAMG,IAAI,CAAA,CAAA;AAHjB,aAAA,EAAA,QAAA;;;AAYrB;AAEA,MAAMc,oBAAiD,GAAA;AACrD,IAAA;AACE,QAAA;YACEV,KAAO,EAAA;AACLyB,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,mBAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA/B,IAAM,EAAA,qBAAA;YACNkC,IAAM,EAAA,CAAA;YACNjC,IAAM,EAAA;AACR,SAAA;AACA,QAAA;YACEG,KAAO,EAAA;AACLyB,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,oBAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA/B,IAAM,EAAA,qBAAA;YACNkC,IAAM,EAAA,CAAA;YACNjC,IAAM,EAAA;AACR,SAAA;AACA,QAAA;YACEG,KAAO,EAAA;AACLyB,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,wBAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA/B,IAAM,EAAA,mBAAA;YACNkC,IAAM,EAAA,CAAA;YACNjC,IAAM,EAAA;AACR;AACD,KAAA;AACD,IAAA;AACE,QAAA;YACEgB,IAAM,EAAA;AACJY,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,yCAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA3B,KAAO,EAAA;AACLyB,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,wBAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA/B,IAAM,EAAA,mBAAA;YACNmB,OAAS,EAAA;AAAC,gBAAA,IAAA;AAAM,gBAAA,IAAA;AAAM,gBAAA,IAAA;AAAM,gBAAA;AAAM,aAAA,CAACJ,GAAG,CAAC,CAACZ,KAAAA,IAAW;AAAEA,oBAAAA,KAAAA;oBAAOC,KAAOD,EAAAA;iBAAM,CAAA,CAAA;YACzE+B,IAAM,EAAA,CAAA;YACNjC,IAAM,EAAA;AACR,SAAA;AACA,QAAA;YACEG,KAAO,EAAA;AACLyB,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,wBAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA/B,IAAM,EAAA,wBAAA;AACNmB,YAAAA,OAAAA,EAAS,EAAE;YACXe,IAAM,EAAA,CAAA;YACNjC,IAAM,EAAA;AACR,SAAA;AACA,QAAA;YACEG,KAAO,EAAA;AACLyB,gBAAAA,EAAAA,EAAIC,cAAe,CAAA,uBAAA,CAAA;gBACnBC,cAAgB,EAAA;AAClB,aAAA;YACA/B,IAAM,EAAA,2BAAA;YACNmB,OAAS,EAAA;AAAC,gBAAA,KAAA;AAAO,gBAAA;AAAO,aAAA,CAACJ,GAAG,CAAC,CAACZ,KAAAA,IAAW;AAAEA,oBAAAA,KAAAA;oBAAOC,KAAOD,EAAAA;iBAAM,CAAA,CAAA;YAC/D+B,IAAM,EAAA,CAAA;YACNjC,IAAM,EAAA;AACR;AACD;AACF,CAAA;;;;"}