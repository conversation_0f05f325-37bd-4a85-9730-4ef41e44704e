import{createTranslatorFactory as r,ITSELF as t}from"@ucast/core";export*from"@ucast/core";import{MongoQueryParser as o,allParsingInstructions as n,defaultParsers as e}from"@ucast/mongo";export*from"@ucast/mongo";import{createJsInterpreter as f,allInterpreters as u,compare as c}from"@ucast/js";export*from"@ucast/js";function i(){return(i=Object.assign||function(r){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(r[n]=o[n])}return r}).apply(this,arguments)}function a(r){return null===r||"object"!=typeof r?r:r instanceof Date?r.getTime():r&&"function"==typeof r.toJSON?r.toJSON():r}var m=function(r,t){return c(a(r),a(t))};function p(n,e,u){var c=new o(n),a=f(e,i({compare:m},u));if(u&&u.forPrimitives){var p={field:t},s=c.parse;c.setParse((function(r){return s(r,p)}))}return r(c.parse,a)}var s=p(n,u),v=p(["$and","$or"].reduce((function(r,t){return r[t]=i({},r[t],{type:"field"}),r}),i({},n,{$nor:i({},n.$nor,{type:"field",parse:e.compound})})),u,{forPrimitives:!0}),j=s;export{p as createFactory,j as filter,s as guard,v as squire};
//# sourceMappingURL=index.js.map
