"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See LICENSE in the project root for license information.
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalStreamWritable = exports.NoOpTerminalProvider = exports.PrefixProxyTerminalProvider = exports.StringBufferTerminalProvider = exports.ConsoleTerminalProvider = exports.TerminalProviderSeverity = exports.Colorize = exports.Terminal = exports.AnsiEscape = exports.TextRewriterTransform = exports.TextRewriter = exports.TerminalWritable = exports.TerminalTransform = exports.StdioWritable = exports.StdioSummarizer = exports.StderrLineTransform = exports.SplitterTransform = exports.RemoveColorsTextRewriter = exports.PrintUtilities = exports.DEFAULT_CONSOLE_WIDTH = exports.NormalizeNewlinesTextRewriter = exports.MockWritable = exports.TerminalChunkKind = exports.DiscardStdoutTransform = exports.CallbackWritable = void 0;
/// <reference types="node" preserve="true" />
/**
 * This library implements a system for processing human readable text that
 * will be output by console applications.
 *
 * @remarks
 * See the {@link TerminalWritable} documentation for an overview of the major concepts.
 *
 * @packageDocumentation
 */
var CallbackWritable_1 = require("./CallbackWritable");
Object.defineProperty(exports, "CallbackWritable", { enumerable: true, get: function () { return CallbackWritable_1.CallbackWritable; } });
var DiscardStdoutTransform_1 = require("./DiscardStdoutTransform");
Object.defineProperty(exports, "DiscardStdoutTransform", { enumerable: true, get: function () { return DiscardStdoutTransform_1.DiscardStdoutTransform; } });
var ITerminalChunk_1 = require("./ITerminalChunk");
Object.defineProperty(exports, "TerminalChunkKind", { enumerable: true, get: function () { return ITerminalChunk_1.TerminalChunkKind; } });
var MockWritable_1 = require("./MockWritable");
Object.defineProperty(exports, "MockWritable", { enumerable: true, get: function () { return MockWritable_1.MockWritable; } });
var NormalizeNewlinesTextRewriter_1 = require("./NormalizeNewlinesTextRewriter");
Object.defineProperty(exports, "NormalizeNewlinesTextRewriter", { enumerable: true, get: function () { return NormalizeNewlinesTextRewriter_1.NormalizeNewlinesTextRewriter; } });
var PrintUtilities_1 = require("./PrintUtilities");
Object.defineProperty(exports, "DEFAULT_CONSOLE_WIDTH", { enumerable: true, get: function () { return PrintUtilities_1.DEFAULT_CONSOLE_WIDTH; } });
Object.defineProperty(exports, "PrintUtilities", { enumerable: true, get: function () { return PrintUtilities_1.PrintUtilities; } });
var RemoveColorsTextRewriter_1 = require("./RemoveColorsTextRewriter");
Object.defineProperty(exports, "RemoveColorsTextRewriter", { enumerable: true, get: function () { return RemoveColorsTextRewriter_1.RemoveColorsTextRewriter; } });
var SplitterTransform_1 = require("./SplitterTransform");
Object.defineProperty(exports, "SplitterTransform", { enumerable: true, get: function () { return SplitterTransform_1.SplitterTransform; } });
var StdioLineTransform_1 = require("./StdioLineTransform");
Object.defineProperty(exports, "StderrLineTransform", { enumerable: true, get: function () { return StdioLineTransform_1.StderrLineTransform; } });
var StdioSummarizer_1 = require("./StdioSummarizer");
Object.defineProperty(exports, "StdioSummarizer", { enumerable: true, get: function () { return StdioSummarizer_1.StdioSummarizer; } });
var StdioWritable_1 = require("./StdioWritable");
Object.defineProperty(exports, "StdioWritable", { enumerable: true, get: function () { return StdioWritable_1.StdioWritable; } });
var TerminalTransform_1 = require("./TerminalTransform");
Object.defineProperty(exports, "TerminalTransform", { enumerable: true, get: function () { return TerminalTransform_1.TerminalTransform; } });
var TerminalWritable_1 = require("./TerminalWritable");
Object.defineProperty(exports, "TerminalWritable", { enumerable: true, get: function () { return TerminalWritable_1.TerminalWritable; } });
var TextRewriter_1 = require("./TextRewriter");
Object.defineProperty(exports, "TextRewriter", { enumerable: true, get: function () { return TextRewriter_1.TextRewriter; } });
var TextRewriterTransform_1 = require("./TextRewriterTransform");
Object.defineProperty(exports, "TextRewriterTransform", { enumerable: true, get: function () { return TextRewriterTransform_1.TextRewriterTransform; } });
var AnsiEscape_1 = require("./AnsiEscape");
Object.defineProperty(exports, "AnsiEscape", { enumerable: true, get: function () { return AnsiEscape_1.AnsiEscape; } });
var Terminal_1 = require("./Terminal");
Object.defineProperty(exports, "Terminal", { enumerable: true, get: function () { return Terminal_1.Terminal; } });
var Colorize_1 = require("./Colorize");
Object.defineProperty(exports, "Colorize", { enumerable: true, get: function () { return Colorize_1.Colorize; } });
var ITerminalProvider_1 = require("./ITerminalProvider");
Object.defineProperty(exports, "TerminalProviderSeverity", { enumerable: true, get: function () { return ITerminalProvider_1.TerminalProviderSeverity; } });
var ConsoleTerminalProvider_1 = require("./ConsoleTerminalProvider");
Object.defineProperty(exports, "ConsoleTerminalProvider", { enumerable: true, get: function () { return ConsoleTerminalProvider_1.ConsoleTerminalProvider; } });
var StringBufferTerminalProvider_1 = require("./StringBufferTerminalProvider");
Object.defineProperty(exports, "StringBufferTerminalProvider", { enumerable: true, get: function () { return StringBufferTerminalProvider_1.StringBufferTerminalProvider; } });
var PrefixProxyTerminalProvider_1 = require("./PrefixProxyTerminalProvider");
Object.defineProperty(exports, "PrefixProxyTerminalProvider", { enumerable: true, get: function () { return PrefixProxyTerminalProvider_1.PrefixProxyTerminalProvider; } });
var NoOpTerminalProvider_1 = require("./NoOpTerminalProvider");
Object.defineProperty(exports, "NoOpTerminalProvider", { enumerable: true, get: function () { return NoOpTerminalProvider_1.NoOpTerminalProvider; } });
var TerminalStreamWritable_1 = require("./TerminalStreamWritable");
Object.defineProperty(exports, "TerminalStreamWritable", { enumerable: true, get: function () { return TerminalStreamWritable_1.TerminalStreamWritable; } });
//# sourceMappingURL=index.js.map