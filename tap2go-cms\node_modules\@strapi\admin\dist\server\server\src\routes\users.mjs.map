{"version": 3, "file": "users.mjs", "sources": ["../../../../../server/src/routes/users.ts"], "sourcesContent": ["export default [\n  {\n    method: 'GET',\n    path: '/users/me',\n    handler: 'authenticated-user.getMe',\n    config: {\n      policies: ['admin::isAuthenticatedAdmin'],\n    },\n  },\n  {\n    method: 'PUT',\n    path: '/users/me',\n    handler: 'authenticated-user.updateMe',\n    config: {\n      policies: ['admin::isAuthenticatedAdmin'],\n    },\n  },\n  {\n    method: 'GET',\n    path: '/users/me/permissions',\n    handler: 'authenticated-user.getOwnPermissions',\n    config: {\n      policies: ['admin::isAuthenticatedAdmin'],\n    },\n  },\n  {\n    method: 'POST',\n    path: '/users',\n    handler: 'user.create',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::users.create'] } },\n      ],\n    },\n  },\n  {\n    method: 'GET',\n    path: '/users',\n    handler: 'user.find',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::users.read'] } },\n      ],\n    },\n  },\n  {\n    method: 'GET',\n    path: '/users/:id',\n    handler: 'user.findOne',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::users.read'] } },\n      ],\n    },\n  },\n  {\n    method: 'PUT',\n    path: '/users/:id',\n    handler: 'user.update',\n    config: {\n      policies: [\n        'admin::isAuthenticatedAdmin',\n        { name: 'admin::hasPermissions', config: { actions: ['admin::users.update'] } },\n      ],\n    },\n  },\n  {\n    method: 'DELETE',\n    path: '/users/:id',\n    handler: 'user.deleteOne',\n    config: {\n      policies: [{ name: 'admin::hasPermissions', config: { actions: ['admin::users.delete'] } }],\n    },\n  },\n  {\n    method: 'POST',\n    path: '/users/batch-delete',\n    handler: 'user.deleteMany',\n    config: {\n      policies: [{ name: 'admin::hasPermissions', config: { actions: ['admin::users.delete'] } }],\n    },\n  },\n];\n"], "names": ["method", "path", "handler", "config", "policies", "name", "actions"], "mappings": "AAAA,YAAe;AACb,IAAA;QACEA,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,WAAA;QACNC,OAAS,EAAA,0BAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AAAC,gBAAA;AAA8B;AAC3C;AACF,KAAA;AACA,IAAA;QACEJ,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,WAAA;QACNC,OAAS,EAAA,6BAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AAAC,gBAAA;AAA8B;AAC3C;AACF,KAAA;AACA,IAAA;QACEJ,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,uBAAA;QACNC,OAAS,EAAA,sCAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AAAC,gBAAA;AAA8B;AAC3C;AACF,KAAA;AACA,IAAA;QACEJ,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,QAAA;QACNC,OAAS,EAAA,aAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAsB;AAAC;AAAE;AAC/E;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,QAAA;QACNC,OAAS,EAAA,WAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAoB;AAAC;AAAE;AAC7E;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,YAAA;QACNC,OAAS,EAAA,cAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAoB;AAAC;AAAE;AAC7E;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,KAAA;QACRC,IAAM,EAAA,YAAA;QACNC,OAAS,EAAA,aAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AACR,gBAAA,6BAAA;AACA,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAsB;AAAC;AAAE;AAC/E;AACH;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,QAAA;QACRC,IAAM,EAAA,YAAA;QACNC,OAAS,EAAA,gBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AAAC,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAsB;AAAC;AAAE;AAAE;AAC7F;AACF,KAAA;AACA,IAAA;QACEN,MAAQ,EAAA,MAAA;QACRC,IAAM,EAAA,qBAAA;QACNC,OAAS,EAAA,iBAAA;QACTC,MAAQ,EAAA;YACNC,QAAU,EAAA;AAAC,gBAAA;oBAAEC,IAAM,EAAA,uBAAA;oBAAyBF,MAAQ,EAAA;wBAAEG,OAAS,EAAA;AAAC,4BAAA;AAAsB;AAAC;AAAE;AAAE;AAC7F;AACF;CACD;;;;"}