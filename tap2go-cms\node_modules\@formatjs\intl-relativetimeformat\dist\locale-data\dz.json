{"data": {"dz": {"nu": ["tibt"], "year": {"0": "this year", "1": "next year", "future": {"other": "ལོ་འཁོར་ {0} ནང་"}, "past": {"other": "ལོ་འཁོར་ {0} ཧེ་མ་"}, "-1": "last year"}, "year-short": {"0": "this year", "1": "next year", "future": {"other": "ལོ་འཁོར་ {0} ནང་"}, "past": {"other": "ལོ་འཁོར་ {0} ཧེ་མ་"}, "-1": "last year"}, "year-narrow": {"0": "this year", "1": "next year", "future": {"other": "ལོ་འཁོར་ {0} ནང་"}, "past": {"other": "ལོ་འཁོར་ {0} ཧེ་མ་"}, "-1": "last year"}, "quarter": {"0": "this quarter", "1": "next quarter", "future": {"other": "+{0} Q"}, "past": {"other": "-{0} Q"}, "-1": "last quarter"}, "quarter-short": {"0": "this quarter", "1": "next quarter", "future": {"other": "+{0} Q"}, "past": {"other": "-{0} Q"}, "-1": "last quarter"}, "quarter-narrow": {"0": "this quarter", "1": "next quarter", "future": {"other": "+{0} Q"}, "past": {"other": "-{0} Q"}, "-1": "last quarter"}, "month": {"0": "this month", "1": "next month", "future": {"other": "ཟླཝ་ {0} ནང་"}, "past": {"other": "ཟླཝ་ {0} ཧེ་མ་"}, "-1": "last month"}, "month-short": {"0": "this month", "1": "next month", "future": {"other": "ཟླཝ་ {0} ནང་"}, "past": {"other": "ཟླཝ་ {0} ཧེ་མ་"}, "-1": "last month"}, "month-narrow": {"0": "this month", "1": "next month", "future": {"other": "ཟླཝ་ {0} ནང་"}, "past": {"other": "ཟླཝ་ {0} ཧེ་མ་"}, "-1": "last month"}, "week": {"0": "this week", "1": "next week", "future": {"other": "བངུན་ཕྲག་ {0} ནང་"}, "past": {"other": "བངུན་ཕྲག་ {0} ཧེ་མ་"}, "-1": "last week"}, "week-short": {"0": "this week", "1": "next week", "future": {"other": "བངུན་ཕྲག་ {0} ནང་"}, "past": {"other": "བངུན་ཕྲག་ {0} ཧེ་མ་"}, "-1": "last week"}, "week-narrow": {"0": "this week", "1": "next week", "future": {"other": "བངུན་ཕྲག་ {0} ནང་"}, "past": {"other": "བངུན་ཕྲག་ {0} ཧེ་མ་"}, "-1": "last week"}, "day": {"0": "ད་རིས་", "1": "ནངས་པ་", "2": "གནངས་ཚེ", "future": {"other": "ཉིནམ་ {0} ནང་"}, "past": {"other": "ཉིནམ་ {0} ཧེ་མ་"}, "-2": "ཁ་ཉིམ", "-1": "ཁ་ཙ་"}, "day-short": {"0": "ད་རིས་", "1": "ནངས་པ་", "2": "གནངས་ཚེ", "future": {"other": "ཉིནམ་ {0} ནང་"}, "past": {"other": "ཉིནམ་ {0} ཧེ་མ་"}, "-2": "ཁ་ཉིམ", "-1": "ཁ་ཙ་"}, "day-narrow": {"0": "ད་རིས་", "1": "ནངས་པ་", "2": "གནངས་ཚེ", "future": {"other": "ཉིནམ་ {0} ནང་"}, "past": {"other": "ཉིནམ་ {0} ཧེ་མ་"}, "-2": "ཁ་ཉིམ", "-1": "ཁ་ཙ་"}, "hour": {"0": "this hour", "future": {"other": "ཆུ་ཚོད་ {0} ནང་"}, "past": {"other": "ཆུ་ཚོད་ {0} ཧེ་མ་"}}, "hour-short": {"0": "this hour", "future": {"other": "ཆུ་ཚོད་ {0} ནང་"}, "past": {"other": "ཆུ་ཚོད་ {0} ཧེ་མ་"}}, "hour-narrow": {"0": "this hour", "future": {"other": "ཆུ་ཚོད་ {0} ནང་"}, "past": {"other": "ཆུ་ཚོད་ {0} ཧེ་མ་"}}, "minute": {"0": "this minute", "future": {"other": "སྐར་མ་ {0} ནང་"}, "past": {"other": "སྐར་མ་ {0} ཧེ་མ་"}}, "minute-short": {"0": "this minute", "future": {"other": "སྐར་མ་ {0} ནང་"}, "past": {"other": "སྐར་མ་ {0} ཧེ་མ་"}}, "minute-narrow": {"0": "this minute", "future": {"other": "སྐར་མ་ {0} ནང་"}, "past": {"other": "སྐར་མ་ {0} ཧེ་མ་"}}, "second": {"0": "now", "future": {"other": "སྐར་ཆ་ {0} ནང་"}, "past": {"other": "སྐར་ཆ་ {0} ཧེ་མ་"}}, "second-short": {"0": "now", "future": {"other": "སྐར་ཆ་ {0} ནང་"}, "past": {"other": "སྐར་ཆ་ {0} ཧེ་མ་"}}, "second-narrow": {"0": "now", "future": {"other": "སྐར་ཆ་ {0} ནང་"}, "past": {"other": "སྐར་ཆ་ {0} ཧེ་མ་"}}}}, "availableLocales": ["dz"], "aliases": {}, "parentLocales": {}}