{"version": 3, "file": "reset-user-password.js", "sources": ["../../../../../src/cli/commands/admin/reset-user-password.ts"], "sourcesContent": ["import _ from 'lodash';\nimport inquirer from 'inquirer';\nimport { createCommand } from 'commander';\nimport { createStrapi, compileStrapi } from '@strapi/core';\n\nimport type { StrapiCommand } from '../../types';\nimport { runAction } from '../../utils/helpers';\n\ninterface CmdOptions {\n  email?: string;\n  password?: string;\n}\n\ninterface Answers {\n  email: string;\n  password: string;\n  confirm: boolean;\n}\n\nconst promptQuestions: ReadonlyArray<inquirer.DistinctQuestion<Answers>> = [\n  { type: 'input', name: 'email', message: 'User email?' },\n  { type: 'password', name: 'password', message: 'New password?' },\n  {\n    type: 'confirm',\n    name: 'confirm',\n    message: \"Do you really want to reset this user's password?\",\n  },\n];\n\nasync function changePassword({ email, password }: CmdOptions) {\n  const appContext = await compileStrapi();\n  const app = await createStrapi(appContext).load();\n\n  await app.admin!.services.user.resetPasswordByEmail(email, password);\n\n  console.log(`Successfully reset user's password`);\n  process.exit(0);\n}\n\n/**\n * Reset user's password\n */\nconst action = async (cmdOptions: CmdOptions = {}) => {\n  const { email, password } = cmdOptions;\n\n  if (_.isEmpty(email) && _.isEmpty(password) && process.stdin.isTTY) {\n    const inquiry = await inquirer.prompt(promptQuestions);\n\n    if (!inquiry.confirm) {\n      process.exit(0);\n    }\n\n    return changePassword(inquiry);\n  }\n\n  if (_.isEmpty(email) || _.isEmpty(password)) {\n    console.error('Missing required options `email` or `password`');\n    process.exit(1);\n  }\n\n  return changePassword({ email, password });\n};\n\n/**\n * `$ strapi admin:reset-user-password`\n */\nconst command: StrapiCommand = () => {\n  return createCommand('admin:reset-user-password')\n    .alias('admin:reset-password')\n    .description(\"Reset an admin user's password\")\n    .option('-e, --email <email>', 'The user email')\n    .option('-p, --password <password>', 'New password for the user')\n    .action(runAction('admin:reset-user-password', action));\n};\n\nexport { action, command };\n"], "names": ["promptQuestions", "type", "name", "message", "changePassword", "email", "password", "appContext", "compileStrapi", "app", "createStrapi", "load", "admin", "services", "user", "resetPasswordByEmail", "console", "log", "process", "exit", "action", "cmdOptions", "_", "isEmpty", "stdin", "isTTY", "inquiry", "inquirer", "prompt", "confirm", "error", "command", "createCommand", "alias", "description", "option", "runAction"], "mappings": ";;;;;;;;AAmBA,MAAMA,eAAqE,GAAA;AACzE,IAAA;QAAEC,IAAM,EAAA,OAAA;QAASC,IAAM,EAAA,OAAA;QAASC,OAAS,EAAA;AAAc,KAAA;AACvD,IAAA;QAAEF,IAAM,EAAA,UAAA;QAAYC,IAAM,EAAA,UAAA;QAAYC,OAAS,EAAA;AAAgB,KAAA;AAC/D,IAAA;QACEF,IAAM,EAAA,SAAA;QACNC,IAAM,EAAA,SAAA;QACNC,OAAS,EAAA;AACX;AACD,CAAA;AAED,eAAeC,cAAe,CAAA,EAAEC,KAAK,EAAEC,QAAQ,EAAc,EAAA;AAC3D,IAAA,MAAMC,aAAa,MAAMC,kBAAAA,EAAAA;AACzB,IAAA,MAAMC,GAAM,GAAA,MAAMC,iBAAaH,CAAAA,UAAAA,CAAAA,CAAYI,IAAI,EAAA;IAE/C,MAAMF,GAAAA,CAAIG,KAAK,CAAEC,QAAQ,CAACC,IAAI,CAACC,oBAAoB,CAACV,KAAOC,EAAAA,QAAAA,CAAAA;AAE3DU,IAAAA,OAAAA,CAAQC,GAAG,CAAC,CAAC,kCAAkC,CAAC,CAAA;AAChDC,IAAAA,OAAAA,CAAQC,IAAI,CAAC,CAAA,CAAA;AACf;AAEA;;AAEC,IACKC,MAAAA,MAAAA,GAAS,OAAOC,UAAAA,GAAyB,EAAE,GAAA;AAC/C,IAAA,MAAM,EAAEhB,KAAK,EAAEC,QAAQ,EAAE,GAAGe,UAAAA;AAE5B,IAAA,IAAIC,CAAEC,CAAAA,OAAO,CAAClB,KAAAA,CAAAA,IAAUiB,CAAEC,CAAAA,OAAO,CAACjB,QAAAA,CAAAA,IAAaY,OAAQM,CAAAA,KAAK,CAACC,KAAK,EAAE;AAClE,QAAA,MAAMC,OAAU,GAAA,MAAMC,QAASC,CAAAA,MAAM,CAAC5B,eAAAA,CAAAA;QAEtC,IAAI,CAAC0B,OAAQG,CAAAA,OAAO,EAAE;AACpBX,YAAAA,OAAAA,CAAQC,IAAI,CAAC,CAAA,CAAA;AACf;AAEA,QAAA,OAAOf,cAAesB,CAAAA,OAAAA,CAAAA;AACxB;AAEA,IAAA,IAAIJ,EAAEC,OAAO,CAAClB,UAAUiB,CAAEC,CAAAA,OAAO,CAACjB,QAAW,CAAA,EAAA;AAC3CU,QAAAA,OAAAA,CAAQc,KAAK,CAAC,gDAAA,CAAA;AACdZ,QAAAA,OAAAA,CAAQC,IAAI,CAAC,CAAA,CAAA;AACf;AAEA,IAAA,OAAOf,cAAe,CAAA;AAAEC,QAAAA,KAAAA;AAAOC,QAAAA;AAAS,KAAA,CAAA;AAC1C;AAEA;;AAEC,UACKyB,OAAyB,GAAA,IAAA;AAC7B,IAAA,OAAOC,wBAAc,2BAClBC,CAAAA,CAAAA,KAAK,CAAC,sBACNC,CAAAA,CAAAA,WAAW,CAAC,gCACZC,CAAAA,CAAAA,MAAM,CAAC,qBAAuB,EAAA,gBAAA,CAAA,CAC9BA,MAAM,CAAC,2BAAA,EAA6B,6BACpCf,MAAM,CAACgB,kBAAU,2BAA6BhB,EAAAA,MAAAA,CAAAA,CAAAA;AACnD;;;;;"}