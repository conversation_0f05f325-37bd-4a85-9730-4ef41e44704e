{"version": 3, "file": "UseCasePage.js", "sources": ["../../../../../admin/src/pages/UseCasePage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Flex,\n  Main,\n  SingleSelectOption,\n  SingleSelect,\n  TextButton,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { parse } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nimport { PrivateRoute } from '../components/PrivateRoute';\nimport { Logo } from '../components/UnauthenticatedLogo';\nimport { useAuth } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { LayoutContent, UnauthenticatedLayout } from '../layouts/UnauthenticatedLayout';\n\nexport const options = [\n  {\n    intlLabel: {\n      id: 'Usecase.front-end',\n      defaultMessage: 'Front-end developer',\n    },\n    value: 'front_end_developer',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.back-end',\n      defaultMessage: 'Back-end developer',\n    },\n    value: 'back_end_developer',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.full-stack',\n      defaultMessage: 'Full-stack developer',\n    },\n    value: 'full_stack_developer',\n  },\n  {\n    intlLabel: {\n      id: 'global.content-manager',\n      defaultMessage: 'Content Manager',\n    },\n    value: 'content_manager',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.content-creator',\n      defaultMessage: 'Content Creator',\n    },\n    value: 'content_creator',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.other',\n      defaultMessage: 'Other',\n    },\n    value: 'other',\n  },\n];\n\nconst UseCasePage = () => {\n  const { toggleNotification } = useNotification();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const [role, setRole] = React.useState<string | number | null>(null);\n  const [otherRole, setOtherRole] = React.useState('');\n\n  const { firstname, email } = useAuth('UseCasePage', (state) => state.user) ?? {};\n  const { hasAdmin } = parse(location.search, { ignoreQueryPrefix: true });\n  const isOther = role === 'other';\n\n  const handleSubmit = async (event: React.FormEvent, skipPersona: boolean) => {\n    event.preventDefault();\n    try {\n      await fetch('https://analytics.strapi.io/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email,\n          username: firstname,\n          firstAdmin: Boolean(!hasAdmin),\n          persona: {\n            role: skipPersona ? undefined : role,\n            otherRole: skipPersona ? undefined : otherRole,\n          },\n        }),\n      });\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: 'Usecase.notification.success.project-created',\n          defaultMessage: 'Project has been successfully created',\n        }),\n      });\n      navigate('/');\n    } catch (err) {\n      // Silent\n    }\n  };\n\n  return (\n    <UnauthenticatedLayout>\n      <Main labelledBy=\"usecase-title\">\n        <LayoutContent>\n          <form onSubmit={(e) => handleSubmit(e, false)}>\n            <Flex direction=\"column\" paddingBottom={7}>\n              <Logo />\n              <Box paddingTop={6} paddingBottom={1} width={`25rem`}>\n                <Typography textAlign=\"center\" variant=\"alpha\" tag=\"h1\" id=\"usecase-title\">\n                  {formatMessage({\n                    id: 'Usecase.title',\n                    defaultMessage: 'Tell us a bit more about yourself',\n                  })}\n                </Typography>\n              </Box>\n            </Flex>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              <Field.Root name=\"usecase\">\n                <Field.Label>\n                  {formatMessage({\n                    id: 'Usecase.input.work-type',\n                    defaultMessage: 'What type of work do you do?',\n                  })}\n                </Field.Label>\n                <SingleSelect onChange={(value) => setRole(value)} value={role}>\n                  {options.map(({ intlLabel, value }) => (\n                    <SingleSelectOption key={value} value={value}>\n                      {formatMessage(intlLabel)}\n                    </SingleSelectOption>\n                  ))}\n                </SingleSelect>\n              </Field.Root>\n              {isOther && (\n                <Field.Root name=\"other\">\n                  <Field.Label>\n                    {formatMessage({ id: 'Usecase.other', defaultMessage: 'Other' })}\n                  </Field.Label>\n                  <TextInput value={otherRole} onChange={(e) => setOtherRole(e.target.value)} />\n                </Field.Root>\n              )}\n              <Button type=\"submit\" size=\"L\" fullWidth disabled={!role}>\n                {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n              </Button>\n            </Flex>\n          </form>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <TextButton\n              onClick={(event: React.MouseEvent<HTMLButtonElement>) => handleSubmit(event, true)}\n            >\n              {formatMessage({\n                id: 'Usecase.button.skip',\n                defaultMessage: 'Skip this question',\n              })}\n            </TextButton>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nconst PrivateUseCasePage = () => {\n  return (\n    <PrivateRoute>\n      <UseCasePage />\n    </PrivateRoute>\n  );\n};\n\nexport { PrivateUseCasePage, UseCasePage };\n"], "names": ["options", "intlLabel", "id", "defaultMessage", "value", "UseCasePage", "toggleNotification", "useNotification", "location", "useLocation", "navigate", "useNavigate", "formatMessage", "useIntl", "role", "setRole", "React", "useState", "otherRole", "setOtherRole", "firstname", "email", "useAuth", "state", "user", "has<PERSON>dmin", "parse", "search", "ignoreQueryPrefix", "isOther", "handleSubmit", "event", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "fetch", "method", "headers", "body", "JSON", "stringify", "username", "firstAdmin", "Boolean", "persona", "undefined", "type", "message", "err", "_jsx", "UnauthenticatedLayout", "_jsxs", "Main", "labelledBy", "LayoutContent", "form", "onSubmit", "e", "Flex", "direction", "paddingBottom", "Logo", "Box", "paddingTop", "width", "Typography", "textAlign", "variant", "tag", "alignItems", "gap", "Field", "Root", "name", "Label", "SingleSelect", "onChange", "map", "SingleSelectOption", "TextInput", "target", "<PERSON><PERSON>", "size", "fullWidth", "disabled", "justifyContent", "TextButton", "onClick", "PrivateUseCasePage", "PrivateRoute"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAwBaA,OAAU,GAAA;AACrB,IAAA;QACEC,SAAW,EAAA;YACTC,EAAI,EAAA,mBAAA;YACJC,cAAgB,EAAA;AAClB,SAAA;QACAC,KAAO,EAAA;AACT,KAAA;AACA,IAAA;QACEH,SAAW,EAAA;YACTC,EAAI,EAAA,kBAAA;YACJC,cAAgB,EAAA;AAClB,SAAA;QACAC,KAAO,EAAA;AACT,KAAA;AACA,IAAA;QACEH,SAAW,EAAA;YACTC,EAAI,EAAA,oBAAA;YACJC,cAAgB,EAAA;AAClB,SAAA;QACAC,KAAO,EAAA;AACT,KAAA;AACA,IAAA;QACEH,SAAW,EAAA;YACTC,EAAI,EAAA,wBAAA;YACJC,cAAgB,EAAA;AAClB,SAAA;QACAC,KAAO,EAAA;AACT,KAAA;AACA,IAAA;QACEH,SAAW,EAAA;YACTC,EAAI,EAAA,yBAAA;YACJC,cAAgB,EAAA;AAClB,SAAA;QACAC,KAAO,EAAA;AACT,KAAA;AACA,IAAA;QACEH,SAAW,EAAA;YACTC,EAAI,EAAA,eAAA;YACJC,cAAgB,EAAA;AAClB,SAAA;QACAC,KAAO,EAAA;AACT;;AAGF,MAAMC,WAAc,GAAA,IAAA;IAClB,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,6BAAAA,EAAAA;AAC/B,IAAA,MAAMC,QAAWC,GAAAA,0BAAAA,EAAAA;AACjB,IAAA,MAAMC,QAAWC,GAAAA,0BAAAA,EAAAA;IACjB,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;AAC1B,IAAA,MAAM,CAACC,IAAMC,EAAAA,OAAAA,CAAQ,GAAGC,gBAAAA,CAAMC,QAAQ,CAAyB,IAAA,CAAA;AAC/D,IAAA,MAAM,CAACC,SAAWC,EAAAA,YAAAA,CAAa,GAAGH,gBAAAA,CAAMC,QAAQ,CAAC,EAAA,CAAA;AAEjD,IAAA,MAAM,EAAEG,SAAS,EAAEC,KAAK,EAAE,GAAGC,YAAQ,CAAA,aAAA,EAAe,CAACC,KAAAA,GAAUA,KAAMC,CAAAA,IAAI,KAAK,EAAC;AAC/E,IAAA,MAAM,EAAEC,QAAQ,EAAE,GAAGC,QAAMlB,CAAAA,QAAAA,CAASmB,MAAM,EAAE;QAAEC,iBAAmB,EAAA;AAAK,KAAA,CAAA;AACtE,IAAA,MAAMC,UAAUf,IAAS,KAAA,OAAA;IAEzB,MAAMgB,YAAAA,GAAe,OAAOC,KAAwBC,EAAAA,WAAAA,GAAAA;AAClDD,QAAAA,KAAAA,CAAME,cAAc,EAAA;QACpB,IAAI;AACF,YAAA,MAAMC,MAAM,sCAAwC,EAAA;gBAClDC,MAAQ,EAAA,MAAA;gBACRC,OAAS,EAAA;oBACP,cAAgB,EAAA;AAClB,iBAAA;gBACAC,IAAMC,EAAAA,IAAAA,CAAKC,SAAS,CAAC;AACnBlB,oBAAAA,KAAAA;oBACAmB,QAAUpB,EAAAA,SAAAA;AACVqB,oBAAAA,UAAAA,EAAYC,QAAQ,CAACjB,QAAAA,CAAAA;oBACrBkB,OAAS,EAAA;AACP7B,wBAAAA,IAAAA,EAAMkB,cAAcY,SAAY9B,GAAAA,IAAAA;AAChCI,wBAAAA,SAAAA,EAAWc,cAAcY,SAAY1B,GAAAA;AACvC;AACF,iBAAA;AACF,aAAA,CAAA;YAEAZ,kBAAmB,CAAA;gBACjBuC,IAAM,EAAA,SAAA;AACNC,gBAAAA,OAAAA,EAASlC,aAAc,CAAA;oBACrBV,EAAI,EAAA,8CAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;AACF,aAAA,CAAA;YACAO,QAAS,CAAA,GAAA,CAAA;AACX,SAAA,CAAE,OAAOqC,GAAK,EAAA;;AAEd;AACF,KAAA;AAEA,IAAA,qBACEC,cAACC,CAAAA,2CAAAA,EAAAA;AACC,QAAA,QAAA,gBAAAC,eAACC,CAAAA,iBAAAA,EAAAA;YAAKC,UAAW,EAAA,eAAA;;8BACfJ,cAACK,CAAAA,mCAAAA,EAAAA;AACC,oBAAA,QAAA,gBAAAH,eAACI,CAAAA,MAAAA,EAAAA;wBAAKC,QAAU,EAAA,CAACC,CAAM1B,GAAAA,YAAAA,CAAa0B,CAAG,EAAA,KAAA,CAAA;;0CACrCN,eAACO,CAAAA,iBAAAA,EAAAA;gCAAKC,SAAU,EAAA,QAAA;gCAASC,aAAe,EAAA,CAAA;;kDACtCX,cAACY,CAAAA,wBAAAA,EAAAA,EAAAA,CAAAA;kDACDZ,cAACa,CAAAA,gBAAAA,EAAAA;wCAAIC,UAAY,EAAA,CAAA;wCAAGH,aAAe,EAAA,CAAA;wCAAGI,KAAO,EAAA,CAAC,KAAK,CAAC;AAClD,wCAAA,QAAA,gBAAAf,cAACgB,CAAAA,uBAAAA,EAAAA;4CAAWC,SAAU,EAAA,QAAA;4CAASC,OAAQ,EAAA,OAAA;4CAAQC,GAAI,EAAA,IAAA;4CAAKjE,EAAG,EAAA,eAAA;sDACxDU,aAAc,CAAA;gDACbV,EAAI,EAAA,eAAA;gDACJC,cAAgB,EAAA;AAClB,6CAAA;;;;;0CAIN+C,eAACO,CAAAA,iBAAAA,EAAAA;gCAAKC,SAAU,EAAA,QAAA;gCAASU,UAAW,EAAA,SAAA;gCAAUC,GAAK,EAAA,CAAA;;AACjD,kDAAAnB,eAAA,CAACoB,mBAAMC,IAAI,EAAA;wCAACC,IAAK,EAAA,SAAA;;AACf,0DAAAxB,cAAA,CAACsB,mBAAMG,KAAK,EAAA;0DACT7D,aAAc,CAAA;oDACbV,EAAI,EAAA,yBAAA;oDACJC,cAAgB,EAAA;AAClB,iDAAA;;0DAEF6C,cAAC0B,CAAAA,yBAAAA,EAAAA;gDAAaC,QAAU,EAAA,CAACvE,QAAUW,OAAQX,CAAAA,KAAAA,CAAAA;gDAAQA,KAAOU,EAAAA,IAAAA;0DACvDd,OAAQ4E,CAAAA,GAAG,CAAC,CAAC,EAAE3E,SAAS,EAAEG,KAAK,EAAE,iBAChC4C,cAAC6B,CAAAA,+BAAAA,EAAAA;wDAA+BzE,KAAOA,EAAAA,KAAAA;kEACpCQ,aAAcX,CAAAA,SAAAA;AADQG,qDAAAA,EAAAA,KAAAA,CAAAA;;;;oCAM9ByB,OACC,kBAAAqB,eAAA,CAACoB,mBAAMC,IAAI,EAAA;wCAACC,IAAK,EAAA,OAAA;;AACf,0DAAAxB,cAAA,CAACsB,mBAAMG,KAAK,EAAA;0DACT7D,aAAc,CAAA;oDAAEV,EAAI,EAAA,eAAA;oDAAiBC,cAAgB,EAAA;AAAQ,iDAAA;;0DAEhE6C,cAAC8B,CAAAA,sBAAAA,EAAAA;gDAAU1E,KAAOc,EAAAA,SAAAA;AAAWyD,gDAAAA,QAAAA,EAAU,CAACnB,CAAMrC,GAAAA,YAAAA,CAAaqC,CAAEuB,CAAAA,MAAM,CAAC3E,KAAK;;;;kDAG7E4C,cAACgC,CAAAA,mBAAAA,EAAAA;wCAAOnC,IAAK,EAAA,QAAA;wCAASoC,IAAK,EAAA,GAAA;wCAAIC,SAAS,EAAA,IAAA;AAACC,wCAAAA,QAAAA,EAAU,CAACrE,IAAAA;kDACjDF,aAAc,CAAA;4CAAEV,EAAI,EAAA,eAAA;4CAAiBC,cAAgB,EAAA;AAAS,yCAAA;;;;;;;8BAKvE6C,cAACS,CAAAA,iBAAAA,EAAAA;oBAAK2B,cAAe,EAAA,QAAA;AACnB,oBAAA,QAAA,gBAAApC,cAACa,CAAAA,gBAAAA,EAAAA;wBAAIC,UAAY,EAAA,CAAA;AACf,wBAAA,QAAA,gBAAAd,cAACqC,CAAAA,uBAAAA,EAAAA;4BACCC,OAAS,EAAA,CAACvD,KAA+CD,GAAAA,YAAAA,CAAaC,KAAO,EAAA,IAAA,CAAA;sCAE5EnB,aAAc,CAAA;gCACbV,EAAI,EAAA,qBAAA;gCACJC,cAAgB,EAAA;AAClB,6BAAA;;;;;;;AAOd;AAEA,MAAMoF,kBAAqB,GAAA,IAAA;AACzB,IAAA,qBACEvC,cAACwC,CAAAA,yBAAAA,EAAAA;AACC,QAAA,QAAA,gBAAAxC,cAAC3C,CAAAA,WAAAA,EAAAA,EAAAA;;AAGP;;;;;;"}