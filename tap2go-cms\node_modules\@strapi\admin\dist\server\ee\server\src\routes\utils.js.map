{"version": 3, "file": "utils.js", "sources": ["../../../../../../ee/server/src/routes/utils.ts"], "sourcesContent": ["import type { Core } from '@strapi/types';\n\nexport const enableFeatureMiddleware =\n  (featureName: string): Core.MiddlewareHandler =>\n  (ctx, next) => {\n    if (strapi.ee.features.isEnabled(featureName)) {\n      return next();\n    }\n\n    ctx.status = 404;\n  };\n"], "names": ["enableFeatureMiddleware", "featureName", "ctx", "next", "strapi", "ee", "features", "isEnabled", "status"], "mappings": ";;AAEaA,MAAAA,uBAAAA,GACX,CAACC,WAAAA,GACD,CAACC,GAAKC,EAAAA,IAAAA,GAAAA;AACJ,QAAA,IAAIC,OAAOC,EAAE,CAACC,QAAQ,CAACC,SAAS,CAACN,WAAc,CAAA,EAAA;YAC7C,OAAOE,IAAAA,EAAAA;AACT;AAEAD,QAAAA,GAAAA,CAAIM,MAAM,GAAG,GAAA;;;;;"}