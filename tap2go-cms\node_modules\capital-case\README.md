# Capital Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transform into a space separated string with each word capitalized.

## Installation

```
npm install capital-case --save
```

## Usage

```js
import { capitalCase } from "capital-case";

capitalCase("string"); //=> "String"
capitalCase("dot.case"); //=> "Dot Case"
capitalCase("PascalCase"); //=> "Pascal Case"
capitalCase("version 1.2.10"); //=> "Version 1 2 10"
```

The function also accepts [`options`](https://github.com/blakeembrey/change-case#options).

## License

MIT

[npm-image]: https://img.shields.io/npm/v/capital-case.svg?style=flat
[npm-url]: https://npmjs.org/package/capital-case
[downloads-image]: https://img.shields.io/npm/dm/capital-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/capital-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/capital-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=capital-case
