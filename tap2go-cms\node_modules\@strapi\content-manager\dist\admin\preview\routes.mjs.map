{"version": 3, "file": "routes.mjs", "sources": ["../../../admin/src/preview/routes.tsx"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport type { PathRouteProps } from 'react-router-dom';\n\nconst ProtectedPreviewPage = React.lazy(() =>\n  import('./pages/Preview').then((mod) => ({ default: mod.ProtectedPreviewPage }))\n);\n\nconst routes: PathRouteProps[] = [\n  {\n    path: ':collectionType/:slug/:id/preview',\n    Component: ProtectedPreviewPage,\n  },\n  {\n    path: ':collectionType/:slug/preview',\n    Component: ProtectedPreviewPage,\n  },\n];\n\nexport { routes };\n"], "names": ["ProtectedPreviewPage", "React", "lazy", "then", "mod", "default", "routes", "path", "Component"], "mappings": ";;AAKA,MAAMA,oBAAuBC,iBAAAA,KAAAA,CAAMC,IAAI,CAAC,IACtC,OAAO,qBAAA,CAAA,CAAmBC,IAAI,CAAC,CAACC,GAAAA,IAAS;AAAEC,YAAAA,OAAAA,EAASD,IAAIJ;SAAqB,CAAA,CAAA,CAAA;AAG/E,MAAMM,MAA2B,GAAA;AAC/B,IAAA;QACEC,IAAM,EAAA,mCAAA;QACNC,SAAWR,EAAAA;AACb,KAAA;AACA,IAAA;QACEO,IAAM,EAAA,+BAAA;QACNC,SAAWR,EAAAA;AACb;AACD;;;;"}