{"version": 3, "file": "fix-eperm.js", "sourceRoot": "", "sources": ["../../src/fix-eperm.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAA;AAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAA;AAE1B,MAAM,CAAC,MAAM,QAAQ,GACnB,CAAC,EAAkC,EAAE,EAAE,CAAC,KAAK,EAAE,IAAY,EAAE,EAAE;IAC7D,IAAI;QACF,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAA;KACtB;IAAC,OAAO,EAAE,EAAE;QACX,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,IAAI,GAAG,EAAE,IAAI,KAAK,QAAQ,EAAE;YAC1B,OAAM;SACP;QACD,IAAI,GAAG,EAAE,IAAI,KAAK,OAAO,EAAE;YACzB,IAAI;gBACF,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;aACzB;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,IAAI,GAAG,GAA4B,CAAA;gBACzC,IAAI,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE;oBAC3B,OAAM;iBACP;gBACD,MAAM,EAAE,CAAA;aACT;YACD,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAA;SACtB;QACD,MAAM,EAAE,CAAA;KACT;AACH,CAAC,CAAA;AAEH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,EAAyB,EAAE,EAAE,CAAC,CAAC,IAAY,EAAE,EAAE;IAC1E,IAAI;QACF,OAAO,EAAE,CAAC,IAAI,CAAC,CAAA;KAChB;IAAC,OAAO,EAAE,EAAE;QACX,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,IAAI,GAAG,EAAE,IAAI,KAAK,QAAQ,EAAE;YAC1B,OAAM;SACP;QACD,IAAI,GAAG,EAAE,IAAI,KAAK,OAAO,EAAE;YACzB,IAAI;gBACF,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;aACvB;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,IAAI,GAAG,GAA4B,CAAA;gBACzC,IAAI,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE;oBAC3B,OAAM;iBACP;gBACD,MAAM,EAAE,CAAA;aACT;YACD,OAAO,EAAE,CAAC,IAAI,CAAC,CAAA;SAChB;QACD,MAAM,EAAE,CAAA;KACT;AACH,CAAC,CAAA", "sourcesContent": ["import { chmodSync, promises } from './fs.js'\nconst { chmod } = promises\n\nexport const fixEPERM =\n  (fn: (path: string) => Promise<any>) => async (path: string) => {\n    try {\n      return await fn(path)\n    } catch (er) {\n      const fer = er as NodeJS.ErrnoException\n      if (fer?.code === 'ENOENT') {\n        return\n      }\n      if (fer?.code === 'EPERM') {\n        try {\n          await chmod(path, 0o666)\n        } catch (er2) {\n          const fer2 = er2 as NodeJS.ErrnoException\n          if (fer2?.code === 'ENOENT') {\n            return\n          }\n          throw er\n        }\n        return await fn(path)\n      }\n      throw er\n    }\n  }\n\nexport const fixEPERMSync = (fn: (path: string) => any) => (path: string) => {\n  try {\n    return fn(path)\n  } catch (er) {\n    const fer = er as NodeJS.ErrnoException\n    if (fer?.code === 'ENOENT') {\n      return\n    }\n    if (fer?.code === 'EPERM') {\n      try {\n        chmodSync(path, 0o666)\n      } catch (er2) {\n        const fer2 = er2 as NodeJS.ErrnoException\n        if (fer2?.code === 'ENOENT') {\n          return\n        }\n        throw er\n      }\n      return fn(path)\n    }\n    throw er\n  }\n}\n"]}