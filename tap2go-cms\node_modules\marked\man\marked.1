.ds q \N'34'
.TH marked 1

.SH NAME
marked \- a javascript markdown parser

.SH SYNOPSIS
.B marked
[\-o \fI<output>\fP] [\-i \fI<input>\fP] [\-s \fI<string>\fP] [\-\-help]
[\-\-tokens] [\-\-pedantic] [\-\-gfm]
[\-\-breaks] [\-\-sanitize]
[\-\-smart\-lists] [\-\-lang\-prefix \fI<prefix>\fP]
[\-\-no\-etc...] [\-\-silent] [\fIfilename\fP]

.SH DESCRIPTION
.B marked
is a full-featured javascript markdown parser, built for speed.
It also includes multiple GFM features.

.SH EXAMPLES
.TP
cat in.md | marked > out.html
.TP
echo "hello *world*" | marked
.TP
marked \-o out.html \-i in.md \-\-gfm
.TP
marked \-\-output="hello world.html" \-i in.md \-\-no-breaks

.SH OPTIONS
.TP
.BI \-o,\ \-\-output\ [\fIoutput\fP]
Specify file output. If none is specified, write to stdout.
.TP
.BI \-i,\ \-\-input\ [\fIinput\fP]
Specify file input, otherwise use last argument as input file.
If no input file is specified, read from stdin.
.TP
.BI \-s,\ \-\-string\ [\fIstring\fP]
Specify string input instead of a file.
.TP
.BI \-t,\ \-\-tokens
Output a token stream instead of html.
.TP
.BI \-\-pedantic
Conform to obscure parts of markdown.pl as much as possible.
Don't fix original markdown bugs.
.TP
.BI \-\-gfm
Enable github flavored markdown.
.TP
.BI \-\-breaks
Enable GFM line breaks. Only works with the gfm option.
.TP
.BI \-\-sanitize
Sanitize output. Ignore any HTML input.
.TP
.BI \-\-smart\-lists
Use smarter list behavior than the original markdown.
.TP
.BI \-\-lang\-prefix\ [\fIprefix\fP]
Set the prefix for code block classes.
.TP
.BI \-\-mangle
Mangle email addresses.
.TP
.BI \-\-no\-sanitize,\ \-no-etc...
The inverse of any of the marked options above.
.TP
.BI \-\-silent
Silence error output.
.TP
.BI \-h,\ \-\-help
Display help information.

.SH CONFIGURATION
For configuring and running programmatically.

.B Example

    import { marked } from 'marked';
    marked('*foo*', { gfm: true });

.SH BUGS
Please report any bugs to https://github.com/markedjs/marked.

.SH LICENSE
Copyright (c) 2011-2014, Christopher Jeffrey (MIT License).

.SH "SEE ALSO"
.BR markdown(1),
.BR node.js(1)
