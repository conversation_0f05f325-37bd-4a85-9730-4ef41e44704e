import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/hybrid-client';

/**
 * Test Database Connection API Route
 * Simple test to verify database connectivity and Prisma client
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test 1: Direct SQL query
    const sqlTest = await db.sql('SELECT NOW() as current_time, version() as db_version');
    console.log('✅ Direct SQL test passed:', sqlTest[0]);
    
    // Test 2: Check if blog_posts table exists
    const tableCheck = await db.sql(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'blog_posts'
    `);
    console.log('✅ Table check:', tableCheck);
    
    // Test 3: Try to count existing blog posts
    const countTest = await db.sql('SELECT COUNT(*) as total FROM blog_posts');
    console.log('✅ Count test:', countTest[0]);
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      tests: {
        sql_connection: true,
        table_exists: tableCheck.length > 0,
        current_posts: parseInt(countTest[0].total),
        database_time: sqlTest[0].current_time,
        database_version: sqlTest[0].db_version
      }
    });

  } catch (error: any) {
    console.error('❌ Database test failed:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Database connection failed',
      error: error.name || 'DatabaseError'
    }, { status: 500 });
  }
}

/**
 * Test Blog Post Creation
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing blog post creation...');
    
    const testPost = {
      title: 'Test Post - ' + new Date().toISOString(),
      content: 'This is a test blog post created to verify the CMS functionality.',
      excerpt: 'Test post excerpt',
      authorName: 'Test Admin',
      status: 'draft'
    };
    
    // Test using direct SQL first
    const sqlResult = await db.sql(`
      INSERT INTO blog_posts (title, slug, content, excerpt, author_name, status, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING id, title, slug, status, created_at
    `, [
      testPost.title,
      testPost.title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, ''),
      testPost.content,
      testPost.excerpt,
      testPost.authorName,
      testPost.status
    ]);
    
    console.log('✅ Direct SQL insert successful:', sqlResult[0]);
    
    return NextResponse.json({
      success: true,
      message: 'Test blog post created successfully',
      method: 'direct_sql',
      post: sqlResult[0]
    });

  } catch (error: any) {
    console.error('❌ Blog post creation test failed:', error);
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create test blog post',
      error: error.name || 'CreateError'
    }, { status: 500 });
  }
}
