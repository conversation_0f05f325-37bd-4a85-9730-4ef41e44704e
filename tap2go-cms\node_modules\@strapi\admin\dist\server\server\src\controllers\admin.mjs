import path from 'path';
import { pipe, map, flatMap, values, sumBy, propEq } from 'lodash/fp';
import ___default from 'lodash';
import { exists } from 'fs-extra';
import '@strapi/types';
import { env } from '@strapi/utils';
import tsUtils from '@strapi/typescript-utils';
import { validateUpdateProjectSettings, validateUpdateProjectSettingsFiles, validateUpdateProjectSettingsImagesDimensions } from '../validation/project-settings.mjs';
import { getService } from '../utils/index.mjs';

const { isUsingTypeScript } = tsUtils;
/**
 * A set of functions called "actions" for `Admin`
 */ var admin = {
    // TODO very temporary to check the switch ee/ce
    // When removing this we need to update the /admin/src/index.js file
    // whe,re we set the strapi.window.isEE value
    // NOTE: admin/ee/server overrides this controller, and adds the EE features
    // This returns an empty feature list for CE
    async getProjectType () {
        const flags = strapi.config.get('admin.flags', {});
        return {
            data: {
                isEE: false,
                features: [],
                flags
            }
        };
    },
    async init () {
        let uuid = strapi.config.get('uuid', false);
        const hasAdmin = await getService('user').exists();
        const { menuLogo, authLogo } = await getService('project-settings').getProjectSettings();
        // set to null if telemetryDisabled flag not avaialble in package.json
        const telemetryDisabled = strapi.config.get('packageJsonStrapi.telemetryDisabled', null);
        if (telemetryDisabled !== null && telemetryDisabled === true) {
            uuid = false;
        }
        return {
            data: {
                uuid,
                hasAdmin,
                menuLogo: menuLogo ? menuLogo.url : null,
                authLogo: authLogo ? authLogo.url : null
            }
        };
    },
    async getProjectSettings () {
        return getService('project-settings').getProjectSettings();
    },
    async updateProjectSettings (ctx) {
        const { request: { files, body } } = ctx;
        const projectSettingsService = getService('project-settings');
        await validateUpdateProjectSettings(body);
        await validateUpdateProjectSettingsFiles(files);
        const formatedFiles = await projectSettingsService.parseFilesData(files);
        await validateUpdateProjectSettingsImagesDimensions(formatedFiles);
        return projectSettingsService.updateProjectSettings({
            ...body,
            ...formatedFiles
        });
    },
    async telemetryProperties (ctx) {
        // If the telemetry is disabled, ignore the request and return early
        if (strapi.telemetry.isDisabled) {
            ctx.status = 204;
            return;
        }
        const useTypescriptOnServer = await isUsingTypeScript(strapi.dirs.app.root);
        const useTypescriptOnAdmin = await isUsingTypeScript(path.join(strapi.dirs.app.root, 'src', 'admin'));
        const isHostedOnStrapiCloud = env('STRAPI_HOSTING', null) === 'strapi.cloud';
        const numberOfAllContentTypes = ___default.size(strapi.contentTypes);
        const numberOfComponents = ___default.size(strapi.components);
        const getNumberOfDynamicZones = ()=>{
            return pipe(map('attributes'), flatMap(values), // @ts-expect-error lodash types
            sumBy(propEq('type', 'dynamiczone')))(strapi.contentTypes);
        };
        return {
            data: {
                useTypescriptOnServer,
                useTypescriptOnAdmin,
                isHostedOnStrapiCloud,
                numberOfAllContentTypes,
                numberOfComponents,
                numberOfDynamicZones: getNumberOfDynamicZones()
            }
        };
    },
    async information () {
        const currentEnvironment = strapi.config.get('environment');
        const autoReload = strapi.config.get('autoReload', false);
        const strapiVersion = strapi.config.get('info.strapi', null);
        const dependencies = strapi.config.get('info.dependencies', {});
        const projectId = strapi.config.get('uuid', null);
        const nodeVersion = process.version;
        const communityEdition = !strapi.EE;
        const useYarn = await exists(path.join(process.cwd(), 'yarn.lock'));
        return {
            data: {
                currentEnvironment,
                autoReload,
                strapiVersion,
                dependencies,
                projectId,
                nodeVersion,
                communityEdition,
                useYarn
            }
        };
    },
    async plugins (ctx) {
        const enabledPlugins = strapi.config.get('enabledPlugins');
        // List of core plugins that are always enabled,
        // and so it's not necessary to display them in the plugins list
        const CORE_PLUGINS = [
            'content-manager',
            'content-type-builder',
            'email',
            'upload',
            'i18n',
            'content-releases',
            'review-workflows'
        ];
        const plugins = Object.entries(enabledPlugins).filter(([key])=>!CORE_PLUGINS.includes(key)).map(([key, plugin])=>({
                name: plugin.info.name || key,
                displayName: plugin.info.displayName || plugin.info.name || key,
                description: plugin.info.description || '',
                packageName: plugin.info.packageName
            }));
        ctx.send({
            plugins
        });
    },
    async licenseTrialTimeLeft () {
        const data = await strapi.ee.getTrialEndDate({
            strapi
        });
        return data;
    }
};

export { admin as default };
//# sourceMappingURL=admin.mjs.map
