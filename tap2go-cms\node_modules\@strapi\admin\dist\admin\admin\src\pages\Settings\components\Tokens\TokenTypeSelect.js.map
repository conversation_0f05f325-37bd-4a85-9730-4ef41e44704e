{"version": 3, "file": "TokenTypeSelect.js", "sources": ["../../../../../../../../admin/src/pages/Settings/components/Tokens/TokenTypeSelect.tsx"], "sourcesContent": ["import { SingleSelectOption, SingleSelect, SingleSelectProps, Field } from '@strapi/design-system';\nimport { MessageDescriptor, useIntl } from 'react-intl';\n\nimport { isErrorMessageMessageDescriptor } from '../../utils/forms';\n\ninterface TokenTypeSelectProps extends Pick<SingleSelectProps, 'onChange' | 'value'> {\n  name?: string;\n  options: Array<{\n    label: MessageDescriptor;\n    value: string;\n  }>;\n  error?: string | MessageDescriptor;\n  canEditInputs: boolean;\n  label: MessageDescriptor;\n}\n\nexport const TokenTypeSelect = ({\n  name = 'type',\n  error,\n  value,\n  onChange,\n  canEditInputs,\n  options = [],\n  label,\n}: TokenTypeSelectProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Field.Root\n      error={\n        error\n          ? formatMessage(\n              isErrorMessageMessageDescriptor(error) ? error : { id: error, defaultMessage: error }\n            )\n          : undefined\n      }\n      name={name}\n      required\n    >\n      <Field.Label>\n        {formatMessage({\n          id: label.id,\n          defaultMessage: label.defaultMessage,\n        })}\n      </Field.Label>\n      <SingleSelect\n        value={value}\n        onChange={onChange}\n        placeholder=\"Select\"\n        disabled={!canEditInputs}\n      >\n        {options &&\n          options.map(({ value, label }) => (\n            <SingleSelectOption key={value} value={value}>\n              {formatMessage(label)}\n            </SingleSelectOption>\n          ))}\n      </SingleSelect>\n      <Field.Error />\n    </Field.Root>\n  );\n};\n"], "names": ["TokenTypeSelect", "name", "error", "value", "onChange", "canEditInputs", "options", "label", "formatMessage", "useIntl", "_jsxs", "Field", "Root", "isErrorMessageMessageDescriptor", "id", "defaultMessage", "undefined", "required", "_jsx", "Label", "SingleSelect", "placeholder", "disabled", "map", "SingleSelectOption", "Error"], "mappings": ";;;;;;;MAgBaA,eAAkB,GAAA,CAAC,EAC9BC,IAAO,GAAA,MAAM,EACbC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,aAAa,EACbC,UAAU,EAAE,EACZC,KAAK,EACgB,GAAA;IACrB,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;IAE1B,qBACEC,eAAA,CAACC,mBAAMC,IAAI,EAAA;AACTV,QAAAA,KAAAA,EACEA,KACIM,GAAAA,aAAAA,CACEK,qCAAgCX,CAAAA,KAAAA,CAAAA,GAASA,KAAQ,GAAA;YAAEY,EAAIZ,EAAAA,KAAAA;YAAOa,cAAgBb,EAAAA;SAEhFc,CAAAA,GAAAA,SAAAA;QAENf,IAAMA,EAAAA,IAAAA;QACNgB,QAAQ,EAAA,IAAA;;AAER,0BAAAC,cAAA,CAACP,mBAAMQ,KAAK,EAAA;0BACTX,aAAc,CAAA;AACbM,oBAAAA,EAAAA,EAAIP,MAAMO,EAAE;AACZC,oBAAAA,cAAAA,EAAgBR,MAAMQ;AACxB,iBAAA;;0BAEFG,cAACE,CAAAA,yBAAAA,EAAAA;gBACCjB,KAAOA,EAAAA,KAAAA;gBACPC,QAAUA,EAAAA,QAAAA;gBACViB,WAAY,EAAA,QAAA;AACZC,gBAAAA,QAAAA,EAAU,CAACjB,aAAAA;0BAEVC,OACCA,IAAAA,OAAAA,CAAQiB,GAAG,CAAC,CAAC,EAAEpB,KAAK,EAAEI,KAAK,EAAE,iBAC3BW,cAACM,CAAAA,+BAAAA,EAAAA;wBAA+BrB,KAAOA,EAAAA,KAAAA;kCACpCK,aAAcD,CAAAA,KAAAA;AADQJ,qBAAAA,EAAAA,KAAAA,CAAAA;;AAK/B,0BAAAe,cAAA,CAACP,mBAAMc,KAAK,EAAA,EAAA;;;AAGlB;;;;"}