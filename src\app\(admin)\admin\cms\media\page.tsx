'use client';

import { useState, useEffect } from 'react';
import { PhotoIcon, CloudArrowUpIcon, FolderIcon } from '@heroicons/react/24/outline';

export default function MediaLibrary() {
  const [loading, setLoading] = useState(false);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <PhotoIcon className="w-8 h-8 text-orange-500 mr-3" />
                Media Library
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage images and files with Cloudinary CDN
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm text-gray-600">
                  Cloudinary Connected
                </span>
              </div>
              <button className="bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-orange-700 transition-colors">
                <CloudArrowUpIcon className="w-5 h-5" />
                <span>Upload Media</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Media Grid */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Media Files</h2>
        </div>
        
        <div className="p-8 text-center">
          <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">Media library integration coming soon</p>
          <p className="text-sm text-gray-400">
            This will integrate with Cloudinary for professional media management
          </p>
        </div>
      </div>

      {/* Cloudinary Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Cloudinary Integration</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <span className="text-sm text-gray-600">CDN Status</span>
            <span className="text-sm text-green-600 font-medium">✅ Active</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <span className="text-sm text-gray-600">Cloud Name</span>
            <span className="text-sm text-blue-600 font-medium">dpekh75yi</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
            <span className="text-sm text-gray-600">Upload Presets</span>
            <span className="text-sm text-purple-600 font-medium">5 Configured</span>
          </div>
        </div>
      </div>
    </div>
  );
}
