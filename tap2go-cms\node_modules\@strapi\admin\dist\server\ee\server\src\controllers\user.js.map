{"version": 3, "file": "user.js", "sources": ["../../../../../../ee/server/src/controllers/user.ts"], "sourcesContent": ["import type { Context } from 'koa';\n\nimport _ from 'lodash';\nimport { pick, isNil } from 'lodash/fp';\nimport { errors } from '@strapi/utils';\nimport { validateUserCreationInput } from '../validation/user';\nimport { validateUserUpdateInput } from '../../../../server/src/validation/user';\nimport { getService } from '../utils';\nimport { isSsoLocked } from '../utils/sso-lock';\n\nconst { ApplicationError, ForbiddenError } = errors;\n\nconst pickUserCreationAttributes = pick(['firstname', 'lastname', 'email', 'roles']);\n\nconst hasAdminSeatsAvaialble = async () => {\n  if (!strapi.EE) {\n    return true;\n  }\n\n  const permittedSeats = strapi.ee.seats as any;\n  if (isNil(permittedSeats)) {\n    return true;\n  }\n\n  const userCount = await strapi.service('admin::user').getCurrentActiveUserCount();\n\n  if (userCount < permittedSeats) {\n    return true;\n  }\n};\n\nexport default {\n  async create(ctx: Context) {\n    if (!(await hasAdminSeatsAvaialble())) {\n      throw new ForbiddenError('License seat limit reached. You cannot create a new user');\n    }\n\n    const { body } = ctx.request;\n    const cleanData = { ...body, email: _.get(body, `email`, ``).toLowerCase() };\n\n    await validateUserCreationInput(cleanData);\n\n    const attributes = pickUserCreationAttributes(cleanData);\n    const { useSSORegistration } = cleanData;\n\n    const userAlreadyExists = await getService('user').exists({ email: attributes.email });\n\n    if (userAlreadyExists) {\n      throw new ApplicationError('Email already taken');\n    }\n\n    if (useSSORegistration) {\n      Object.assign(attributes, { registrationToken: null, isActive: true });\n    }\n\n    const createdUser = await getService('user').create(attributes);\n    const userInfo = getService('user').sanitizeUser(createdUser);\n\n    // Note: We need to assign manually the registrationToken to the\n    // final user payload so that it's not removed in the sanitation process.\n    Object.assign(userInfo, { registrationToken: createdUser.registrationToken });\n\n    ctx.created({ data: userInfo });\n  },\n\n  async update(ctx: Context) {\n    const { id } = ctx.params;\n    const { body: input } = ctx.request;\n\n    await validateUserUpdateInput(input);\n\n    if (_.has(input, 'email')) {\n      const uniqueEmailCheck = await getService('user').exists({\n        id: { $ne: id },\n        email: input.email,\n      });\n\n      if (uniqueEmailCheck) {\n        throw new ApplicationError('A user with this email address already exists');\n      }\n    }\n\n    const user = await getService('user').findOne(id, null);\n\n    if (!(await hasAdminSeatsAvaialble()) && !user.isActive && input.isActive) {\n      throw new ForbiddenError('License seat limit reached. You cannot active this user');\n    }\n\n    const updatedUser = await getService('user').updateById(id, input);\n\n    if (!updatedUser) {\n      return ctx.notFound('User does not exist');\n    }\n\n    ctx.body = {\n      data: getService('user').sanitizeUser(updatedUser),\n    };\n  },\n\n  async isSSOLocked(ctx: Context) {\n    const { user } = ctx.state;\n    const isSSOLocked = await isSsoLocked(user);\n\n    ctx.body = {\n      data: {\n        isSSOLocked,\n      },\n    };\n  },\n};\n"], "names": ["ApplicationError", "ForbiddenError", "errors", "pickUserCreationAttributes", "pick", "hasAdminSeatsAvaialble", "strapi", "EE", "permittedSeats", "ee", "seats", "isNil", "userCount", "service", "getCurrentActiveUserCount", "create", "ctx", "body", "request", "cleanData", "email", "_", "get", "toLowerCase", "validateUserCreationInput", "attributes", "useSSORegistration", "userAlreadyExists", "getService", "exists", "Object", "assign", "registrationToken", "isActive", "created<PERSON>ser", "userInfo", "sanitizeUser", "created", "data", "update", "id", "params", "input", "validateUserUpdateInput", "has", "uniqueEmailCheck", "$ne", "user", "findOne", "updatedUser", "updateById", "notFound", "isSSOLocked", "state", "isSsoLocked"], "mappings": ";;;;;;;;;;AAUA,MAAM,EAAEA,gBAAgB,EAAEC,cAAc,EAAE,GAAGC,YAAAA;AAE7C,MAAMC,6BAA6BC,OAAK,CAAA;AAAC,IAAA,WAAA;AAAa,IAAA,UAAA;AAAY,IAAA,OAAA;AAAS,IAAA;AAAQ,CAAA,CAAA;AAEnF,MAAMC,sBAAyB,GAAA,UAAA;IAC7B,IAAI,CAACC,MAAOC,CAAAA,EAAE,EAAE;QACd,OAAO,IAAA;AACT;AAEA,IAAA,MAAMC,cAAiBF,GAAAA,MAAAA,CAAOG,EAAE,CAACC,KAAK;AACtC,IAAA,IAAIC,SAAMH,cAAiB,CAAA,EAAA;QACzB,OAAO,IAAA;AACT;AAEA,IAAA,MAAMI,YAAY,MAAMN,MAAAA,CAAOO,OAAO,CAAC,eAAeC,yBAAyB,EAAA;AAE/E,IAAA,IAAIF,YAAYJ,cAAgB,EAAA;QAC9B,OAAO,IAAA;AACT;AACF,CAAA;AAEA,WAAe;AACb,IAAA,MAAMO,QAAOC,GAAY,EAAA;QACvB,IAAI,CAAE,MAAMX,sBAA2B,EAAA,EAAA;AACrC,YAAA,MAAM,IAAIJ,cAAe,CAAA,0DAAA,CAAA;AAC3B;AAEA,QAAA,MAAM,EAAEgB,IAAI,EAAE,GAAGD,IAAIE,OAAO;AAC5B,QAAA,MAAMC,SAAY,GAAA;AAAE,YAAA,GAAGF,IAAI;YAAEG,KAAOC,EAAAA,CAAAA,CAAEC,GAAG,CAACL,IAAM,EAAA,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA,CAAEM,WAAW;AAAG,SAAA;AAE3E,QAAA,MAAMC,gCAA0BL,CAAAA,SAAAA,CAAAA;AAEhC,QAAA,MAAMM,aAAatB,0BAA2BgB,CAAAA,SAAAA,CAAAA;QAC9C,MAAM,EAAEO,kBAAkB,EAAE,GAAGP,SAAAA;AAE/B,QAAA,MAAMQ,iBAAoB,GAAA,MAAMC,gBAAW,CAAA,MAAA,CAAA,CAAQC,MAAM,CAAC;AAAET,YAAAA,KAAAA,EAAOK,WAAWL;AAAM,SAAA,CAAA;AAEpF,QAAA,IAAIO,iBAAmB,EAAA;AACrB,YAAA,MAAM,IAAI3B,gBAAiB,CAAA,qBAAA,CAAA;AAC7B;AAEA,QAAA,IAAI0B,kBAAoB,EAAA;YACtBI,MAAOC,CAAAA,MAAM,CAACN,UAAY,EAAA;gBAAEO,iBAAmB,EAAA,IAAA;gBAAMC,QAAU,EAAA;AAAK,aAAA,CAAA;AACtE;AAEA,QAAA,MAAMC,WAAc,GAAA,MAAMN,gBAAW,CAAA,MAAA,CAAA,CAAQb,MAAM,CAACU,UAAAA,CAAAA;AACpD,QAAA,MAAMU,QAAWP,GAAAA,gBAAAA,CAAW,MAAQQ,CAAAA,CAAAA,YAAY,CAACF,WAAAA,CAAAA;;;QAIjDJ,MAAOC,CAAAA,MAAM,CAACI,QAAU,EAAA;AAAEH,YAAAA,iBAAAA,EAAmBE,YAAYF;AAAkB,SAAA,CAAA;AAE3EhB,QAAAA,GAAAA,CAAIqB,OAAO,CAAC;YAAEC,IAAMH,EAAAA;AAAS,SAAA,CAAA;AAC/B,KAAA;AAEA,IAAA,MAAMI,QAAOvB,GAAY,EAAA;AACvB,QAAA,MAAM,EAAEwB,EAAE,EAAE,GAAGxB,IAAIyB,MAAM;AACzB,QAAA,MAAM,EAAExB,IAAMyB,EAAAA,KAAK,EAAE,GAAG1B,IAAIE,OAAO;AAEnC,QAAA,MAAMyB,8BAAwBD,CAAAA,KAAAA,CAAAA;AAE9B,QAAA,IAAIrB,CAAEuB,CAAAA,GAAG,CAACF,KAAAA,EAAO,OAAU,CAAA,EAAA;AACzB,YAAA,MAAMG,gBAAmB,GAAA,MAAMjB,gBAAW,CAAA,MAAA,CAAA,CAAQC,MAAM,CAAC;gBACvDW,EAAI,EAAA;oBAAEM,GAAKN,EAAAA;AAAG,iBAAA;AACdpB,gBAAAA,KAAAA,EAAOsB,MAAMtB;AACf,aAAA,CAAA;AAEA,YAAA,IAAIyB,gBAAkB,EAAA;AACpB,gBAAA,MAAM,IAAI7C,gBAAiB,CAAA,+CAAA,CAAA;AAC7B;AACF;AAEA,QAAA,MAAM+C,OAAO,MAAMnB,gBAAAA,CAAW,MAAQoB,CAAAA,CAAAA,OAAO,CAACR,EAAI,EAAA,IAAA,CAAA;QAElD,IAAI,CAAE,MAAMnC,sBAA6B,EAAA,IAAA,CAAC0C,KAAKd,QAAQ,IAAIS,KAAMT,CAAAA,QAAQ,EAAE;AACzE,YAAA,MAAM,IAAIhC,cAAe,CAAA,yDAAA,CAAA;AAC3B;AAEA,QAAA,MAAMgD,cAAc,MAAMrB,gBAAAA,CAAW,MAAQsB,CAAAA,CAAAA,UAAU,CAACV,EAAIE,EAAAA,KAAAA,CAAAA;AAE5D,QAAA,IAAI,CAACO,WAAa,EAAA;YAChB,OAAOjC,GAAAA,CAAImC,QAAQ,CAAC,qBAAA,CAAA;AACtB;AAEAnC,QAAAA,GAAAA,CAAIC,IAAI,GAAG;YACTqB,IAAMV,EAAAA,gBAAAA,CAAW,MAAQQ,CAAAA,CAAAA,YAAY,CAACa,WAAAA;AACxC,SAAA;AACF,KAAA;AAEA,IAAA,MAAMG,aAAYpC,GAAY,EAAA;AAC5B,QAAA,MAAM,EAAE+B,IAAI,EAAE,GAAG/B,IAAIqC,KAAK;QAC1B,MAAMD,WAAAA,GAAc,MAAME,mBAAYP,CAAAA,IAAAA,CAAAA;AAEtC/B,QAAAA,GAAAA,CAAIC,IAAI,GAAG;YACTqB,IAAM,EAAA;AACJc,gBAAAA;AACF;AACF,SAAA;AACF;AACF,CAAE;;;;"}