"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from) {
    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)
        to[j] = from[i];
    return to;
};
exports.__esModule = true;
exports.Adapt = void 0;
function isEmpty(s) {
    return !s;
}
function Adapt(adapter_) {
    var env = adapter_.env, os = adapter_.os, path = adapter_.path;
    var isWinOS = /^win/i.test(adapter_.process.platform);
    function normalizePath(path_) {
        return path_ ? adapter_.path.normalize(adapter_.path.join(path_, '.')) : void 0;
    }
    function home() {
        var posix = function () {
            return normalizePath((typeof os.homedir === 'function' ? os.homedir() : void 0) || env.get('HOME'));
        };
        var windows = function () {
            var priorityList = [
                typeof os.homedir === 'function' ? os.homedir() : void 0,
                env.get('USERPROFILE'),
                env.get('HOME'),
                env.get('HOMEDRIVE') || env.get('HOMEPATH')
                    ? path.join(env.get('HOMEDRIVE') || '', env.get('HOMEPATH') || '')
                    : void 0,
            ];
            return normalizePath(priorityList.find(function (v) { return !isEmpty(v); }));
        };
        return isWinOS ? windows() : posix();
    }
    function temp() {
        function joinPathToBase(base, segments) {
            return base ? path.join.apply(path, __spreadArray([base], segments)) : void 0;
        }
        function posix() {
            var fallback = '/tmp';
            var priorityList = [
                typeof os.tmpdir === 'function' ? os.tmpdir() : void 0,
                env.get('TMPDIR'),
                env.get('TEMP'),
                env.get('TMP'),
            ];
            return normalizePath(priorityList.find(function (v) { return !isEmpty(v); })) || fallback;
        }
        function windows() {
            var fallback = 'C:\\Temp';
            var priorityListLazy = [
                typeof os.tmpdir === 'function' ? os.tmpdir : function () { return void 0; },
                function () { return env.get('TEMP'); },
                function () { return env.get('TMP'); },
                function () { return joinPathToBase(env.get('LOCALAPPDATA'), ['Temp']); },
                function () { return joinPathToBase(home(), ['AppData', 'Local', 'Temp']); },
                function () { return joinPathToBase(env.get('ALLUSERSPROFILE'), ['Temp']); },
                function () { return joinPathToBase(env.get('SystemRoot'), ['Temp']); },
                function () { return joinPathToBase(env.get('windir'), ['Temp']); },
                function () { return joinPathToBase(env.get('SystemDrive'), ['\\', 'Temp']); },
            ];
            var v = priorityListLazy.find(function (v) { return v && !isEmpty(v()); });
            return (v && normalizePath(v())) || fallback;
        }
        return isWinOS ? windows() : posix();
    }
    var OSPaths_ = (function () {
        function OSPaths_() {
            function OSPaths() {
                return new OSPaths_();
            }
            OSPaths.home = home;
            OSPaths.temp = temp;
            return OSPaths;
        }
        return OSPaths_;
    }());
    return { OSPaths: new OSPaths_() };
}
exports.Adapt = Adapt;
//# sourceMappingURL=data:application/json;base64,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