{"version": 3, "file": "plugins.mjs", "sources": ["../../../../src/node/vite/plugins.ts"], "sourcesContent": ["import type { Plugin } from 'vite';\n\nimport { getDocumentHTML } from '../staticFiles';\nimport type { BuildContext } from '../create-build-context';\n\nconst buildFilesPlugin = (ctx: BuildContext): Plugin => {\n  const CHUNK_ID = '.strapi/client/app.js';\n\n  return {\n    name: 'strapi/server/build-files',\n    apply: 'build',\n    buildStart() {\n      this.emitFile({\n        type: 'chunk',\n        id: CHUNK_ID,\n        name: 'strapi',\n      });\n    },\n    async generateBundle(_options, outputBundle) {\n      const bundle = outputBundle;\n      const entryFile = Object.values(bundle).find(\n        (file) =>\n          file.type === 'chunk' && file.name === 'strapi' && file.facadeModuleId?.endsWith(CHUNK_ID)\n      );\n\n      if (!entryFile) {\n        throw new Error(`Failed to find entry file in bundle (${CHUNK_ID})`);\n      }\n\n      if (entryFile.type !== 'chunk') {\n        throw new Error('Entry file is not a chunk');\n      }\n\n      const entryFileName = entryFile.fileName;\n      const entryPath = [ctx.basePath.replace(/\\/+$/, ''), entryFileName].join('/');\n\n      this.emitFile({\n        type: 'asset',\n        fileName: 'index.html',\n        source: getDocumentHTML({\n          logger: ctx.logger,\n          props: {\n            entryPath,\n          },\n        }),\n      });\n    },\n  };\n};\n\nexport { buildFilesPlugin };\n"], "names": ["buildFilesPlugin", "ctx", "CHUNK_ID", "name", "apply", "buildStart", "emitFile", "type", "id", "generateBundle", "_options", "outputBundle", "bundle", "entryFile", "Object", "values", "find", "file", "facadeModuleId", "endsWith", "Error", "entryFileName", "fileName", "entryPath", "basePath", "replace", "join", "source", "getDocumentHTML", "logger", "props"], "mappings": ";;AAKA,MAAMA,mBAAmB,CAACC,GAAAA,GAAAA;AACxB,IAAA,MAAMC,QAAW,GAAA,uBAAA;IAEjB,OAAO;QACLC,IAAM,EAAA,2BAAA;QACNC,KAAO,EAAA,OAAA;AACPC,QAAAA,UAAAA,CAAAA,GAAAA;YACE,IAAI,CAACC,QAAQ,CAAC;gBACZC,IAAM,EAAA,OAAA;gBACNC,EAAIN,EAAAA,QAAAA;gBACJC,IAAM,EAAA;AACR,aAAA,CAAA;AACF,SAAA;QACA,MAAMM,cAAAA,CAAAA,CAAeC,QAAQ,EAAEC,YAAY,EAAA;AACzC,YAAA,MAAMC,MAASD,GAAAA,YAAAA;YACf,MAAME,SAAAA,GAAYC,OAAOC,MAAM,CAACH,QAAQI,IAAI,CAC1C,CAACC,IACCA,GAAAA,IAAAA,CAAKV,IAAI,KAAK,OAAA,IAAWU,KAAKd,IAAI,KAAK,YAAYc,IAAKC,CAAAA,cAAc,EAAEC,QAASjB,CAAAA,QAAAA,CAAAA,CAAAA;AAGrF,YAAA,IAAI,CAACW,SAAW,EAAA;AACd,gBAAA,MAAM,IAAIO,KAAM,CAAA,CAAC,qCAAqC,EAAElB,QAAAA,CAAS,CAAC,CAAC,CAAA;AACrE;YAEA,IAAIW,SAAAA,CAAUN,IAAI,KAAK,OAAS,EAAA;AAC9B,gBAAA,MAAM,IAAIa,KAAM,CAAA,2BAAA,CAAA;AAClB;YAEA,MAAMC,aAAAA,GAAgBR,UAAUS,QAAQ;AACxC,YAAA,MAAMC,SAAY,GAAA;AAACtB,gBAAAA,GAAAA,CAAIuB,QAAQ,CAACC,OAAO,CAAC,MAAQ,EAAA,EAAA,CAAA;AAAKJ,gBAAAA;AAAc,aAAA,CAACK,IAAI,CAAC,GAAA,CAAA;YAEzE,IAAI,CAACpB,QAAQ,CAAC;gBACZC,IAAM,EAAA,OAAA;gBACNe,QAAU,EAAA,YAAA;AACVK,gBAAAA,MAAAA,EAAQC,eAAgB,CAAA;AACtBC,oBAAAA,MAAAA,EAAQ5B,IAAI4B,MAAM;oBAClBC,KAAO,EAAA;AACLP,wBAAAA;AACF;AACF,iBAAA;AACF,aAAA,CAAA;AACF;AACF,KAAA;AACF;;;;"}