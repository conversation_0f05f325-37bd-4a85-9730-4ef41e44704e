{"version": 3, "file": "getMethodColor.mjs", "sources": ["../../../../admin/src/components/BoundRoute/getMethodColor.js"], "sourcesContent": ["const getMethodColor = (verb) => {\n  switch (verb) {\n    case 'POST': {\n      return {\n        text: 'success600',\n        border: 'success200',\n        background: 'success100',\n      };\n    }\n    case 'GET': {\n      return {\n        text: 'secondary600',\n        border: 'secondary200',\n        background: 'secondary100',\n      };\n    }\n    case 'PUT': {\n      return {\n        text: 'warning600',\n        border: 'warning200',\n        background: 'warning100',\n      };\n    }\n    case 'DELETE': {\n      return {\n        text: 'danger600',\n        border: 'danger200',\n        background: 'danger100',\n      };\n    }\n    default: {\n      return {\n        text: 'neutral600',\n        border: 'neutral200',\n        background: 'neutral100',\n      };\n    }\n  }\n};\n\nexport default getMethodColor;\n"], "names": ["getMethodColor", "verb", "text", "border", "background"], "mappings": "AAAA,MAAMA,iBAAiB,CAACC,IAAAA,GAAAA;IACtB,OAAQA,IAAAA;QACN,KAAK,MAAA;AAAQ,YAAA;gBACX,OAAO;oBACLC,IAAM,EAAA,YAAA;oBACNC,MAAQ,EAAA,YAAA;oBACRC,UAAY,EAAA;AACd,iBAAA;AACF;QACA,KAAK,KAAA;AAAO,YAAA;gBACV,OAAO;oBACLF,IAAM,EAAA,cAAA;oBACNC,MAAQ,EAAA,cAAA;oBACRC,UAAY,EAAA;AACd,iBAAA;AACF;QACA,KAAK,KAAA;AAAO,YAAA;gBACV,OAAO;oBACLF,IAAM,EAAA,YAAA;oBACNC,MAAQ,EAAA,YAAA;oBACRC,UAAY,EAAA;AACd,iBAAA;AACF;QACA,KAAK,QAAA;AAAU,YAAA;gBACb,OAAO;oBACLF,IAAM,EAAA,WAAA;oBACNC,MAAQ,EAAA,WAAA;oBACRC,UAAY,EAAA;AACd,iBAAA;AACF;AACA,QAAA;AAAS,YAAA;gBACP,OAAO;oBACLF,IAAM,EAAA,YAAA;oBACNC,MAAQ,EAAA,YAAA;oBACRC,UAAY,EAAA;AACd,iBAAA;AACF;AACF;AACF;;;;"}