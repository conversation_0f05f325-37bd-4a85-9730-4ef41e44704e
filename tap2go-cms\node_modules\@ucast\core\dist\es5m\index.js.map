{"version": 3, "file": "index.js", "sources": ["../../src/Condition.ts", "../../src/utils.ts", "../../src/builder.ts", "../../src/parsers/defaultInstructionParsers.ts", "../../src/parsers/ObjectQueryParser.ts", "../../src/interpreter.ts", "../../src/translator.ts", "../../src/index.ts"], "sourcesContent": ["export interface Note<T> {\n  type: string\n  message?: string\n  originalValue?: T\n}\n\nexport abstract class Condition<T = unknown> {\n  private _notes!: Note<T>[];\n\n  constructor(\n    public readonly operator: string,\n    public readonly value: T\n  ) {\n    Object.defineProperty(this, '_notes', {\n      writable: true\n    });\n  }\n\n  get notes(): ReadonlyArray<Note<T>> | undefined {\n    return this._notes;\n  }\n\n  addNote(note: Note<T>) {\n    this._notes = this._notes || [];\n    this._notes.push(note);\n  }\n}\n\nexport class DocumentCondition<T> extends Condition<T> {\n}\n\nexport class CompoundCondition<T extends Condition = Condition> extends DocumentCondition<T[]> {\n  constructor(operator: string, conditions: T[]) {\n    if (!Array.isArray(conditions)) {\n      throw new Error(`\"${operator}\" operator expects to receive an array of conditions`);\n    }\n\n    super(operator, conditions);\n  }\n}\n\nexport const ITSELF = '__itself__';\nexport class FieldCondition<T = unknown> extends Condition<T> {\n  public readonly field!: string | typeof ITSELF;\n\n  constructor(operator: string, field: string | typeof ITSELF, value: T) {\n    super(operator, value);\n    this.field = field;\n  }\n}\n\nexport const NULL_CONDITION = new DocumentCondition('__null__', null);\nexport type ConditionValue<T> = T extends Condition<infer V> ? V : unknown;\n", "import { Condition, CompoundCondition, NULL_CONDITION } from './Condition';\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);\n\nexport function isCompound(operator: string, condition: Condition): condition is CompoundCondition {\n  return condition instanceof CompoundCondition && condition.operator === operator;\n}\n\nfunction flattenConditions<T extends Condition>(\n  operator: string,\n  conditions: T[],\n  aggregatedResult?: T[]\n) {\n  const flatConditions: T[] = aggregatedResult || [];\n\n  for (let i = 0, length = conditions.length; i < length; i++) {\n    const currentNode = conditions[i];\n\n    if (isCompound(operator, currentNode)) {\n      flattenConditions(operator, currentNode.value as T[], flatConditions);\n    } else {\n      flatConditions.push(currentNode);\n    }\n  }\n\n  return flatConditions;\n}\n\nexport function optimizedCompoundCondition<T extends Condition>(operator: string, conditions: T[]) {\n  if (conditions.length === 1) {\n    return conditions[0];\n  }\n\n  return new CompoundCondition(operator, flattenConditions(operator, conditions));\n}\n\nexport const identity = <T>(x: T) => x;\nexport const object = () => Object.create(null);\n\nexport const ignoreValue: IgnoreValue = Object.defineProperty(object(), '__@type@__', {\n  value: 'ignore value'\n});\nexport interface IgnoreValue {\n  readonly ['__@type@__']: 'ignore value'\n}\n\nexport function hasOperators<T>(\n  value: any,\n  instructions: Record<string, unknown>,\n  skipIgnore = false,\n): value is T {\n  if (!value || value && value.constructor !== Object) {\n    return false;\n  }\n\n  for (const prop in value) { // eslint-disable-line no-restricted-syntax, guard-for-in\n    const hasProp = hasOwnProperty(value, prop) && hasOwnProperty(instructions, prop);\n    if (hasProp && (!skipIgnore || value[prop] !== ignoreValue)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nexport function objectKeysSkipIgnore(anyObject: Record<string, unknown>) {\n  const keys: string[] = [];\n  for (const key in anyObject) { // eslint-disable-line no-restricted-syntax\n    if (hasOwnProperty(anyObject, key) && anyObject[key] !== ignoreValue) {\n      keys.push(key);\n    }\n  }\n\n  return keys;\n}\n\nexport function pushIfNonNullCondition(conditions: Condition[], condition: Condition) {\n  if (condition !== NULL_CONDITION) {\n    conditions.push(condition);\n  }\n}\n", "import { Condition } from './Condition';\nimport { optimizedCompoundCondition } from './utils';\n\nexport const buildAnd = (conditions: Condition[]) => optimizedCompoundCondition('and', conditions);\nexport const buildOr = (conditions: Condition[]) => optimizedCompoundCondition('or', conditions);\n", "import {\n  FieldCondition,\n  CompoundCondition,\n  DocumentCondition,\n} from '../Condition';\nimport {\n  DocumentInstruction,\n  CompoundInstruction,\n  FieldInstruction,\n} from '../types';\n\ninterface DefaultParsers {\n  compound: Exclude<CompoundInstruction['parse'], undefined>,\n  field: Exclude<FieldInstruction['parse'], undefined>,\n  document: Exclude<DocumentInstruction['parse'], undefined>\n}\n\nexport const defaultInstructionParsers: DefaultParsers = {\n  compound(instruction, value, context) {\n    const queries = Array.isArray(value) ? value : [value];\n    const conditions = queries.map(query => context.parse(query));\n    return new CompoundCondition(instruction.name, conditions);\n  },\n  field(instruction, value, context) {\n    return new FieldCondition(instruction.name, context.field, value);\n  },\n  document(instruction, value) {\n    return new DocumentCondition(instruction.name, value);\n  }\n};\n", "import { Condition } from '../Condition';\nimport {\n  NamedInstruction,\n  ParsingInstruction,\n  FieldParsingContext,\n  ParsingContext,\n} from '../types';\nimport { buildAnd } from '../builder';\nimport { defaultInstructionParsers } from './defaultInstructionParsers';\nimport {\n  identity,\n  hasOperators,\n  object,\n  pushIfNonNullCondition,\n  objectKeysSkipIgnore,\n} from '../utils';\n\nexport type FieldQueryOperators<T extends {}> = {\n  [K in keyof T]: T[K] extends {} ? T[K] : never\n}[keyof T];\n\ntype ParsingInstructions = Record<string, NamedInstruction>;\n\nexport interface QueryOptions {\n  operatorToConditionName?(name: string): string\n  defaultOperatorName?: string\n  fieldContext?: Record<string, unknown>\n  documentContext?: Record<string, unknown>\n  useIgnoreValue?: boolean\n  mergeFinalConditions?(conditions: Condition[]): Condition\n}\n\nexport type ObjectQueryFieldParsingContext = ParsingContext<FieldParsingContext & {\n  query: {},\n  hasOperators<T>(value: unknown): value is T\n}>;\n\nexport class ObjectQueryParser<\n  T extends Record<any, any>,\n  U extends FieldQueryOperators<T> = FieldQueryOperators<T>\n> {\n  private readonly _instructions: ParsingInstructions;\n  private _fieldInstructionContext: ObjectQueryFieldParsingContext;\n  private _documentInstructionContext: ParsingContext<{ query: {} }>;\n  private readonly _options: Required<\n  Pick<QueryOptions, 'operatorToConditionName' | 'defaultOperatorName' | 'mergeFinalConditions'>\n  >;\n\n  private readonly _objectKeys: typeof Object.keys;\n\n  constructor(instructions: Record<string, ParsingInstruction>, options: QueryOptions = object()) {\n    this.parse = this.parse.bind(this);\n    this._options = {\n      operatorToConditionName: options.operatorToConditionName || identity,\n      defaultOperatorName: options.defaultOperatorName || 'eq',\n      mergeFinalConditions: options.mergeFinalConditions || buildAnd,\n    };\n    this._instructions = Object.keys(instructions).reduce((all, name) => {\n      all[name] = { name: this._options.operatorToConditionName(name), ...instructions[name] };\n      return all;\n    }, {} as ParsingInstructions);\n    this._fieldInstructionContext = {\n      ...options.fieldContext,\n      field: '',\n      query: {},\n      parse: this.parse,\n      hasOperators: <T>(value: unknown): value is T => hasOperators(\n        value,\n        this._instructions,\n        options.useIgnoreValue\n      ),\n    };\n    this._documentInstructionContext = {\n      ...options.documentContext,\n      parse: this.parse,\n      query: {}\n    };\n    this._objectKeys = options.useIgnoreValue ? objectKeysSkipIgnore : Object.keys;\n  }\n\n  setParse(parse: this['parse']) {\n    this.parse = parse;\n    this._fieldInstructionContext.parse = parse;\n    this._documentInstructionContext.parse = parse;\n  }\n\n  protected parseField(field: string, operator: string, value: unknown, parentQuery: {}) {\n    const instruction = this._instructions[operator];\n\n    if (!instruction) {\n      throw new Error(`Unsupported operator \"${operator}\"`);\n    }\n\n    if (instruction.type !== 'field') {\n      throw new Error(`Unexpected ${instruction.type} operator \"${operator}\" at field level`);\n    }\n\n    this._fieldInstructionContext.field = field;\n    this._fieldInstructionContext.query = parentQuery;\n\n    return this.parseInstruction(instruction, value, this._fieldInstructionContext);\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  protected parseInstruction(\n    instruction: NamedInstruction,\n    value: unknown,\n    context: ParsingContext<{}>\n  ) {\n    if (typeof instruction.validate === 'function') {\n      instruction.validate(instruction, value);\n    }\n\n    const parse: typeof instruction.parse = instruction.parse\n      || defaultInstructionParsers[instruction.type as keyof typeof defaultInstructionParsers];\n    return parse(instruction, value, context);\n  }\n\n  protected parseFieldOperators(field: string, value: U) {\n    const conditions: Condition[] = [];\n    const keys = this._objectKeys(value);\n\n    for (let i = 0, length = keys.length; i < length; i++) {\n      const op = keys[i];\n      const instruction = this._instructions[op];\n\n      if (!instruction) {\n        throw new Error(`Field query for \"${field}\" may contain only operators or a plain object as a value`);\n      }\n\n      const condition = this.parseField(field, op, value[op as keyof U], value);\n      pushIfNonNullCondition(conditions, condition);\n    }\n\n    return conditions;\n  }\n\n  parse<Q extends T>(query: Q): Condition {\n    const conditions = [];\n    const keys = this._objectKeys(query);\n\n    this._documentInstructionContext.query = query;\n\n    for (let i = 0, length = keys.length; i < length; i++) {\n      const key = keys[i];\n      const value = query[key];\n      const instruction = this._instructions[key];\n\n      if (instruction) {\n        if (instruction.type !== 'document' && instruction.type !== 'compound') {\n          throw new Error(`Cannot use parsing instruction for operator \"${key}\" in \"document\" context as it is supposed to be used in  \"${instruction.type}\" context`);\n        }\n\n        pushIfNonNullCondition(\n          conditions,\n          this.parseInstruction(instruction, value, this._documentInstructionContext)\n        );\n      } else if (this._fieldInstructionContext.hasOperators<U>(value)) {\n        conditions.push(...this.parseFieldOperators(key, value));\n      } else {\n        pushIfNonNullCondition(\n          conditions,\n          this.parseField(key, this._options.defaultOperatorName, value, query)\n        );\n      }\n    }\n\n    return this._options.mergeFinalConditions(conditions);\n  }\n}\n", "import { Condition } from './Condition';\n\ntype ArgsExceptLast<F extends (...args: any[]) => any> =\n  F extends (a: any, c: any) => any\n    ? Parameters<(condition: Condition) => 0>\n    : F extends (a: any, b: any, c: any) => any\n      ? Parameters<(condition: Condition, value: Parameters<F>[1]) => 0>\n      : Parameters<(\n        condition: Condition,\n        value: Parameters<F>[1],\n        options: Parameters<F>[2],\n        ...args: unknown[]\n      ) => 0>;\n\nexport type Interpreter<T extends Condition, R> = (condition: T, ...args: any[]) => R;\nexport type AnyInterpreter = Interpreter<any, any>;\nexport interface InterpretationContext<T extends AnyInterpreter> {\n  interpret(...args: ArgsExceptLast<T>): ReturnType<T>;\n}\n\nfunction getInterpreter<T extends Record<string, AnyInterpreter>>(\n  interpreters: T,\n  operator: keyof T\n) {\n  const interpret = interpreters[operator];\n\n  if (typeof interpret !== 'function') {\n    throw new Error(`Unable to interpret \"${operator}\" condition. Did you forget to register interpreter for it?`);\n  }\n\n  return interpret;\n}\n\nexport interface InterpreterOptions {\n  numberOfArguments?: 1 | 2 | 3\n  getInterpreterName?(condition: Condition, context: this): string\n}\n\nfunction defaultInterpreterName(condition: Condition) {\n  return condition.operator;\n}\n\nexport function createInterpreter<T extends AnyInterpreter, U extends {} = {}>(\n  interpreters: Record<string, T>,\n  rawOptions?: U\n) {\n  const options = rawOptions as U & InterpreterOptions;\n  const getInterpreterName = options && options.getInterpreterName || defaultInterpreterName;\n  let interpret;\n\n  switch (options ? options.numberOfArguments : 0) {\n    case 1:\n      interpret = ((condition) => {\n        const interpreterName = getInterpreterName(condition, options);\n        const interpretOperator = getInterpreter(interpreters, interpreterName);\n        return interpretOperator(condition, defaultContext); // eslint-disable-line @typescript-eslint/no-use-before-define\n      }) as InterpretationContext<T>['interpret'];\n      break;\n    case 3:\n      interpret = ((condition, value, params) => {\n        const interpreterName = getInterpreterName(condition, options);\n        const interpretOperator = getInterpreter(interpreters, interpreterName);\n        return interpretOperator(condition, value, params, defaultContext); // eslint-disable-line @typescript-eslint/no-use-before-define\n      }) as InterpretationContext<T>['interpret'];\n      break;\n    default:\n      interpret = ((condition, value) => {\n        const interpreterName = getInterpreterName(condition, options);\n        const interpretOperator = getInterpreter(interpreters, interpreterName);\n        return interpretOperator(condition, value, defaultContext); // eslint-disable-line @typescript-eslint/no-use-before-define\n      }) as InterpretationContext<T>['interpret'];\n      break;\n  }\n\n  const defaultContext = {\n    ...options,\n    interpret,\n  } as InterpretationContext<T> & U;\n\n  return defaultContext.interpret;\n}\n", "import { Condition } from './Condition';\nimport { Parse } from './types';\nimport { AnyInterpreter } from './interpreter';\n\ntype Bound<T> = T extends (first: Condition, ...args: infer A) => any\n  ? { (...args: A): ReturnType<T>, ast: Condition }\n  : never;\n\nexport function createTranslatorFactory<Lang, Interpreter extends AnyInterpreter>(\n  parse: Parse<Lang>,\n  interpret: Interpreter\n) {\n  return (query: Lang, ...args: unknown[]): Bound<Interpreter> => {\n    const ast = parse(query, ...args);\n    const translate = (interpret as any).bind(null, ast);\n    translate.ast = ast;\n    return translate;\n  };\n}\n", "import { ObjectQueryParser } from './parsers/ObjectQueryParser';\n\nexport * from './Condition';\nexport * from './types';\nexport * from './interpreter';\nexport * from './translator';\nexport * from './builder';\nexport {\n  isCompound,\n  hasOperators,\n  identity,\n  object,\n  optimizedCompoundCondition,\n  ignoreValue,\n} from './utils';\nexport type {\n  IgnoreValue\n} from './utils';\nexport * from './parsers/ObjectQueryParser';\nexport * from './parsers/defaultInstructionParsers';\n/**\n * @deprecated use `ObjectQueryParser#parseInstruction` instead\n * TODO(major): remove\n */\nexport const parseInstruction = (ObjectQueryParser.prototype as any).parseInstruction;\n"], "names": ["Condition", "operator", "value", "Object", "defineProperty", "this", "writable", "addNote", "note", "_notes", "push", "DocumentCondition", "CompoundCondition", "conditions", "Array", "isArray", "Error", "_DocumentCondition", "ITSELF", "FieldCondition", "field", "NULL_CONDITION", "hasOwnProperty", "prototype", "call", "bind", "isCompound", "condition", "optimizedCompoundCondition", "length", "flattenConditions", "aggregatedResult", "flatConditions", "i", "currentNode", "identity", "x", "object", "create", "ignoreValue", "hasOperators", "instructions", "skip<PERSON><PERSON>re", "constructor", "prop", "objectKeysSkipIgnore", "anyObject", "keys", "key", "pushIfNonNullCondition", "buildAnd", "buildOr", "defaultInstructionParsers", "compound", "instruction", "context", "map", "query", "parse", "name", "document", "ObjectQueryParser", "options", "_instructions", "_fieldInstructionContext", "_documentInstructionContext", "_options", "_objectKeys", "operatorToConditionName", "defaultOperatorName", "mergeFinalConditions", "reduce", "all", "_this", "fieldContext", "useIgnoreValue", "documentContext", "set<PERSON><PERSON><PERSON>", "parseField", "parentQuery", "type", "parseInstruction", "validate", "parseFieldOperators", "op", "getInterpreter", "interpreters", "interpret", "defaultInterpreterName", "createInterpreter", "rawOptions", "getInterpreterName", "numberOfArguments", "<PERSON><PERSON><PERSON>", "interpretOperator", "defaultContext", "params", "createTranslatorFactory", "args", "ast", "translate"], "mappings": "wdAMsBA,wBAIFC,EACAC,QADAD,SAAAA,OACAC,MAAAA,EAEhBC,OAAOC,eAAeC,KAAM,IAAU,CACpCC,UAAU,iCAQdC,QAAA,SAAQC,QACDC,EAASJ,KAAKI,GAAU,QACxBA,EAAOC,KAAKF,+CALVH,KAAKI,uCASHE,iFAA6BX,GAG7BY,yBACCX,EAAkBY,OACvBC,MAAMC,QAAQF,SACX,IAAIG,UAAUf,iEAGtBgB,YAAMhB,EAAUY,0BANoDF,GAU3DO,EAAS,aACTC,yBAGClB,EAAkBmB,EAA+BlB,8BACrDD,EAAUC,UACXkB,MAAQA,qBALgCpB,GASpCqB,EAAiB,IAAIV,EAAkB,WAAY,MCjD1DW,EAAiBnB,OAAOoB,UAAUD,eAAeE,KAAKC,KAAKtB,OAAOoB,UAAUD,gBAE3E,SAASI,EAAWzB,EAAkB0B,UACpCA,aAAqBf,GAAqBe,EAAU1B,WAAaA,EAuBnE,SAAS2B,EAAgD3B,EAAkBY,UACtD,IAAtBA,EAAWgB,OACNhB,EAAW,GAGb,IAAID,EAAkBX,EAzB/B,SAAS6B,EACP7B,EACAY,EACAkB,WAEMC,EAAsBD,GAAoB,GAEvCE,EAAI,EAAGJ,EAAShB,EAAWgB,OAAQI,EAAIJ,EAAQI,IAAK,KACrDC,EAAcrB,EAAWoB,GAE3BP,EAAWzB,EAAUiC,GACvBJ,EAAkB7B,EAAUiC,EAAYhC,MAAc8B,GAEtDA,EAAetB,KAAKwB,UAIjBF,EAQgCF,CAAkB7B,EAAUY,QAGxDsB,EAAW,SAAIC,UAASA,GACxBC,EAAS,kBAAMlC,OAAOmC,OAAO,OAE7BC,EAA2BpC,OAAOC,eAAeiC,IAAU,aAAc,CACpFnC,MAAO,iBAMF,SAASsC,EACdtC,EACAuC,EACAC,eAAAA,IAAAA,GAAa,IAERxC,GAASA,GAASA,EAAMyC,cAAgBxC,cACpC,MAGJ,IAAMyC,KAAQ1C,EAAO,IACRoB,EAAepB,EAAO0C,IAAStB,EAAemB,EAAcG,MAC3DF,GAAcxC,EAAM0C,KAAUL,UACtC,SAIJ,EAGF,SAASM,EAAqBC,OAC7BC,EAAiB,OAClB,IAAMC,KAAOF,EACZxB,EAAewB,EAAWE,IAAQF,EAAUE,KAAST,GACvDQ,EAAKrC,KAAKsC,UAIPD,EAGF,SAASE,EAAuBpC,EAAyBc,GAC1DA,IAAcN,GAChBR,EAAWH,KAAKiB,OC3EPuB,EAAW,SAACrC,UAA4Be,EAA2B,MAAOf,IAC1EsC,EAAU,SAACtC,UAA4Be,EAA2B,KAAMf,ICaxEuC,EAA4C,CACvDC,kBAASC,EAAapD,EAAOqD,OAErB1C,GADUC,MAAMC,QAAQb,GAASA,EAAQ,CAACA,IACrBsD,KAAI,SAAAC,UAASF,EAAQG,MAAMD,aAC/C,IAAI7C,EAAkB0C,EAAYK,KAAM9C,IAEjDO,eAAMkC,EAAapD,EAAOqD,UACjB,IAAIpC,EAAemC,EAAYK,KAAMJ,EAAQnC,MAAOlB,IAE7D0D,kBAASN,EAAapD,UACb,IAAIS,EAAkB2C,EAAYK,KAAMzD,KCUtC2D,wBAaCpB,EAAkDqB,uBAAAA,IAAAA,EAAwBzB,UATrE0B,cACTC,cACAC,cACSC,cAIAC,cAGVT,MAAQrD,KAAKqD,MAAMjC,KAAKpB,WACxB6D,EAAW,CACdE,wBAAyBN,EAAQM,yBAA2BjC,EAC5DkC,oBAAqBP,EAAQO,qBAAuB,KACpDC,qBAAsBR,EAAQQ,sBAAwBpB,QAEnDa,EAAgB5D,OAAO4C,KAAKN,GAAc8B,QAAO,SAACC,EAAKb,UAC1Da,EAAIb,MAAUA,KAAMc,EAAKP,EAASE,wBAAwBT,IAAUlB,EAAakB,IAC1Ea,IACN,SACER,OACAF,EAAQY,cACXtD,MAAO,GACPqC,MAAO,GACPC,MAAOrD,KAAKqD,MACZlB,aAAc,SAAItC,UAA+BsC,EAC/CtC,EACAuE,EAAKV,EACLD,EAAQa,wBAGPV,OACAH,EAAQc,iBACXlB,MAAOrD,KAAKqD,MACZD,MAAO,UAEJU,EAAcL,EAAQa,eAAiB9B,EAAuB1C,OAAO4C,gCAG5E8B,SAAA,SAASnB,QACFA,MAAQA,OACRM,EAAyBN,MAAQA,OACjCO,EAA4BP,MAAQA,KAGjCoB,WAAV,SAAqB1D,EAAenB,EAAkBC,EAAgB6E,OAC9DzB,EAAcjD,KAAK0D,EAAc9D,OAElCqD,QACG,IAAItC,+BAA+Bf,UAGlB,UAArBqD,EAAY0B,WACR,IAAIhE,oBAAoBsC,EAAY0B,mBAAkB/E,kCAGzD+D,EAAyB5C,MAAQA,OACjC4C,EAAyBP,MAAQsB,EAE/B1E,KAAK4E,iBAAiB3B,EAAapD,EAAOG,KAAK2D,MAI9CiB,iBAAV,SACE3B,EACApD,EACAqD,SAEoC,mBAAzBD,EAAY4B,UACrB5B,EAAY4B,SAAS5B,EAAapD,IAGIoD,EAAYI,OAC/CN,EAA0BE,EAAY0B,OAC9B1B,EAAapD,EAAOqD,MAGzB4B,oBAAV,SAA8B/D,EAAelB,WACrCW,EAA0B,GAC1BkC,EAAO1C,KAAK8D,EAAYjE,GAErB+B,EAAI,EAAGJ,EAASkB,EAAKlB,OAAQI,EAAIJ,EAAQI,IAAK,KAC/CmD,EAAKrC,EAAKd,OACI5B,KAAK0D,EAAcqB,SAG/B,IAAIpE,0BAA0BI,+DAItC6B,EAAuBpC,EADLR,KAAKyE,WAAW1D,EAAOgE,EAAIlF,EAAMkF,GAAgBlF,WAI9DW,KAGT6C,MAAA,SAAmBD,OACX5C,EAAa,GACbkC,EAAO1C,KAAK8D,EAAYV,QAEzBQ,EAA4BR,MAAQA,MAEpC,IAAIxB,EAAI,EAAGJ,EAASkB,EAAKlB,OAAQI,EAAIJ,EAAQI,IAAK,KAC/Ce,EAAMD,EAAKd,GACX/B,EAAQuD,EAAMT,GACdM,EAAcjD,KAAK0D,EAAcf,MAEnCM,EAAa,IACU,aAArBA,EAAY0B,MAA4C,aAArB1B,EAAY0B,WAC3C,IAAIhE,sDAAsDgC,+DAAgEM,EAAY0B,kBAG9I/B,EACEpC,EACAR,KAAK4E,iBAAiB3B,EAAapD,EAAOG,KAAK4D,SAExC5D,KAAK2D,EAAyBxB,aAAgBtC,GACvDW,EAAWH,WAAXG,EAAmBR,KAAK8E,oBAAoBnC,EAAK9C,IAEjD+C,EACEpC,EACAR,KAAKyE,WAAW9B,EAAK3C,KAAK6D,EAASG,oBAAqBnE,EAAOuD,WAK9DpD,KAAK6D,EAASI,qBAAqBzD,SCnJ9C,SAASwE,EACPC,EACArF,OAEMsF,EAAYD,EAAarF,MAEN,mBAAdsF,QACH,IAAIvE,8BAA8Bf,wEAGnCsF,EAQT,SAASC,EAAuB7D,UACvBA,EAAU1B,SAGZ,SAASwF,EACdH,EACAI,OAIIH,EAFEzB,EAAU4B,EACVC,EAAqB7B,GAAWA,EAAQ6B,oBAAsBH,SAG5D1B,EAAUA,EAAQ8B,kBAAoB,QACvC,EACHL,EAAa,SAAC5D,OACNkE,EAAkBF,EAAmBhE,EAAWmC,UAC5BuB,EAAeC,EAAcO,EAChDC,CAAkBnE,EAAWoE,eAGnC,EACHR,EAAa,SAAC5D,EAAWzB,EAAO8F,OACxBH,EAAkBF,EAAmBhE,EAAWmC,UAC5BuB,EAAeC,EAAcO,EAChDC,CAAkBnE,EAAWzB,EAAO8F,EAAQD,kBAIrDR,EAAa,SAAC5D,EAAWzB,OACjB2F,EAAkBF,EAAmBhE,EAAWmC,UAC5BuB,EAAeC,EAAcO,EAChDC,CAAkBnE,EAAWzB,EAAO6F,QAK3CA,OACDjC,GACHyB,UAAAA,WAGKQ,EAAeR,UCvEjB,SAASU,EACdvC,EACA6B,UAEO,SAAC9B,8BAAgByC,mCAAAA,wBAChBC,EAAMzC,gBAAMD,UAAUyC,IACtBE,EAAab,EAAkB9D,KAAK,KAAM0E,UAChDC,EAAUD,IAAMA,EACTC,OCQEnB,EAAoBpB,EAAkBtC,UAAkB0D"}