import { jsx } from 'react/jsx-runtime';
import * as React from 'react';
import { useIntl } from 'react-intl';
import { useMatch, useNavigate } from 'react-router-dom';
import { Page } from '../../../../admin/src/components/PageHelpers.mjs';
import { useTypedDispatch } from '../../../../admin/src/core/store/hooks.mjs';
import { login } from '../../../../admin/src/reducer.mjs';
import { getCookieValue } from '../../../../admin/src/utils/cookies.mjs';

const AuthResponse = ()=>{
    const match = useMatch('/auth/login/:authResponse');
    const { formatMessage } = useIntl();
    const navigate = useNavigate();
    const dispatch = useTypedDispatch();
    const redirectToOops = React.useCallback(()=>{
        navigate({
            pathname: '/auth/oops',
            search: `?info=${encodeURIComponent(formatMessage({
                id: 'Auth.form.button.login.providers.error',
                defaultMessage: 'We cannot connect you through the selected provider.'
            }))}`
        });
    }, [
        navigate,
        formatMessage
    ]);
    React.useEffect(()=>{
        if (match?.params.authResponse === 'error') {
            redirectToOops();
        }
        if (match?.params.authResponse === 'success') {
            const jwtToken = getCookieValue('jwtToken');
            if (jwtToken) {
                dispatch(login({
                    token: jwtToken
                }));
                navigate('/auth/login');
            } else {
                redirectToOops();
            }
        }
    }, [
        dispatch,
        match,
        redirectToOops,
        navigate
    ]);
    return /*#__PURE__*/ jsx(Page.Loading, {});
};

export { AuthResponse };
//# sourceMappingURL=AuthResponse.mjs.map
