{"version": 3, "file": "useMenu.mjs", "sources": ["../../../../../admin/src/hooks/useMenu.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { Cog, ShoppingCart, House } from '@strapi/icons';\nimport cloneDeep from 'lodash/cloneDeep';\n\nimport { useTypedSelector } from '../core/store/hooks';\nimport { useAuth, AuthContextValue } from '../features/Auth';\nimport { StrapiAppContextValue, useStrapiApp } from '../features/StrapiApp';\n\n/* -------------------------------------------------------------------------------------------------\n * useMenu\n * -----------------------------------------------------------------------------------------------*/\n\nexport type MenuItem = Omit<StrapiAppContextValue['menu'][number], 'Component'>;\n\nexport interface Menu {\n  generalSectionLinks: MenuItem[];\n  pluginsSectionLinks: MenuItem[];\n  isLoading: boolean;\n}\n\nconst useMenu = (shouldUpdateStrapi: boolean) => {\n  const checkUserHasPermissions = useAuth('useMenu', (state) => state.checkUserHasPermissions);\n  const menu = useStrapiApp('useMenu', (state) => state.menu);\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const [menuWithUserPermissions, setMenuWithUserPermissions] = React.useState<Menu>({\n    generalSectionLinks: [\n      {\n        icon: House,\n        intlLabel: {\n          id: 'global.home',\n          defaultMessage: 'Home',\n        },\n        to: '/',\n        permissions: [],\n        position: 0,\n      },\n      {\n        icon: ShoppingCart,\n        intlLabel: {\n          id: 'global.marketplace',\n          defaultMessage: 'Marketplace',\n        },\n        to: '/marketplace',\n        permissions: permissions.marketplace?.main ?? [],\n        position: 7,\n      },\n      {\n        icon: Cog,\n        intlLabel: {\n          id: 'global.settings',\n          defaultMessage: 'Settings',\n        },\n        to: '/settings',\n        // Permissions of this link are retrieved in the init phase\n        // using the settings menu\n        permissions: [],\n        notificationsCount: 0,\n        position: 9,\n      },\n    ],\n    pluginsSectionLinks: [],\n    isLoading: true,\n  });\n  const generalSectionLinksRef = React.useRef(menuWithUserPermissions.generalSectionLinks);\n\n  React.useEffect(() => {\n    async function applyMenuPermissions() {\n      const authorizedPluginSectionLinks = await getPluginSectionLinks(\n        menu,\n        checkUserHasPermissions\n      );\n\n      const authorizedGeneralSectionLinks = await getGeneralLinks(\n        generalSectionLinksRef.current,\n        shouldUpdateStrapi,\n        checkUserHasPermissions\n      );\n\n      setMenuWithUserPermissions((state) => ({\n        ...state,\n        generalSectionLinks: authorizedGeneralSectionLinks,\n        pluginsSectionLinks: authorizedPluginSectionLinks,\n        isLoading: false,\n      }));\n    }\n\n    applyMenuPermissions();\n  }, [\n    setMenuWithUserPermissions,\n    generalSectionLinksRef,\n    menu,\n    permissions,\n    shouldUpdateStrapi,\n    checkUserHasPermissions,\n  ]);\n\n  return menuWithUserPermissions;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\nconst getGeneralLinks = async (\n  generalSectionRawLinks: MenuItem[],\n  shouldUpdateStrapi: boolean = false,\n  checkUserHasPermissions: AuthContextValue['checkUserHasPermissions']\n) => {\n  const generalSectionLinksPermissions = await Promise.all(\n    generalSectionRawLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n  );\n\n  const authorizedGeneralSectionLinks = generalSectionRawLinks.filter(\n    (_, index) => generalSectionLinksPermissions[index].length > 0\n  );\n\n  const settingsLinkIndex = authorizedGeneralSectionLinks.findIndex(\n    (obj) => obj.to === '/settings'\n  );\n\n  if (settingsLinkIndex === -1) {\n    return [];\n  }\n\n  const authorizedGeneralLinksClone = cloneDeep(authorizedGeneralSectionLinks);\n\n  authorizedGeneralLinksClone[settingsLinkIndex].notificationsCount = shouldUpdateStrapi ? 1 : 0;\n\n  return authorizedGeneralLinksClone;\n};\n\nconst getPluginSectionLinks = async (\n  pluginsSectionRawLinks: MenuItem[],\n  checkUserHasPermissions: AuthContextValue['checkUserHasPermissions']\n) => {\n  const pluginSectionLinksPermissions = await Promise.all(\n    pluginsSectionRawLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n  );\n\n  const authorizedPluginSectionLinks = pluginsSectionRawLinks.filter(\n    (_, index) => pluginSectionLinksPermissions[index].length > 0\n  );\n\n  return authorizedPluginSectionLinks;\n};\n\nexport { useMenu };\n"], "names": ["useMenu", "shouldUpdateStrapi", "checkUserHasPermissions", "useAuth", "state", "menu", "useStrapiApp", "permissions", "useTypedSelector", "admin_app", "menuWithUserPermissions", "setMenuWithUserPermissions", "React", "useState", "generalSectionLinks", "icon", "House", "intlLabel", "id", "defaultMessage", "to", "position", "ShoppingCart", "marketplace", "main", "Cog", "notificationsCount", "pluginsSectionLinks", "isLoading", "generalSectionLinksRef", "useRef", "useEffect", "applyMenuPermissions", "authorizedPluginSectionLinks", "getPluginSectionLinks", "authorizedGeneralSectionLinks", "getGeneralLinks", "current", "generalSectionRawLinks", "generalSectionLinksPermissions", "Promise", "all", "map", "filter", "_", "index", "length", "settingsLinkIndex", "findIndex", "obj", "authorizedGeneralLinksClone", "cloneDeep", "pluginsSectionRawLinks", "pluginSectionLinksPermissions"], "mappings": ";;;;;;;AAqBA,MAAMA,UAAU,CAACC,kBAAAA,GAAAA;AACf,IAAA,MAAMC,0BAA0BC,OAAQ,CAAA,SAAA,EAAW,CAACC,KAAAA,GAAUA,MAAMF,uBAAuB,CAAA;AAC3F,IAAA,MAAMG,OAAOC,YAAa,CAAA,SAAA,EAAW,CAACF,KAAAA,GAAUA,MAAMC,IAAI,CAAA;AAC1D,IAAA,MAAME,cAAcC,gBAAiB,CAAA,CAACJ,QAAUA,KAAMK,CAAAA,SAAS,CAACF,WAAW,CAAA;AAC3E,IAAA,MAAM,CAACG,uBAAyBC,EAAAA,0BAAAA,CAA2B,GAAGC,KAAAA,CAAMC,QAAQ,CAAO;QACjFC,mBAAqB,EAAA;AACnB,YAAA;gBACEC,IAAMC,EAAAA,KAAAA;gBACNC,SAAW,EAAA;oBACTC,EAAI,EAAA,aAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;gBACAC,EAAI,EAAA,GAAA;AACJb,gBAAAA,WAAAA,EAAa,EAAE;gBACfc,QAAU,EAAA;AACZ,aAAA;AACA,YAAA;gBACEN,IAAMO,EAAAA,YAAAA;gBACNL,SAAW,EAAA;oBACTC,EAAI,EAAA,oBAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;gBACAC,EAAI,EAAA,cAAA;AACJb,gBAAAA,WAAAA,EAAaA,WAAYgB,CAAAA,WAAW,EAAEC,IAAAA,IAAQ,EAAE;gBAChDH,QAAU,EAAA;AACZ,aAAA;AACA,YAAA;gBACEN,IAAMU,EAAAA,GAAAA;gBACNR,SAAW,EAAA;oBACTC,EAAI,EAAA,iBAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;gBACAC,EAAI,EAAA,WAAA;;;AAGJb,gBAAAA,WAAAA,EAAa,EAAE;gBACfmB,kBAAoB,EAAA,CAAA;gBACpBL,QAAU,EAAA;AACZ;AACD,SAAA;AACDM,QAAAA,mBAAAA,EAAqB,EAAE;QACvBC,SAAW,EAAA;AACb,KAAA,CAAA;AACA,IAAA,MAAMC,sBAAyBjB,GAAAA,KAAAA,CAAMkB,MAAM,CAACpB,wBAAwBI,mBAAmB,CAAA;AAEvFF,IAAAA,KAAAA,CAAMmB,SAAS,CAAC,IAAA;QACd,eAAeC,oBAAAA,GAAAA;YACb,MAAMC,4BAAAA,GAA+B,MAAMC,qBAAAA,CACzC7B,IACAH,EAAAA,uBAAAA,CAAAA;AAGF,YAAA,MAAMiC,gCAAgC,MAAMC,eAAAA,CAC1CP,sBAAuBQ,CAAAA,OAAO,EAC9BpC,kBACAC,EAAAA,uBAAAA,CAAAA;YAGFS,0BAA2B,CAAA,CAACP,SAAW;AACrC,oBAAA,GAAGA,KAAK;oBACRU,mBAAqBqB,EAAAA,6BAAAA;oBACrBR,mBAAqBM,EAAAA,4BAAAA;oBACrBL,SAAW,EAAA;iBACb,CAAA,CAAA;AACF;AAEAI,QAAAA,oBAAAA,EAAAA;KACC,EAAA;AACDrB,QAAAA,0BAAAA;AACAkB,QAAAA,sBAAAA;AACAxB,QAAAA,IAAAA;AACAE,QAAAA,WAAAA;AACAN,QAAAA,kBAAAA;AACAC,QAAAA;AACD,KAAA,CAAA;IAED,OAAOQ,uBAAAA;AACT;AAEA;;AAEkG,qGAElG,MAAM0B,eAAkB,GAAA,OACtBE,sBACArC,EAAAA,kBAAAA,GAA8B,KAAK,EACnCC,uBAAAA,GAAAA;AAEA,IAAA,MAAMqC,8BAAiC,GAAA,MAAMC,OAAQC,CAAAA,GAAG,CACtDH,sBAAAA,CAAuBI,GAAG,CAAC,CAAC,EAAEnC,WAAW,EAAE,GAAKL,uBAAwBK,CAAAA,WAAAA,CAAAA,CAAAA,CAAAA;AAG1E,IAAA,MAAM4B,6BAAgCG,GAAAA,sBAAAA,CAAuBK,MAAM,CACjE,CAACC,CAAAA,EAAGC,KAAUN,GAAAA,8BAA8B,CAACM,KAAAA,CAAM,CAACC,MAAM,GAAG,CAAA,CAAA;IAG/D,MAAMC,iBAAAA,GAAoBZ,8BAA8Ba,SAAS,CAC/D,CAACC,GAAQA,GAAAA,GAAAA,CAAI7B,EAAE,KAAK,WAAA,CAAA;IAGtB,IAAI2B,iBAAAA,KAAsB,CAAC,CAAG,EAAA;AAC5B,QAAA,OAAO,EAAE;AACX;AAEA,IAAA,MAAMG,8BAA8BC,SAAUhB,CAAAA,6BAAAA,CAAAA;AAE9Ce,IAAAA,2BAA2B,CAACH,iBAAkB,CAAA,CAACrB,kBAAkB,GAAGzB,qBAAqB,CAAI,GAAA,CAAA;IAE7F,OAAOiD,2BAAAA;AACT,CAAA;AAEA,MAAMhB,qBAAAA,GAAwB,OAC5BkB,sBACAlD,EAAAA,uBAAAA,GAAAA;AAEA,IAAA,MAAMmD,6BAAgC,GAAA,MAAMb,OAAQC,CAAAA,GAAG,CACrDW,sBAAAA,CAAuBV,GAAG,CAAC,CAAC,EAAEnC,WAAW,EAAE,GAAKL,uBAAwBK,CAAAA,WAAAA,CAAAA,CAAAA,CAAAA;AAG1E,IAAA,MAAM0B,4BAA+BmB,GAAAA,sBAAAA,CAAuBT,MAAM,CAChE,CAACC,CAAAA,EAAGC,KAAUQ,GAAAA,6BAA6B,CAACR,KAAAA,CAAM,CAACC,MAAM,GAAG,CAAA,CAAA;IAG9D,OAAOb,4BAAAA;AACT,CAAA;;;;"}