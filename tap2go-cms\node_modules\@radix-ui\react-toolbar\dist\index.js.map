{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;A;;;;;;;;;;;ACcA;;oGAEA,CAEA,MAAMuB,kCAAY,GAAG,SAArB,AAAA;AAGA,MAAM,CAACC,0CAAD,EAAuBxB,yCAAvB,CAAA,GAA6Ce,6CAAkB,CAACQ,kCAAD,EAAe;IAClFN,0DADkF;IAElFI,qDAFkF;CAAf,CAArE,AAAA;AAIA,MAAMI,8CAAwB,GAAGR,0DAA2B,EAA5D,AAAA;AACA,MAAMS,yCAAmB,GAAGL,qDAAsB,EAAlD,AAAA;AAOA,MAAM,CAACM,qCAAD,EAAkBC,uCAAlB,CAAA,GACJJ,0CAAoB,CAAsBD,kCAAtB,CADtB,AAAA;AAWA,MAAMtB,yCAAO,GAAA,aAAGY,CAAAA,uBAAA,CACd,CAACiB,KAAD,EAAmCC,YAAnC,GAAoD;IAClD,MAAM,E,gBAAEC,cAAF,CAAA,eAAkBC,WAAW,GAAG,YAAhC,G,KAA8CC,GAA9C,CAAA,QAAmDC,IAAI,GAAG,IAA1D,GAAgE,GAAGC,YAAH,EAAhE,GAAoFN,KAA1F,AAAM;IACN,MAAMO,qBAAqB,GAAGZ,8CAAwB,CAACO,cAAD,CAAtD,AAAA;IACA,MAAMM,SAAS,GAAGhB,yCAAY,CAACY,GAAD,CAA9B,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,qCAAD,EADF;QACmB,KAAK,EAAEF,cAAxB;QAAwC,WAAW,EAAEC,WAArD;QAAkE,GAAG,EAAEK,SAAL;KAAlE,EAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EADF,2DAAA,CAAA;QAEI,OAAO,EAAP,IAAA;KADF,EAEMD,qBAFN,EAAA;QAGE,WAAW,EAAEJ,WAHf;QAIE,GAAG,EAAEK,SAJP;QAKE,IAAI,EAAEH,IAAN;KALF,CAAA,EAAA,aAOE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAPF,2DAAA,CAAA;QAQI,IAAI,EAAC,SADP;QAEE,kBAAA,EAAkBF,WAFpB;QAGE,GAAG,EAAEK,SAAL;KAHF,EAIMF,YAJN,EAAA;QAKE,GAAG,EAAEL,YAAL;KALF,CAAA,CAPF,CADF,CADF,CASM;CAdM,CAAhB,AAwBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMQ,oCAAc,GAAG,kBAAvB,AAAA;AAMA,MAAMrC,yCAAgB,GAAA,aAAGW,CAAAA,uBAAA,CACvB,CAACiB,KAAD,EAA4CC,YAA5C,GAA6D;IAC3D,MAAM,E,gBAAEC,cAAF,CAAA,EAAkB,GAAGQ,cAAH,EAAlB,GAAwCV,KAA9C,AAAM;IACN,MAAMW,OAAO,GAAGb,uCAAiB,CAACW,oCAAD,EAAiBP,cAAjB,CAAjC,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,iCAAD,EADF,2DAAA,CAAA;QAEI,WAAW,EAAES,OAAO,CAACR,WAAR,KAAwB,YAAxB,GAAuC,UAAvC,GAAoD,YAAjE;KADF,EAEMO,cAFN,EAAA;QAGE,GAAG,EAAET,YAAL;KAHF,CAAA,CADF,CACE;CALmB,CAAzB,AAWG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,oCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMW,iCAAW,GAAG,eAApB,AAAA;AAMA,MAAMvC,yCAAa,GAAA,aAAGU,CAAAA,uBAAA,CACpB,CAACiB,KAAD,EAAyCC,YAAzC,GAA0D;IACxD,MAAM,E,gBAAEC,cAAF,CAAA,EAAkB,GAAGW,WAAH,EAAlB,GAAqCb,KAA3C,AAAM;IACN,MAAMO,qBAAqB,GAAGZ,8CAAwB,CAACO,cAAD,CAAtD,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EADF,2DAAA,CAAA;QACyB,OAAO,EAAP,IAAA;KAAvB,EAAmCK,qBAAnC,EAAA;QAA0D,SAAS,EAAE,CAACP,KAAK,CAACc,QAAlB;KAA1D,CAAA,EAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,MAAX,EADF,2DAAA,CAAA;QACoB,IAAI,EAAC,QAAL;KAAlB,EAAoCD,WAApC,EAAA;QAAiD,GAAG,EAAEZ,YAAL;KAAjD,CAAA,CADF,CADF,CAEI;CANc,CAAtB,AASG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMc,+BAAS,GAAG,aAAlB,AAAA;AAMA,MAAMzC,yCAAW,GAAA,aAAGS,CAAAA,uBAAA,CAClB,CAACiB,KAAD,EAAuCC,YAAvC,GAAwD;IACtD,MAAM,E,gBAAEC,cAAF,CAAA,EAAkB,GAAGc,SAAH,EAAlB,GAAmChB,KAAzC,AAAM;IACN,MAAMO,qBAAqB,GAAGZ,8CAAwB,CAACO,cAAD,CAAtD,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EADF,2DAAA,CAAA;QACyB,OAAO,EAAP,IAAA;KAAvB,EAAmCK,qBAAnC,EAAA;QAA0D,SAAS,EAAT,IAAA;KAA1D,CAAA,EAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,CAAX,EAAA,2DAAA,CAAA,EAAA,EACMS,SADN,EADF;QAGI,GAAG,EAAEf,YAFP;QAGE,SAAS,EAAEjB,4CAAoB,CAACgB,KAAK,CAACiB,SAAP,EAAmBC,CAAAA,KAAD,GAAW;YAC1D,IAAIA,KAAK,CAACC,GAAN,KAAc,GAAlB,EAAuBD,KAAK,CAACE,aAAN,CAAoBC,KAApB,EAAvB,CAAA;SAD6B,CAE9B;KALH,CAAA,CADF,CADF,CAEI;CANY,CAApB,AAeG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,+BAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMC,uCAAiB,GAAG,oBAA1B,AAAA;AAOA,MAAM/C,yCAAkB,GAAA,aAAGQ,CAAAA,uBAAA,CAIzB,CACEiB,KADF,EAEEC,YAFF,GAGK;IACH,MAAM,E,gBAAEC,cAAF,CAAA,EAAkB,GAAGqB,gBAAH,EAAlB,GAA0CvB,KAAhD,AAAM;IACN,MAAMW,OAAO,GAAGb,uCAAiB,CAACwB,uCAAD,EAAoBpB,cAApB,CAAjC,AAAA;IACA,MAAMsB,gBAAgB,GAAG5B,yCAAmB,CAACM,cAAD,CAA5C,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EADF,2DAAA,CAAA;QAEI,kBAAA,EAAkBS,OAAO,CAACR,WAD5B;QAEE,GAAG,EAAEQ,OAAO,CAACP,GAAb;KAFF,EAGMoB,gBAHN,EAIMD,gBAJN,EAAA;QAKE,GAAG,EAAEtB,YALP;QAME,WAAW,EAAE,KAAb;KANF,CAAA,CADF,CACE;CAZqB,CAA3B,AAqBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,uCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMwB,sCAAgB,GAAG,mBAAzB,AAAA;AAMA,MAAMjD,yCAAiB,GAAA,aAAGO,CAAAA,uBAAA,CACxB,CAACiB,KAAD,EAA6CC,YAA7C,GAA8D;IAC5D,MAAM,E,gBAAEC,cAAF,CAAA,EAAkB,GAAGwB,eAAH,EAAlB,GAAyC1B,KAA/C,AAAM;IACN,MAAMwB,gBAAgB,GAAG5B,yCAAmB,CAACM,cAAD,CAA5C,AAAA;IACA,MAAMyB,KAAK,GAAG;QAAEzB,cAAc,EAAEF,KAAK,CAACE,cAAtBA;KAAhB,AAAc;IAEd,OAAA,aACE,CAAA,0BAAA,CAAC,yCAAD,EADF,2DAAA,CAAA;QACiB,OAAO,EAAP,IAAA;KAAf,EAA2ByB,KAA3B,CAAA,EAAA,aACE,CAAA,0BAAA,CAAC,mCAAD,EAAA,2DAAA,CAAA,EAAA,EAA+BH,gBAA/B,EAAqDE,eAArD,EADF;QACwE,GAAG,EAAEzB,YAAL;KAAtE,CAAA,CADF,CADF,CAEI;CARkB,CAA1B,AAWG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,sCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,MAAMxB,yCAAI,GAAGN,yCAAb,AAAA;AACA,MAAMO,yCAAS,GAAGN,yCAAlB,AAAA;AACA,MAAMO,yCAAM,GAAGN,yCAAf,AAAA;AACA,MAAMO,yCAAI,GAAGN,yCAAb,AAAA;AACA,MAAMO,yCAAW,GAAGN,yCAApB,AAAA;AACA,MAAMO,yCAAU,GAAGN,yCAAnB,AAAA;;AD/NA", "sources": ["packages/react/toolbar/src/index.ts", "packages/react/toolbar/src/Toolbar.tsx"], "sourcesContent": ["export {\n  createToolbarScope,\n  //\n  Toolbar,\n  ToolbarSeparator,\n  ToolbarButton,\n  ToolbarLink,\n  ToolbarToggleGroup,\n  ToolbarToggleItem,\n  //\n  Root,\n  Separator,\n  Button,\n  Link,\n  ToggleGroup,\n  ToggleItem,\n} from './Toolbar';\nexport type {\n  ToolbarProps,\n  ToolbarSeparatorProps,\n  ToolbarButtonProps,\n  ToolbarLinkProps,\n  ToolbarToggleGroupSingleProps,\n  ToolbarToggleGroupMultipleProps,\n  ToolbarToggleItemProps,\n} from './Toolbar';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\nimport * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group';\nimport { createToggleGroupScope } from '@radix-ui/react-toggle-group';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Toolbar\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLBAR_NAME = 'Toolbar';\n\ntype ScopedProps<P> = P & { __scopeToolbar?: Scope };\nconst [createToolbarContext, createToolbarScope] = createContextScope(TOOLBAR_NAME, [\n  createRovingFocusGroupScope,\n  createToggleGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\nconst useToggleGroupScope = createToggleGroupScope();\n\ntype RovingFocusGroupProps = Radix.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype ToolbarContextValue = {\n  orientation: RovingFocusGroupProps['orientation'];\n  dir: RovingFocusGroupProps['dir'];\n};\nconst [ToolbarProvider, useToolbarContext] =\n  createToolbarContext<ToolbarContextValue>(TOOLBAR_NAME);\n\ntype ToolbarElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToolbarProps extends PrimitiveDivProps {\n  orientation?: RovingFocusGroupProps['orientation'];\n  loop?: RovingFocusGroupProps['loop'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst Toolbar = React.forwardRef<ToolbarElement, ToolbarProps>(\n  (props: ScopedProps<ToolbarProps>, forwardedRef) => {\n    const { __scopeToolbar, orientation = 'horizontal', dir, loop = true, ...toolbarProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToolbar);\n    const direction = useDirection(dir);\n    return (\n      <ToolbarProvider scope={__scopeToolbar} orientation={orientation} dir={direction}>\n        <RovingFocusGroup.Root\n          asChild\n          {...rovingFocusGroupScope}\n          orientation={orientation}\n          dir={direction}\n          loop={loop}\n        >\n          <Primitive.div\n            role=\"toolbar\"\n            aria-orientation={orientation}\n            dir={direction}\n            {...toolbarProps}\n            ref={forwardedRef}\n          />\n        </RovingFocusGroup.Root>\n      </ToolbarProvider>\n    );\n  }\n);\n\nToolbar.displayName = TOOLBAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'ToolbarSeparator';\n\ntype ToolbarSeparatorElement = React.ElementRef<typeof SeparatorPrimitive.Root>;\ntype SeparatorProps = Radix.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>;\ninterface ToolbarSeparatorProps extends SeparatorProps {}\n\nconst ToolbarSeparator = React.forwardRef<ToolbarSeparatorElement, ToolbarSeparatorProps>(\n  (props: ScopedProps<ToolbarSeparatorProps>, forwardedRef) => {\n    const { __scopeToolbar, ...separatorProps } = props;\n    const context = useToolbarContext(SEPARATOR_NAME, __scopeToolbar);\n    return (\n      <SeparatorPrimitive.Root\n        orientation={context.orientation === 'horizontal' ? 'vertical' : 'horizontal'}\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nToolbarSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUTTON_NAME = 'ToolbarButton';\n\ntype ToolbarButtonElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToolbarButtonProps extends PrimitiveButtonProps {}\n\nconst ToolbarButton = React.forwardRef<ToolbarButtonElement, ToolbarButtonProps>(\n  (props: ScopedProps<ToolbarButtonProps>, forwardedRef) => {\n    const { __scopeToolbar, ...buttonProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToolbar);\n    return (\n      <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!props.disabled}>\n        <Primitive.button type=\"button\" {...buttonProps} ref={forwardedRef} />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nToolbarButton.displayName = BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarLink\n * -----------------------------------------------------------------------------------------------*/\n\nconst LINK_NAME = 'ToolbarLink';\n\ntype ToolbarLinkElement = React.ElementRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = Radix.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface ToolbarLinkProps extends PrimitiveLinkProps {}\n\nconst ToolbarLink = React.forwardRef<ToolbarLinkElement, ToolbarLinkProps>(\n  (props: ScopedProps<ToolbarLinkProps>, forwardedRef) => {\n    const { __scopeToolbar, ...linkProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToolbar);\n    return (\n      <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable>\n        <Primitive.a\n          {...linkProps}\n          ref={forwardedRef}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === ' ') event.currentTarget.click();\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nToolbarLink.displayName = LINK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarToggleGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_GROUP_NAME = 'ToolbarToggleGroup';\n\ntype ToolbarToggleGroupElement = React.ElementRef<typeof ToggleGroupPrimitive.Root>;\ntype ToggleGroupProps = Radix.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root>;\ninterface ToolbarToggleGroupSingleProps extends Extract<ToggleGroupProps, { type: 'single' }> {}\ninterface ToolbarToggleGroupMultipleProps extends Extract<ToggleGroupProps, { type: 'multiple' }> {}\n\nconst ToolbarToggleGroup = React.forwardRef<\n  ToolbarToggleGroupElement,\n  ToolbarToggleGroupSingleProps | ToolbarToggleGroupMultipleProps\n>(\n  (\n    props: ScopedProps<ToolbarToggleGroupSingleProps | ToolbarToggleGroupMultipleProps>,\n    forwardedRef\n  ) => {\n    const { __scopeToolbar, ...toggleGroupProps } = props;\n    const context = useToolbarContext(TOGGLE_GROUP_NAME, __scopeToolbar);\n    const toggleGroupScope = useToggleGroupScope(__scopeToolbar);\n    return (\n      <ToggleGroupPrimitive.Root\n        data-orientation={context.orientation}\n        dir={context.dir}\n        {...toggleGroupScope}\n        {...toggleGroupProps}\n        ref={forwardedRef}\n        rovingFocus={false}\n      />\n    );\n  }\n);\n\nToolbarToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarToggleItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_ITEM_NAME = 'ToolbarToggleItem';\n\ntype ToolbarToggleItemElement = React.ElementRef<typeof ToggleGroupPrimitive.Item>;\ntype ToggleGroupItemProps = Radix.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item>;\ninterface ToolbarToggleItemProps extends ToggleGroupItemProps {}\n\nconst ToolbarToggleItem = React.forwardRef<ToolbarToggleItemElement, ToolbarToggleItemProps>(\n  (props: ScopedProps<ToolbarToggleItemProps>, forwardedRef) => {\n    const { __scopeToolbar, ...toggleItemProps } = props;\n    const toggleGroupScope = useToggleGroupScope(__scopeToolbar);\n    const scope = { __scopeToolbar: props.__scopeToolbar };\n\n    return (\n      <ToolbarButton asChild {...scope}>\n        <ToggleGroupPrimitive.Item {...toggleGroupScope} {...toggleItemProps} ref={forwardedRef} />\n      </ToolbarButton>\n    );\n  }\n);\n\nToolbarToggleItem.displayName = TOGGLE_ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = Toolbar;\nconst Separator = ToolbarSeparator;\nconst Button = ToolbarButton;\nconst Link = ToolbarLink;\nconst ToggleGroup = ToolbarToggleGroup;\nconst ToggleItem = ToolbarToggleItem;\n\nexport {\n  createToolbarScope,\n  //\n  Toolbar,\n  ToolbarSeparator,\n  ToolbarButton,\n  ToolbarLink,\n  ToolbarToggleGroup,\n  ToolbarToggleItem,\n  //\n  Root,\n  Separator,\n  Button,\n  Link,\n  ToggleGroup,\n  ToggleItem,\n};\nexport type {\n  ToolbarProps,\n  ToolbarSeparatorProps,\n  ToolbarButtonProps,\n  ToolbarLinkProps,\n  ToolbarToggleGroupSingleProps,\n  ToolbarToggleGroupMultipleProps,\n  ToolbarToggleItemProps,\n};\n"], "names": ["createToolbarScope", "<PERSON><PERSON><PERSON>", "ToolbarSeparator", "<PERSON><PERSON>barButton", "ToolbarLink", "ToolbarToggleGroup", "ToolbarToggleItem", "Root", "Separator", "<PERSON><PERSON>", "Link", "ToggleGroup", "ToggleItem", "React", "composeEventHandlers", "createContextScope", "RovingFocusGroup", "createRovingFocusGroupScope", "Primitive", "SeparatorPrimitive", "ToggleGroupPrimitive", "createToggleGroupScope", "useDirection", "TOOLBAR_NAME", "createToolbarContext", "useRovingFocusGroupScope", "useToggleGroupScope", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useToolbarContext", "forwardRef", "props", "forwardedRef", "__scopeToolbar", "orientation", "dir", "loop", "toolbarProps", "rovingFocusGroupScope", "direction", "SEPARATOR_NAME", "separatorProps", "context", "BUTTON_NAME", "buttonProps", "disabled", "LINK_NAME", "linkProps", "onKeyDown", "event", "key", "currentTarget", "click", "TOGGLE_GROUP_NAME", "toggleGroupProps", "toggleGroupScope", "TOGGLE_ITEM_NAME", "toggleItemProps", "scope"], "version": 3, "file": "index.js.map"}