{"version": 3, "file": "token.js", "sources": ["../../../../../server/src/services/token.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport _ from 'lodash';\nimport jwt from 'jsonwebtoken';\nimport type { AdminUser } from '../../../shared/contracts/shared';\n\nconst defaultJwtOptions = { expiresIn: '30d' };\n\nexport type TokenOptions = {\n  expiresIn?: string;\n  [key: string]: unknown;\n};\n\nexport type TokenPayload = {\n  id: AdminUser['id'];\n};\n\nexport type AdminAuthConfig = {\n  secret: string;\n  options: TokenOptions;\n};\n\nconst getTokenOptions = () => {\n  const { options, secret } = strapi.config.get<AdminAuthConfig>(\n    'admin.auth',\n    {} as AdminAuthConfig\n  );\n\n  return {\n    secret,\n    options: _.merge(defaultJwtOptions, options),\n  };\n};\n\n/**\n * Create a random token\n */\nconst createToken = (): string => {\n  return crypto.randomBytes(20).toString('hex');\n};\n\n/**\n * Creates a JWT token for an administration user\n * @param user - admin user\n */\nconst createJwtToken = (user: { id: AdminUser['id'] }) => {\n  const { options, secret } = getTokenOptions();\n\n  return jwt.sign({ id: user.id }, secret, options);\n};\n\n/**\n * Tries to decode a token an return its payload and if it is valid\n * @param token - a token to decode\n * @return decodeInfo - the decoded info\n */\nconst decodeJwtToken = (\n  token: string\n): { payload: TokenPayload; isValid: true } | { payload: null; isValid: false } => {\n  const { secret } = getTokenOptions();\n\n  try {\n    const payload = jwt.verify(token, secret) as TokenPayload;\n    return { payload, isValid: true };\n  } catch (err) {\n    return { payload: null, isValid: false };\n  }\n};\n\nconst checkSecretIsDefined = () => {\n  if (strapi.config.get('admin.serveAdminPanel') && !strapi.config.get('admin.auth.secret')) {\n    throw new Error(\n      `Missing auth.secret. Please set auth.secret in config/admin.js (ex: you can generate one using Node with \\`crypto.randomBytes(16).toString('base64')\\`).\nFor security reasons, prefer storing the secret in an environment variable and read it in config/admin.js. See https://docs.strapi.io/developer-docs/latest/setup-deployment-guides/configurations/optional/environment.html#configuration-using-environment-variables.`\n    );\n  }\n};\n\nexport { createToken, createJwtToken, getTokenOptions, decodeJwtToken, checkSecretIsDefined };\n"], "names": ["defaultJwtOptions", "expiresIn", "getTokenOptions", "options", "secret", "strapi", "config", "get", "_", "merge", "createToken", "crypto", "randomBytes", "toString", "createJwtToken", "user", "jwt", "sign", "id", "decodeJwtToken", "token", "payload", "verify", "<PERSON><PERSON><PERSON><PERSON>", "err", "checkSecretIsDefined", "Error"], "mappings": ";;;;;;AAKA,MAAMA,iBAAoB,GAAA;IAAEC,SAAW,EAAA;AAAM,CAAA;AAgB7C,MAAMC,eAAkB,GAAA,IAAA;AACtB,IAAA,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGC,MAAOC,CAAAA,MAAM,CAACC,GAAG,CAC3C,YAAA,EACA,EAAC,CAAA;IAGH,OAAO;AACLH,QAAAA,MAAAA;QACAD,OAASK,EAAAA,CAAAA,CAAEC,KAAK,CAACT,iBAAmBG,EAAAA,OAAAA;AACtC,KAAA;AACF;AAEA;;AAEC,UACKO,WAAc,GAAA,IAAA;AAClB,IAAA,OAAOC,MAAOC,CAAAA,WAAW,CAAC,EAAA,CAAA,CAAIC,QAAQ,CAAC,KAAA,CAAA;AACzC;AAEA;;;IAIA,MAAMC,iBAAiB,CAACC,IAAAA,GAAAA;AACtB,IAAA,MAAM,EAAEZ,OAAO,EAAEC,MAAM,EAAE,GAAGF,eAAAA,EAAAA;IAE5B,OAAOc,GAAAA,CAAIC,IAAI,CAAC;AAAEC,QAAAA,EAAAA,EAAIH,KAAKG;AAAG,KAAA,EAAGd,MAAQD,EAAAA,OAAAA,CAAAA;AAC3C;AAEA;;;;IAKA,MAAMgB,iBAAiB,CACrBC,KAAAA,GAAAA;IAEA,MAAM,EAAEhB,MAAM,EAAE,GAAGF,eAAAA,EAAAA;IAEnB,IAAI;AACF,QAAA,MAAMmB,OAAUL,GAAAA,GAAAA,CAAIM,MAAM,CAACF,KAAOhB,EAAAA,MAAAA,CAAAA;QAClC,OAAO;AAAEiB,YAAAA,OAAAA;YAASE,OAAS,EAAA;AAAK,SAAA;AAClC,KAAA,CAAE,OAAOC,GAAK,EAAA;QACZ,OAAO;YAAEH,OAAS,EAAA,IAAA;YAAME,OAAS,EAAA;AAAM,SAAA;AACzC;AACF;AAEA,MAAME,oBAAuB,GAAA,IAAA;AAC3B,IAAA,IAAIpB,MAAOC,CAAAA,MAAM,CAACC,GAAG,CAAC,uBAAA,CAAA,IAA4B,CAACF,MAAAA,CAAOC,MAAM,CAACC,GAAG,CAAC,mBAAsB,CAAA,EAAA;QACzF,MAAM,IAAImB,MACR,CAAC;uQACgQ,CAAC,CAAA;AAEtQ;AACF;;;;;;;;"}