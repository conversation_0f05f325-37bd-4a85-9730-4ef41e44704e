{"version": 3, "file": "useAuditLogsData.mjs", "sources": ["../../../../../../../../../../ee/admin/src/pages/SettingsPage/pages/AuditLogs/hooks/useAuditLogsData.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useNotification } from '../../../../../../../../admin/src/features/Notifications';\nimport { useAPIErrorHandler } from '../../../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { useQueryParams } from '../../../../../../../../admin/src/hooks/useQueryParams';\nimport { useAdminUsers } from '../../../../../../../../admin/src/services/users';\nimport { useGetAuditLogsQuery } from '../../../../../services/auditLogs';\n\nexport const useAuditLogsData = ({\n  canReadAuditLogs,\n  canReadUsers,\n}: {\n  canReadAuditLogs: boolean;\n  canReadUsers: boolean;\n}) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const [{ query }] = useQueryParams();\n\n  const {\n    data,\n    error,\n    isError: isUsersError,\n    isLoading: isLoadingUsers,\n  } = useAdminUsers(\n    {},\n    {\n      skip: !canReadUsers,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({ type: 'danger', message: formatAPIError(error) });\n    }\n  }, [error, toggleNotification, formatAPIError]);\n\n  const {\n    data: auditLogs,\n    isLoading: isLoadingAuditLogs,\n    isError: isAuditLogsError,\n    error: auditLogsError,\n  } = useGetAuditLogsQuery(query, {\n    refetchOnMountOrArgChange: true,\n    skip: !canReadAuditLogs,\n  });\n\n  React.useEffect(() => {\n    if (auditLogsError) {\n      toggleNotification({ type: 'danger', message: formatAPIError(auditLogsError) });\n    }\n  }, [auditLogsError, toggleNotification, formatAPIError]);\n\n  return {\n    auditLogs,\n    users: data?.users ?? [],\n    isLoading: isLoadingUsers || isLoadingAuditLogs,\n    hasError: isAuditLogsError || isUsersError,\n  };\n};\n"], "names": ["useAuditLogsData", "canReadAuditLogs", "canReadUsers", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "query", "useQueryParams", "data", "error", "isError", "isUsersError", "isLoading", "isLoadingUsers", "useAdminUsers", "skip", "refetchOnMountOrArgChange", "React", "useEffect", "type", "message", "auditLogs", "isLoadingAuditLogs", "isAuditLogsError", "auditLogsError", "useGetAuditLogsQuery", "users", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;MAQaA,gBAAmB,GAAA,CAAC,EAC/BC,gBAAgB,EAChBC,YAAY,EAIb,GAAA;IACC,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,eAAAA,EAAAA;AAC/B,IAAA,MAAM,EAAEC,uBAAAA,EAAyBC,cAAc,EAAE,GAAGC,kBAAAA,EAAAA;AACpD,IAAA,MAAM,CAAC,EAAEC,KAAK,EAAE,CAAC,GAAGC,cAAAA,EAAAA;AAEpB,IAAA,MAAM,EACJC,IAAI,EACJC,KAAK,EACLC,OAASC,EAAAA,YAAY,EACrBC,SAAAA,EAAWC,cAAc,EAC1B,GAAGC,aAAAA,CACF,EACA,EAAA;AACEC,QAAAA,IAAAA,EAAM,CAACf,YAAAA;QACPgB,yBAA2B,EAAA;AAC7B,KAAA,CAAA;AAGFC,IAAAA,KAAAA,CAAMC,SAAS,CAAC,IAAA;AACd,QAAA,IAAIT,KAAO,EAAA;YACTR,kBAAmB,CAAA;gBAAEkB,IAAM,EAAA,QAAA;AAAUC,gBAAAA,OAAAA,EAAShB,cAAeK,CAAAA,KAAAA;AAAO,aAAA,CAAA;AACtE;KACC,EAAA;AAACA,QAAAA,KAAAA;AAAOR,QAAAA,kBAAAA;AAAoBG,QAAAA;AAAe,KAAA,CAAA;AAE9C,IAAA,MAAM,EACJI,IAAAA,EAAMa,SAAS,EACfT,WAAWU,kBAAkB,EAC7BZ,OAASa,EAAAA,gBAAgB,EACzBd,KAAOe,EAAAA,cAAc,EACtB,GAAGC,qBAAqBnB,KAAO,EAAA;QAC9BU,yBAA2B,EAAA,IAAA;AAC3BD,QAAAA,IAAAA,EAAM,CAAChB;AACT,KAAA,CAAA;AAEAkB,IAAAA,KAAAA,CAAMC,SAAS,CAAC,IAAA;AACd,QAAA,IAAIM,cAAgB,EAAA;YAClBvB,kBAAmB,CAAA;gBAAEkB,IAAM,EAAA,QAAA;AAAUC,gBAAAA,OAAAA,EAAShB,cAAeoB,CAAAA,cAAAA;AAAgB,aAAA,CAAA;AAC/E;KACC,EAAA;AAACA,QAAAA,cAAAA;AAAgBvB,QAAAA,kBAAAA;AAAoBG,QAAAA;AAAe,KAAA,CAAA;IAEvD,OAAO;AACLiB,QAAAA,SAAAA;QACAK,KAAOlB,EAAAA,IAAAA,EAAMkB,SAAS,EAAE;AACxBd,QAAAA,SAAAA,EAAWC,cAAkBS,IAAAA,kBAAAA;AAC7BK,QAAAA,QAAAA,EAAUJ,gBAAoBZ,IAAAA;AAChC,KAAA;AACF;;;;"}