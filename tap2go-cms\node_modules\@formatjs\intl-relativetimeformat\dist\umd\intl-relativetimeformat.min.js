!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).IntlRelativeTimeFormat={})}(this,function(e){"use strict";function T(e,t,r){if(void 0===r&&(r=Error),!e)throw new r(t)}function f(e){if(null==e)throw new TypeError("undefined/null cannot be converted to object");return Object(e)}function s(e,t,r,n,o){var a=e[t];if(void 0===a)return o;if("boolean"!==r&&"string"!==r)throw new TypeError("invalid type");if("boolean"===r&&(a=Boolean(a)),"string"===r&&(a=function(e){if("symbol"==typeof e)throw TypeError("Cannot convert a Symbol value to a string");return String(e)}(a)),void 0!==n&&!n.filter(function(e){return e==a}).length)throw new RangeError(a+" is not within "+n.join(", "));return a}function _(e,t,r,n){e.get(t)||e.set(t,Object.create(null)),e.get(t)[r]=n}function h(e,t,r){return function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var o=e.get(t);if(o)return r.reduce(function(e,t){return e[t]=o[t],e},Object.create(null));throw new TypeError(t+" InternalSlot has not been initialized")}(e,t,r)[r]}function w(e){var t=Intl.getCanonicalLocales;return"function"==typeof t?t(e):Intl.DateTimeFormat.supportedLocalesOf(e)}var n,t=(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});function y(e){var u,c,m=function(e,t){for(var r={locale:""},n=0,o=t;n<o.length;n++){var a=o[n],i=a.replace(p,""),l=v(e,i);if(l)return r.locale=l,a!==i&&(r.extension=a.slice(i.length+1,a.length)),r}return r.locale=u(),r},g=(c=u=e,function(e,t){for(var r={locale:""},n=0,o=t;n<o.length;n++){var a=o[n],i=a.replace(p,""),l=v(e,i);if(l)return r.locale=l,a!==i&&(r.extension=a.slice(i.length+1,a.length)),r}return r.locale=c(),r});return function(e,t,r,n,o){for(var a,i=("lookup"===r.localeMatcher?m:g)(e,t),l=i.locale,u={locale:"",dataLocale:l},c="-u",f=0,s=n;f<s.length;f++){var _=s[f],y=o[l];T("object"==typeof y&&null!==y,"locale data "+_+" must be an object");var p=y[_];T(Array.isArray(p),"keyLocaleData for "+_+" must be an array");var v=p[0];T("string"==typeof v||null===v,"value must be string or null");var h,d,b="";!i.extension||void 0!==(h=O(i.extension,_))&&(""!==h?~p.indexOf(h)&&(b="-"+_+"-"+(v=h)):~h.indexOf("true")&&(v="true",b="-"+_)),_ in r&&(T("string"==typeof(d=r[_])||null==d,"optionsValue must be String, Undefined or Null"),~p.indexOf(d)&&d!==v&&(v=d,b="")),u[_]=v,c+=b}return 2<c.length&&(-1===(a=l.indexOf("-x-"))?l+=c:l=l.slice(0,a)+c+l.slice(a,l.length),l=w(l)[0]),u.locale=l,u}}function O(e,t){T(2===t.length,"key must have 2 elements");var r=e.length,n="-"+t+"-",o=e.indexOf(n);if(-1!==o){for(var a=o+4,i=a,l=a,u=!1;!u;){var c=e.indexOf("-",l);2===(-1===c?r-l:c-l)?u=!0:-1===c?(i=r,u=!0):l=(i=c)+1}return e.slice(a,i)}if(n="-"+t,-1!==(o=e.indexOf(n))&&o+3===r)return""}var p=/-u(?:-[0-9a-z]{2,8})+/gi;function v(e,t){for(var r=t;;){if(~e.indexOf(r))return r;var n=r.lastIndexOf("-");if(!~n)return;2<=n&&"-"===r[n-2]&&(n-=2),r=r.slice(0,n)}}function a(e,t){for(var r=[],n=0,o=t;n<o.length;n++){var a=v(e,o[n].replace(p,""));a&&r.push(a)}return r}var r;r=Error,t(o,r);function o(){var e=null!==r&&r.apply(this,arguments)||this;return e.type="MISSING_LOCALE_DATA",e}var i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},d=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},l=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||0<t--)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},u=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(l(arguments[t]));return e};function c(e,t){var r=function(e,t,r){var n=[e];t[e]&&(e=t[e],n.push(e));var o=r[e];o&&n.push(o);for(var a=e.split("-"),i=a.length;1<i;i--)n.push(a.slice(0,i-1).join("-"));return n}(e,t.aliases,t.parentLocales),n=r.map(function(e){return t.data[e]}).filter(Boolean);if(!n.length)throw new Error('Missing locale data for "'+e+'", lookup hierarchy: '+r.join(", "));return n.reverse(),n.reduce(function(e,t){return i(i({},e),t)},{nu:[]})}var b=/^[a-z0-9]{3,8}(-[a-z0-9]{3,8})*$/i;function m(e,t,r){var n,o,a,i,l=function(e){for(var t=[],r=e.indexOf("{"),n=0,o=0,a=e.length;r<e.length&&-1<r;)T(r<(n=e.indexOf("}",r)),"Invalid pattern "+e),o<r&&t.push({type:"literal",value:e.substring(o,r)}),t.push({type:e.substring(r+1,n),value:void 0}),o=n+1,r=e.indexOf("{",o);return o<a&&t.push({type:"literal",value:e.substring(o,a)}),t}(e),u=[];try{for(var c=d(l),f=c.next();!f.done;f=c.next()){var s=f.value;if("literal"===s.type)u.push({type:"literal",value:s.value});else{T("0"===s.type,"Malformed pattern "+e);try{for(var _=(a=void 0,d(r)),y=_.next();!y.done;y=_.next()){var p=y.value;u.push({type:p.type,value:p.value,unit:t})}}catch(e){a={error:e}}finally{try{y&&!y.done&&(i=_.return)&&i.call(_)}finally{if(a)throw a.error}}}}}catch(e){n={error:e}}finally{try{f&&!f.done&&(o=c.return)&&o.call(c)}finally{if(n)throw n.error}}return u}function g(e){return e+""}function L(e,t,r,n){if(T("number"==typeof r,"value must be number, instead got "+typeof r,TypeError),T("string"==typeof n,"unit must be number, instead got "+typeof r,TypeError),isNaN(r)||r===1/0||r===-1/0)throw new RangeError("Invalid value "+r);var o=function(e){if(T("string"==typeof e,"unit must be a string, instead got "+typeof e,TypeError),"seconds"===e)return"second";if("minutes"===e)return"minute";if("hours"===e)return"hour";if("days"===e)return"day";if("weeks"===e)return"week";if("months"===e)return"month";if("quarters"===e)return"quarter";if("years"===e)return"year";if("second"!==e&&"minute"!==e&&"hour"!==e&&"day"!==e&&"week"!==e&&"month"!==e&&"quarter"!==e&&"year"!==e)throw new RangeError("Invalid unit "+e);return e}(n),a=h(e,t,"fields"),i=h(e,t,"style"),l=o;"short"===i?l=n+"-short":"narrow"===i&&(l=n+"-narrow"),l in a||(l=n);var u=a[l];if("auto"===h(e,t,"numeric")&&g(r)in u)return[{type:"literal",value:u[g(r)]}];var c,f,s="future";c=r,f=-0,((Object.is?Object.is(c,f):c===f?0!==c||1/c==1/f:c!=c&&f!=f)||r<0)&&(s="past");var _=u[s],y=h(e,t,"pluralRules"),p=h(e,t,"numberFormat"),v="function"==typeof p.formatToParts?p.formatToParts(Math.abs(r)):[{type:"literal",value:p.format(Math.abs(r)),unit:n}];return m(_[y.select(r)],o,v)}var E=(N.prototype.format=function(e,t){if("object"!=typeof this)throw new TypeError("format was called on a non-object");if(!h(N.__INTERNAL_SLOT_MAP__,this,"initializedRelativeTimeFormat"))throw new TypeError("format was called on a invalid context");return L(N.__INTERNAL_SLOT_MAP__,this,Number(e),g(t)).map(function(e){return e.value}).join("")},N.prototype.formatToParts=function(e,t){if("object"!=typeof this)throw new TypeError("formatToParts was called on a non-object");if(!h(N.__INTERNAL_SLOT_MAP__,this,"initializedRelativeTimeFormat"))throw new TypeError("formatToParts was called on a invalid context");return L(N.__INTERNAL_SLOT_MAP__,this,Number(e),g(t))},N.prototype.resolvedOptions=function(){if("object"!=typeof this)throw new TypeError("resolvedOptions was called on a non-object");if(!h(N.__INTERNAL_SLOT_MAP__,this,"initializedRelativeTimeFormat"))throw new TypeError("resolvedOptions was called on a invalid context");return{locale:h(N.__INTERNAL_SLOT_MAP__,this,"locale"),style:h(N.__INTERNAL_SLOT_MAP__,this,"style"),numeric:h(N.__INTERNAL_SLOT_MAP__,this,"numeric"),numberingSystem:h(N.__INTERNAL_SLOT_MAP__,this,"numberingSystem")}},N.supportedLocalesOf=function(e,t){return r=N.availableLocales,n=w(e),void 0!==(o=t)&&s(o=f(o),"localeMatcher","string",["lookup","best fit"],"best fit"),a(r,n);var r,n,o},N.__addLocaleData=function(){for(var t,e,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];function o(t){Object.keys(u(t.availableLocales,Object.keys(t.aliases),Object.keys(t.parentLocales)).reduce(function(e,t){return e[t]=!0,e},{})).forEach(function(e){try{N.localeData[e]=c(e,t)}catch(e){}})}try{for(var a=d(r),i=a.next();!i.done;i=a.next())o(i.value)}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}N.availableLocales=Object.keys(N.localeData),N.__defaultLocale||(N.__defaultLocale=N.availableLocales[0])},N.getDefaultLocale=function(){return N.__defaultLocale},N.localeData={},N.availableLocales=[],N.__defaultLocale="en",N.relevantExtensionKeys=["nu"],N.polyfilled=!0,N.__INTERNAL_SLOT_MAP__=new WeakMap,N);function N(e,t){if(!(this&&this instanceof N?this.constructor:void 0))throw new TypeError("Intl.RelativeTimeFormat must be called with 'new'");_(N.__INTERNAL_SLOT_MAP__,this,"initializedRelativeTimeFormat",!0);var r=w(e),n=Object.create(null),o=void 0===t?Object.create(null):f(t),a=s(o,"localeMatcher","string",["best fit","lookup"],"best fit");n.localeMatcher=a;var i=s(o,"numberingSystem","string",void 0,void 0);if(void 0!==i&&!b.test(i))throw new RangeError("Invalid numbering system "+i);n.nu=i;var l=y(N.getDefaultLocale)(N.availableLocales,r,n,N.relevantExtensionKeys,N.localeData),u=l.locale,c=l.nu;_(N.__INTERNAL_SLOT_MAP__,this,"locale",u),_(N.__INTERNAL_SLOT_MAP__,this,"style",s(o,"style","string",["long","narrow","short"],"long")),_(N.__INTERNAL_SLOT_MAP__,this,"numeric",s(o,"numeric","string",["always","auto"],"always")),_(N.__INTERNAL_SLOT_MAP__,this,"fields",N.localeData[u]),_(N.__INTERNAL_SLOT_MAP__,this,"numberFormat",new Intl.NumberFormat(e)),_(N.__INTERNAL_SLOT_MAP__,this,"pluralRules",new Intl.PluralRules(e)),_(N.__INTERNAL_SLOT_MAP__,this,"numberingSystem",c)}try{"undefined"!=typeof Symbol&&Object.defineProperty(E.prototype,Symbol.toStringTag,{value:"Intl.RelativeTimeFormat",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype.constructor,"length",{value:0,writable:!1,enumerable:!1,configurable:!0}),Object.defineProperty(E.supportedLocalesOf,"length",{value:1,writable:!1,enumerable:!1,configurable:!0})}catch(e){}e.default=E,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=intl-relativetimeformat.min.js.map
