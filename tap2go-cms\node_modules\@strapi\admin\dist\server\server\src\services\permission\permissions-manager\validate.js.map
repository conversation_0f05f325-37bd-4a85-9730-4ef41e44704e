{"version": 3, "file": "validate.js", "sources": ["../../../../../../../server/src/services/permission/permissions-manager/validate.ts"], "sourcesContent": ["import { subject as asSubject, detectSubjectType } from '@casl/ability';\nimport { permittedFieldsOf } from '@casl/ability/extra';\nimport {\n  defaults,\n  omit,\n  isArray,\n  isEmpty,\n  isNil,\n  flatMap,\n  some,\n  prop,\n  uniq,\n  intersection,\n  getOr,\n  isObject,\n} from 'lodash/fp';\n\nimport { contentTypes, traverseEntity, traverse, validate, async, errors } from '@strapi/utils';\nimport { ADMIN_USER_ALLOWED_FIELDS } from '../../../domain/user';\n\nconst { ValidationError } = errors;\nconst { throwPassword, throwDisallowedFields } = validate.visitors;\n\nconst { constants, isScalarAttribute, getNonVisibleAttributes, getWritableAttributes } =\n  contentTypes;\nconst {\n  ID_ATTRIBUTE,\n  DOC_ID_ATTRIBUTE,\n  CREATED_AT_ATTRIBUTE,\n  UPDATED_AT_ATTRIBUTE,\n  PUBLISHED_AT_ATTRIBUTE,\n  CREATED_BY_ATTRIBUTE,\n  UPDATED_BY_ATTRIBUTE,\n} = constants;\n\nconst COMPONENT_FIELDS = ['__component'];\n\nconst STATIC_FIELDS = [ID_ATTRIBUTE, DOC_ID_ATTRIBUTE];\n\nconst throwInvalidKey = ({ key, path }: { key: string; path?: string | null }) => {\n  const msg = path && path !== key ? `Invalid key ${key} at ${path}` : `Invalid key ${key}`;\n\n  throw new ValidationError(msg);\n};\n\nexport default ({ action, ability, model }: any) => {\n  const schema = strapi.getModel(model);\n\n  const ctx = {\n    schema,\n    getModel: strapi.getModel.bind(strapi),\n  };\n\n  const createValidateQuery = (options = {} as any) => {\n    const { fields } = options;\n\n    // TODO: validate relations to admin users in all validators\n    const permittedFields = fields.shouldIncludeAll ? null : getQueryFields(fields.permitted);\n\n    const validateFilters = async.pipe(\n      traverse.traverseQueryFilters(throwDisallowedFields(permittedFields), ctx),\n      traverse.traverseQueryFilters(throwDisallowedAdminUserFields, ctx),\n      traverse.traverseQueryFilters(throwPassword, ctx),\n      traverse.traverseQueryFilters(({ key, value, path }) => {\n        if (isObject(value) && isEmpty(value)) {\n          throwInvalidKey({ key, path: path.attribute });\n        }\n      }, ctx)\n    );\n\n    const validateSort = async.pipe(\n      traverse.traverseQuerySort(throwDisallowedFields(permittedFields), ctx),\n      traverse.traverseQuerySort(throwDisallowedAdminUserFields, ctx),\n      traverse.traverseQuerySort(throwPassword, ctx),\n      traverse.traverseQuerySort(({ key, attribute, value, path }) => {\n        if (!isScalarAttribute(attribute) && isEmpty(value)) {\n          throwInvalidKey({ key, path: path.attribute });\n        }\n      }, ctx)\n    );\n\n    const validateFields = async.pipe(\n      traverse.traverseQueryFields(throwDisallowedFields(permittedFields), ctx),\n      traverse.traverseQueryFields(throwPassword, ctx)\n    );\n\n    const validatePopulate = async.pipe(\n      traverse.traverseQueryPopulate(throwDisallowedFields(permittedFields), ctx),\n      traverse.traverseQueryPopulate(throwDisallowedAdminUserFields, ctx),\n      traverse.traverseQueryPopulate(throwHiddenFields, ctx),\n      traverse.traverseQueryPopulate(throwPassword, ctx)\n    );\n\n    return async (query: any) => {\n      if (query.filters) {\n        await validateFilters(query.filters);\n      }\n\n      if (query.sort) {\n        await validateSort(query.sort);\n      }\n\n      if (query.fields) {\n        await validateFields(query.fields);\n      }\n\n      // a wildcard is always valid; its conversion will be handled by the entity service and can be optimized with sanitizer\n      if (query.populate && query.populate !== '*') {\n        await validatePopulate(query.populate);\n      }\n\n      return true;\n    };\n  };\n\n  const createValidateInput = (options = {} as any) => {\n    const { fields } = options;\n\n    const permittedFields = fields.shouldIncludeAll ? null : getInputFields(fields.permitted);\n\n    return async.pipe(\n      // Remove fields hidden from the admin\n      traverseEntity(throwHiddenFields, ctx),\n      // Remove not allowed fields (RBAC)\n      traverseEntity(throwDisallowedFields(permittedFields), ctx),\n      // Remove roles from createdBy & updatedBy fields\n      omitCreatorRoles\n    );\n  };\n\n  const wrapValidate = (createValidateFunction: any) => {\n    // TODO\n    // @ts-expect-error define the correct return type\n    const wrappedValidate = async (data, options = {}): Promise<unknown> => {\n      if (isArray(data)) {\n        return Promise.all(data.map((entity: unknown) => wrappedValidate(entity, options)));\n      }\n\n      const { subject, action: actionOverride } = getDefaultOptions(data, options);\n\n      const permittedFields = permittedFieldsOf(ability, actionOverride, subject, {\n        fieldsFrom: (rule) => rule.fields || [],\n      });\n\n      const hasAtLeastOneRegistered = some(\n        (fields) => !isNil(fields),\n        flatMap(prop('fields'), ability.rulesFor(actionOverride, detectSubjectType(subject)))\n      );\n      const shouldIncludeAllFields = isEmpty(permittedFields) && !hasAtLeastOneRegistered;\n\n      const validateOptions = {\n        ...options,\n        fields: {\n          shouldIncludeAll: shouldIncludeAllFields,\n          permitted: permittedFields,\n          hasAtLeastOneRegistered,\n        },\n      };\n\n      const validateFunction = createValidateFunction(validateOptions);\n\n      return validateFunction(data);\n    };\n\n    return wrappedValidate;\n  };\n\n  const getDefaultOptions = (data: any, options: unknown) => {\n    return defaults({ subject: asSubject(model, data), action }, options);\n  };\n\n  /**\n   * Omit creator fields' (createdBy & updatedBy) roles from the admin API responses\n   */\n  const omitCreatorRoles = omit([`${CREATED_BY_ATTRIBUTE}.roles`, `${UPDATED_BY_ATTRIBUTE}.roles`]);\n\n  /**\n   * Visitor used to remove hidden fields from the admin API responses\n   */\n  const throwHiddenFields = ({ key, schema, path }: any) => {\n    const isHidden = getOr(false, ['config', 'attributes', key, 'hidden'], schema);\n\n    if (isHidden) {\n      throwInvalidKey({ key, path: path.attribute });\n    }\n  };\n\n  /**\n   * Visitor used to omit disallowed fields from the admin users entities & avoid leaking sensitive information\n   */\n  const throwDisallowedAdminUserFields = ({ key, attribute, schema, path }: any) => {\n    if (schema.uid === 'admin::user' && attribute && !ADMIN_USER_ALLOWED_FIELDS.includes(key)) {\n      throwInvalidKey({ key, path: path.attribute });\n    }\n  };\n\n  const getInputFields = (fields = []) => {\n    const nonVisibleAttributes = getNonVisibleAttributes(schema);\n    const writableAttributes = getWritableAttributes(schema);\n\n    const nonVisibleWritableAttributes = intersection(nonVisibleAttributes, writableAttributes);\n\n    return uniq([...fields, ...COMPONENT_FIELDS, ...nonVisibleWritableAttributes]);\n  };\n\n  const getQueryFields = (fields = []) => {\n    return uniq([\n      ...fields,\n      ...STATIC_FIELDS,\n      ...COMPONENT_FIELDS,\n      CREATED_AT_ATTRIBUTE,\n      UPDATED_AT_ATTRIBUTE,\n      PUBLISHED_AT_ATTRIBUTE,\n    ]);\n  };\n\n  return {\n    validateQuery: wrapValidate(createValidateQuery),\n    validateInput: wrapValidate(createValidateInput),\n  };\n};\n"], "names": ["ValidationError", "errors", "throwPassword", "throwDisa<PERSON><PERSON><PERSON>ields", "validate", "visitors", "constants", "isScalarAttribute", "getNonVisibleAttributes", "getWritableAttributes", "contentTypes", "ID_ATTRIBUTE", "DOC_ID_ATTRIBUTE", "CREATED_AT_ATTRIBUTE", "UPDATED_AT_ATTRIBUTE", "PUBLISHED_AT_ATTRIBUTE", "CREATED_BY_ATTRIBUTE", "UPDATED_BY_ATTRIBUTE", "COMPONENT_FIELDS", "STATIC_FIELDS", "throwInvalidKey", "key", "path", "msg", "action", "ability", "model", "schema", "strapi", "getModel", "ctx", "bind", "createValidateQuery", "options", "fields", "permittedFields", "shouldIncludeAll", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "permitted", "validateFilters", "async", "pipe", "traverse", "traverseQueryFilters", "throwD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ields", "value", "isObject", "isEmpty", "attribute", "validateSort", "traverseQuerySort", "validateFields", "traverseQueryFields", "validatePopulate", "traverseQueryPopulate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "query", "filters", "sort", "populate", "createValidateInput", "getInputFields", "traverseEntity", "omitCreatorRoles", "wrapValidate", "createValidateFunction", "wrappedValidate", "data", "isArray", "Promise", "all", "map", "entity", "subject", "actionOverride", "getDefaultOptions", "permittedFieldsOf", "fieldsFrom", "rule", "hasAtLeastOneRegistered", "some", "isNil", "flatMap", "prop", "rulesFor", "detectSubjectType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateOptions", "validateFunction", "defaults", "asSubject", "omit", "isHidden", "getOr", "uid", "ADMIN_USER_ALLOWED_FIELDS", "includes", "nonVisibleAttributes", "writableAttributes", "nonVisibleWritableAttributes", "intersection", "uniq", "validate<PERSON><PERSON>y", "validateInput"], "mappings": ";;;;;;;;AAoBA,MAAM,EAAEA,eAAe,EAAE,GAAGC,YAAAA;AAC5B,MAAM,EAAEC,aAAa,EAAEC,qBAAqB,EAAE,GAAGC,eAASC,QAAQ;AAElE,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAE,GACpFC,kBAAAA;AACF,MAAM,EACJC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACrB,GAAGX,SAAAA;AAEJ,MAAMY,gBAAmB,GAAA;AAAC,IAAA;AAAc,CAAA;AAExC,MAAMC,aAAgB,GAAA;AAACR,IAAAA,YAAAA;AAAcC,IAAAA;AAAiB,CAAA;AAEtD,MAAMQ,kBAAkB,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAyC,GAAA;AAC3E,IAAA,MAAMC,MAAMD,IAAQA,IAAAA,IAAAA,KAASD,GAAM,GAAA,CAAC,YAAY,EAAEA,GAAAA,CAAI,IAAI,EAAEC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAED,IAAI,CAAC;AAEzF,IAAA,MAAM,IAAIrB,eAAgBuB,CAAAA,GAAAA,CAAAA;AAC5B,CAAA;AAEA,4BAAe,CAAA,CAAC,EAAEC,MAAM,WAAEC,SAAO,EAAEC,KAAK,EAAO,GAAA;IAC7C,MAAMC,MAAAA,GAASC,MAAOC,CAAAA,QAAQ,CAACH,KAAAA,CAAAA;AAE/B,IAAA,MAAMI,GAAM,GAAA;AACVH,QAAAA,MAAAA;AACAE,QAAAA,QAAAA,EAAUD,MAAOC,CAAAA,QAAQ,CAACE,IAAI,CAACH,MAAAA;AACjC,KAAA;AAEA,IAAA,MAAMI,mBAAsB,GAAA,CAACC,OAAU,GAAA,EAAS,GAAA;QAC9C,MAAM,EAAEC,MAAM,EAAE,GAAGD,OAAAA;;AAGnB,QAAA,MAAME,kBAAkBD,MAAOE,CAAAA,gBAAgB,GAAG,IAAOC,GAAAA,cAAAA,CAAeH,OAAOI,SAAS,CAAA;AAExF,QAAA,MAAMC,eAAkBC,GAAAA,WAAAA,CAAMC,IAAI,CAChCC,cAASC,CAAAA,oBAAoB,CAACxC,qBAAAA,CAAsBgC,eAAkBL,CAAAA,EAAAA,GAAAA,CAAAA,EACtEY,cAASC,CAAAA,oBAAoB,CAACC,8BAAgCd,EAAAA,GAAAA,CAAAA,EAC9DY,cAASC,CAAAA,oBAAoB,CAACzC,aAAAA,EAAe4B,GAC7CY,CAAAA,EAAAA,cAAAA,CAASC,oBAAoB,CAAC,CAAC,EAAEtB,GAAG,EAAEwB,KAAK,EAAEvB,IAAI,EAAE,GAAA;YACjD,IAAIwB,WAAAA,CAASD,KAAUE,CAAAA,IAAAA,UAAAA,CAAQF,KAAQ,CAAA,EAAA;gBACrCzB,eAAgB,CAAA;AAAEC,oBAAAA,GAAAA;AAAKC,oBAAAA,IAAAA,EAAMA,KAAK0B;AAAU,iBAAA,CAAA;AAC9C;SACClB,EAAAA,GAAAA,CAAAA,CAAAA;AAGL,QAAA,MAAMmB,YAAeT,GAAAA,WAAAA,CAAMC,IAAI,CAC7BC,eAASQ,iBAAiB,CAAC/C,qBAAsBgC,CAAAA,eAAAA,CAAAA,EAAkBL,GACnEY,CAAAA,EAAAA,cAAAA,CAASQ,iBAAiB,CAACN,gCAAgCd,GAC3DY,CAAAA,EAAAA,cAAAA,CAASQ,iBAAiB,CAAChD,aAAe4B,EAAAA,GAAAA,CAAAA,EAC1CY,cAASQ,CAAAA,iBAAiB,CAAC,CAAC,EAAE7B,GAAG,EAAE2B,SAAS,EAAEH,KAAK,EAAEvB,IAAI,EAAE,GAAA;AACzD,YAAA,IAAI,CAACf,iBAAAA,CAAkByC,SAAcD,CAAAA,IAAAA,UAAAA,CAAQF,KAAQ,CAAA,EAAA;gBACnDzB,eAAgB,CAAA;AAAEC,oBAAAA,GAAAA;AAAKC,oBAAAA,IAAAA,EAAMA,KAAK0B;AAAU,iBAAA,CAAA;AAC9C;SACClB,EAAAA,GAAAA,CAAAA,CAAAA;AAGL,QAAA,MAAMqB,cAAiBX,GAAAA,WAAAA,CAAMC,IAAI,CAC/BC,cAASU,CAAAA,mBAAmB,CAACjD,qBAAAA,CAAsBgC,eAAkBL,CAAAA,EAAAA,GAAAA,CAAAA,EACrEY,cAASU,CAAAA,mBAAmB,CAAClD,aAAe4B,EAAAA,GAAAA,CAAAA,CAAAA;QAG9C,MAAMuB,gBAAAA,GAAmBb,YAAMC,IAAI,CACjCC,eAASY,qBAAqB,CAACnD,qBAAsBgC,CAAAA,eAAAA,CAAAA,EAAkBL,GACvEY,CAAAA,EAAAA,cAAAA,CAASY,qBAAqB,CAACV,8BAAAA,EAAgCd,GAC/DY,CAAAA,EAAAA,cAAAA,CAASY,qBAAqB,CAACC,mBAAmBzB,GAClDY,CAAAA,EAAAA,cAAAA,CAASY,qBAAqB,CAACpD,aAAe4B,EAAAA,GAAAA,CAAAA,CAAAA;AAGhD,QAAA,OAAO,OAAO0B,KAAAA,GAAAA;YACZ,IAAIA,KAAAA,CAAMC,OAAO,EAAE;gBACjB,MAAMlB,eAAAA,CAAgBiB,MAAMC,OAAO,CAAA;AACrC;YAEA,IAAID,KAAAA,CAAME,IAAI,EAAE;gBACd,MAAMT,YAAAA,CAAaO,MAAME,IAAI,CAAA;AAC/B;YAEA,IAAIF,KAAAA,CAAMtB,MAAM,EAAE;gBAChB,MAAMiB,cAAAA,CAAeK,MAAMtB,MAAM,CAAA;AACnC;;AAGA,YAAA,IAAIsB,MAAMG,QAAQ,IAAIH,KAAMG,CAAAA,QAAQ,KAAK,GAAK,EAAA;gBAC5C,MAAMN,gBAAAA,CAAiBG,MAAMG,QAAQ,CAAA;AACvC;YAEA,OAAO,IAAA;AACT,SAAA;AACF,KAAA;AAEA,IAAA,MAAMC,mBAAsB,GAAA,CAAC3B,OAAU,GAAA,EAAS,GAAA;QAC9C,MAAM,EAAEC,MAAM,EAAE,GAAGD,OAAAA;AAEnB,QAAA,MAAME,kBAAkBD,MAAOE,CAAAA,gBAAgB,GAAG,IAAOyB,GAAAA,cAAAA,CAAe3B,OAAOI,SAAS,CAAA;QAExF,OAAOE,WAAAA,CAAMC,IAAI;QAEfqB,oBAAeP,CAAAA,iBAAAA,EAAmBzB;QAElCgC,oBAAe3D,CAAAA,qBAAAA,CAAsBgC,eAAkBL,CAAAA,EAAAA,GAAAA,CAAAA;AAEvDiC,QAAAA,gBAAAA,CAAAA;AAEJ,KAAA;AAEA,IAAA,MAAMC,eAAe,CAACC,sBAAAA,GAAAA;;;AAGpB,QAAA,MAAMC,eAAkB,GAAA,OAAOC,IAAMlC,EAAAA,OAAAA,GAAU,EAAE,GAAA;AAC/C,YAAA,IAAImC,WAAQD,IAAO,CAAA,EAAA;gBACjB,OAAOE,OAAAA,CAAQC,GAAG,CAACH,IAAAA,CAAKI,GAAG,CAAC,CAACC,MAAoBN,GAAAA,eAAAA,CAAgBM,MAAQvC,EAAAA,OAAAA,CAAAA,CAAAA,CAAAA;AAC3E;YAEA,MAAM,EAAEwC,OAAO,EAAEjD,MAAAA,EAAQkD,cAAc,EAAE,GAAGC,kBAAkBR,IAAMlC,EAAAA,OAAAA,CAAAA;AAEpE,YAAA,MAAME,eAAkByC,GAAAA,uBAAAA,CAAkBnD,SAASiD,EAAAA,cAAAA,EAAgBD,OAAS,EAAA;AAC1EI,gBAAAA,UAAAA,EAAY,CAACC,IAAAA,GAASA,IAAK5C,CAAAA,MAAM,IAAI;AACvC,aAAA,CAAA;AAEA,YAAA,MAAM6C,uBAA0BC,GAAAA,OAAAA,CAC9B,CAAC9C,MAAAA,GAAW,CAAC+C,QAAM/C,CAAAA,MAAAA,CAAAA,EACnBgD,UAAQC,CAAAA,OAAAA,CAAK,QAAW1D,CAAAA,EAAAA,SAAAA,CAAQ2D,QAAQ,CAACV,gBAAgBW,yBAAkBZ,CAAAA,OAAAA,CAAAA,CAAAA,CAAAA,CAAAA;YAE7E,MAAMa,sBAAAA,GAAyBvC,UAAQZ,CAAAA,eAAAA,CAAAA,IAAoB,CAAC4C,uBAAAA;AAE5D,YAAA,MAAMQ,eAAkB,GAAA;AACtB,gBAAA,GAAGtD,OAAO;gBACVC,MAAQ,EAAA;oBACNE,gBAAkBkD,EAAAA,sBAAAA;oBAClBhD,SAAWH,EAAAA,eAAAA;AACX4C,oBAAAA;AACF;AACF,aAAA;AAEA,YAAA,MAAMS,mBAAmBvB,sBAAuBsB,CAAAA,eAAAA,CAAAA;AAEhD,YAAA,OAAOC,gBAAiBrB,CAAAA,IAAAA,CAAAA;AAC1B,SAAA;QAEA,OAAOD,eAAAA;AACT,KAAA;IAEA,MAAMS,iBAAAA,GAAoB,CAACR,IAAWlC,EAAAA,OAAAA,GAAAA;AACpC,QAAA,OAAOwD,WAAS,CAAA;AAAEhB,YAAAA,OAAAA,EAASiB,gBAAUhE,KAAOyC,EAAAA,IAAAA,CAAAA;AAAO3C,YAAAA;SAAUS,EAAAA,OAAAA,CAAAA;AAC/D,KAAA;AAEA;;MAGA,MAAM8B,mBAAmB4B,OAAK,CAAA;QAAC,CAAC,EAAE3E,oBAAqB,CAAA,MAAM,CAAC;QAAE,CAAC,EAAEC,oBAAqB,CAAA,MAAM;AAAE,KAAA,CAAA;AAEhG;;MAGA,MAAMsC,oBAAoB,CAAC,EAAElC,GAAG,EAAEM,MAAM,EAAEL,IAAI,EAAO,GAAA;QACnD,MAAMsE,QAAAA,GAAWC,SAAM,KAAO,EAAA;AAAC,YAAA,QAAA;AAAU,YAAA,YAAA;AAAcxE,YAAAA,GAAAA;AAAK,YAAA;SAAS,EAAEM,MAAAA,CAAAA;AAEvE,QAAA,IAAIiE,QAAU,EAAA;YACZxE,eAAgB,CAAA;AAAEC,gBAAAA,GAAAA;AAAKC,gBAAAA,IAAAA,EAAMA,KAAK0B;AAAU,aAAA,CAAA;AAC9C;AACF,KAAA;AAEA;;MAGA,MAAMJ,8BAAiC,GAAA,CAAC,EAAEvB,GAAG,EAAE2B,SAAS,EAAErB,MAAM,EAAEL,IAAI,EAAO,GAAA;QAC3E,IAAIK,MAAAA,CAAOmE,GAAG,KAAK,aAAA,IAAiB9C,aAAa,CAAC+C,8BAAAA,CAA0BC,QAAQ,CAAC3E,GAAM,CAAA,EAAA;YACzFD,eAAgB,CAAA;AAAEC,gBAAAA,GAAAA;AAAKC,gBAAAA,IAAAA,EAAMA,KAAK0B;AAAU,aAAA,CAAA;AAC9C;AACF,KAAA;IAEA,MAAMa,cAAAA,GAAiB,CAAC3B,MAAAA,GAAS,EAAE,GAAA;AACjC,QAAA,MAAM+D,uBAAuBzF,uBAAwBmB,CAAAA,MAAAA,CAAAA;AACrD,QAAA,MAAMuE,qBAAqBzF,qBAAsBkB,CAAAA,MAAAA,CAAAA;QAEjD,MAAMwE,4BAAAA,GAA+BC,gBAAaH,oBAAsBC,EAAAA,kBAAAA,CAAAA;AAExE,QAAA,OAAOG,OAAK,CAAA;AAAInE,YAAAA,GAAAA,MAAAA;AAAWhB,YAAAA,GAAAA,gBAAAA;AAAqBiF,YAAAA,GAAAA;AAA6B,SAAA,CAAA;AAC/E,KAAA;IAEA,MAAM9D,cAAAA,GAAiB,CAACH,MAAAA,GAAS,EAAE,GAAA;AACjC,QAAA,OAAOmE,OAAK,CAAA;AACPnE,YAAAA,GAAAA,MAAAA;AACAf,YAAAA,GAAAA,aAAAA;AACAD,YAAAA,GAAAA,gBAAAA;AACHL,YAAAA,oBAAAA;AACAC,YAAAA,oBAAAA;AACAC,YAAAA;AACD,SAAA,CAAA;AACH,KAAA;IAEA,OAAO;AACLuF,QAAAA,aAAAA,EAAetC,YAAahC,CAAAA,mBAAAA,CAAAA;AAC5BuE,QAAAA,aAAAA,EAAevC,YAAaJ,CAAAA,mBAAAA;AAC9B,KAAA;AACF,CAAA;;;;"}