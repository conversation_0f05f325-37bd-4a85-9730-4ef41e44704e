/* Base16 Atelier Sulphurpool Light - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/sulphurpool) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>/base16) */

/* Atelier-Sulphurpool Comment */
.hljs-comment,
.hljs-quote {
  color: #6b7394;
}

/* Atelier-Sulphurpool Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #c94922;
}

/* Atelier-Sulphurpool Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #c76b29;
}

/* Atelier-Sulphurpool Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
  color: #ac9739;
}

/* Atelier-Sulphurpool Blue */
.hljs-title,
.hljs-section {
  color: #3d8fd1;
}

/* Atelier-Sulphurpool Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #6679cc;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #f5f7ff;
  color: #5e6687;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
