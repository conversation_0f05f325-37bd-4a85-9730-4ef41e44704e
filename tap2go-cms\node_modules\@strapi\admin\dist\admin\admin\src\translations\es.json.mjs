var Analytics = "Analytics";
var Documentation = "Documentación";
var Email = "Email";
var Password = "Contraseña";
var Provider = "Proveedor";
var ResetPasswordToken = "Restablecer Token de Contraseña";
var Role = "Rol";
var Username = "Nombre de usuario";
var Users = "Usuarios";
var anErrorOccurred = "¡Ups! Algo salió mal. Inténtalo de nuevo.";
var clearLabel = "Limpiar";
var or = "O";
var skipToContent = "Saltar al contenido";
var submit = "Enviar";
var light = "Claro";
var dark = "Oscuro";
var es = {
    Analytics: Analytics,
    "Auth.components.Oops.text": "Tu cuenta ha sido suspendida",
    "Auth.components.Oops.text.admin": "Si se trata de un error, comuníquese con su administrador.",
    "Auth.components.Oops.title": "Ups...",
    "Auth.form.button.forgot-password": "Enviar Email",
    "Auth.form.button.go-home": "REGRESAR A CASA",
    "Auth.form.button.login": "Iniciar sesión",
    "Auth.form.button.login.providers.error": "No podemos conectarlo a través del proveedor seleccionado.",
    "Auth.form.button.login.strapi": "Iniciar sesión a través de Strapi",
    "Auth.form.button.password-recovery": "Recuperación de Contraseña",
    "Auth.form.button.register": "Listo para comenzar",
    "Auth.form.confirmPassword.label": "Confirmación de contraseña",
    "Auth.form.currentPassword.label": "Contraseña actual",
    "Auth.form.email.label": "Correo electrónico",
    "Auth.form.email.placeholder": "<EMAIL>",
    "Auth.form.error.blocked": "Su cuenta ha sido bloqueada por el administrador.",
    "Auth.form.error.code.provide": "Código incorrecto proporcionado.",
    "Auth.form.error.confirmed": "Su cuenta de correo no ha sido confirmada.",
    "Auth.form.error.email.invalid": "Este email es inválido.",
    "Auth.form.error.email.provide": "Por favor proporcione su nombre de usuario o su correo electrónico.",
    "Auth.form.error.email.taken": "El email ya está registrado",
    "Auth.form.error.invalid": "Identificador o contraseña inválidos.",
    "Auth.form.error.params.provide": "Parametros incorrectos proporcionados.",
    "Auth.form.error.password.format": "Su contraseña no puede contener el símbolo `$` más de tres veces.",
    "Auth.form.error.password.local": "Este usuario nunca estableció una contraseña local, por favor ingrese a través de su proveedor utilizado durante la creación de la cuenta.",
    "Auth.form.error.password.matching": "Las contraseñas no coinciden.",
    "Auth.form.error.password.provide": "Por favor, introduzca su contraseña.",
    "Auth.form.error.ratelimit": "Demasiados intentos. Por favor vuelva a intentarlo dentro de un minuto.",
    "Auth.form.error.user.not-exist": "Este email no existe.",
    "Auth.form.error.username.taken": "El nombre de usuario ya está registrado",
    "Auth.form.firstname.label": "Nombre",
    "Auth.form.firstname.placeholder": "Juan",
    "Auth.form.forgot-password.email.label": "Introduce tu email",
    "Auth.form.forgot-password.email.label.success": "Email enviado con éxito a",
    "Auth.form.lastname.label": "Apellido",
    "Auth.form.lastname.placeholder": "Pérez",
    "Auth.form.password.hide-password": "Ocultar contraseña",
    "Auth.form.password.hint": "La contraseña debe contener al menos 8 caracteres, 1 mayúscula, 1 minúscula y 1 número",
    "Auth.form.password.show-password": "Mostrar contraseña",
    "Auth.form.register.news.label": "Mantenerme informado sobre las nuevas funciones y las próximas mejoras (al hacer esto, acepta las {terms} y la {policy}).",
    "Auth.form.register.subtitle": "Sus credenciales solo se utilizan para autenticarse en el panel de administración. Todos los datos guardados se almacenarán en su propia base de datos.",
    "Auth.form.rememberMe.label": "Recuérdame",
    "Auth.form.username.label": "Usuario",
    "Auth.form.username.placeholder": "Kai Doe",
    "Auth.form.welcome.subtitle": "Inicie sesión en su cuenta de Strapi",
    "Auth.form.welcome.title": "Bienvenido!",
    "Auth.link.forgot-password": "¿Olvidó su contraseña?",
    "Auth.link.ready": "¿Listo para iniciar sesión?",
    "Auth.link.signin": "Registrarse",
    "Auth.link.signin.account": "¿Ya tienes una cuenta?",
    "Auth.login.sso.divider": "O inicia sesión con",
    "Auth.login.sso.loading": "Cargando proveedores...",
    "Auth.login.sso.subtitle": "Inicie sesión en su cuenta a través de SSO",
    "Auth.privacy-policy-agreement.policy": "política de privacidad",
    "Auth.privacy-policy-agreement.terms": "condiciones",
    "Content Manager": "Gestor de Contenidos",
    "Content Type Builder": "Constructor de Tipos de Contenido",
    Documentation: Documentation,
    Email: Email,
    "Files Upload": "Subida de archivos",
    "HomePage.head.title": "Página principal",
    "HomePage.roadmap": "Vea nuestra hoja de ruta",
    "HomePage.welcome.congrats": "¡Felicidades!",
    "HomePage.welcome.congrats.content": "Está registrado como el primer administrador. \nPara descubrir las potentes funciones que ofrece Strapi,",
    "HomePage.welcome.congrats.content.bold": "le recomendamos que cree su primer Tipo de Colección.",
    "Media Library": "Biblioteca de Multimedia",
    "New entry": "Entrada nueva",
    Password: Password,
    Provider: Provider,
    ResetPasswordToken: ResetPasswordToken,
    Role: Role,
    "Roles & Permissions": "Roles y Permisos",
    "Roles.ListPage.notification.delete-all-not-allowed": "Algunos roles no se pudieron eliminar porque están asociados a usuarios",
    "Roles.ListPage.notification.delete-not-allowed": "No se puede eliminar un rol si está asociado a usuarios",
    "Roles.RoleRow.select-all": "Seleccione {name} para acciones en bloque",
    "Roles.components.List.empty.withSearch": "No hay rol correspondiente a la búsqueda ({search})...",
    "Settings.PageTitle": "Configuración - {name}",
    "Settings.apiTokens.addFirstToken": "Agrega tu primer token de API",
    "Settings.apiTokens.addNewToken": "Agregar nuevo token de API",
    "Settings.tokens.copy.editMessage": "Por razones de seguridad, solo puede ver su token una vez.",
    "Settings.tokens.copy.editTitle": "Este token ya no es accesible.",
    "Settings.tokens.copy.lastWarning": "¡Asegúrate de copiar este token, no podrás volver a verlo!",
    "Settings.apiTokens.create": "Añadir entrada",
    "Settings.apiTokens.description": "Lista de tokens generados para consumir la API",
    "Settings.apiTokens.emptyStateLayout": "Aún no tienes ningún contenido ...",
    "Settings.tokens.notification.copied": "Token copiado al portapapeles.",
    "Settings.apiTokens.title": "Tokens de API",
    "Settings.apiTokens.lastHour": "última hora",
    "Settings.tokens.ListView.headers.createdAt": "Creado en",
    "Settings.tokens.ListView.headers.description": "Descripción",
    "Settings.tokens.ListView.headers.lastUsedAt": "Último uso",
    "Settings.tokens.ListView.headers.name": "Nombre",
    "Settings.tokens.types.full-access": "Acceso completo",
    "Settings.tokens.types.read-only": "Solo lectura",
    "Settings.application.description": "Información global del panel de administración",
    "Settings.application.edition-title": "Edición actual",
    "Settings.application.get-help": "Consigue ayuda",
    "Settings.application.link-pricing": "Ver todos los planes",
    "Settings.application.link-upgrade": "Actualiza tu panel de administración",
    "Settings.application.node-version": "versión de node",
    "Settings.application.strapi-version": "versión de strapi",
    "Settings.application.strapiVersion": "versión de strapi",
    "Settings.application.title": "Descripción general",
    "Settings.error": "Error",
    "Settings.global": "Configuración global",
    "Settings.permissions": "Panel de administración",
    "Settings.permissions.category": "Configuración de permisos para {category}",
    "Settings.permissions.category.plugins": "Configuración de permisos para el plugin de {category}",
    "Settings.permissions.conditions.anytime": "En cualquier momento",
    "Settings.permissions.conditions.apply": "Aplicar",
    "Settings.permissions.conditions.can": "Poder",
    "Settings.permissions.conditions.conditions": "Definir condiciones",
    "Settings.permissions.conditions.links": "Enlaces",
    "Settings.permissions.conditions.no-actions": "No hay acción",
    "Settings.permissions.conditions.none-selected": "En cualquier momento",
    "Settings.permissions.conditions.or": "O",
    "Settings.permissions.conditions.when": "Cuando",
    "Settings.permissions.select-all-by-permission": "Seleccionar todos los permisos de {label}",
    "Settings.permissions.select-by-permission": "Seleccionar permiso de {label}",
    "Settings.permissions.users.create": "Crear nuevo usuario",
    "Settings.permissions.users.email": "Correo electrónico",
    "Settings.permissions.users.firstname": "Nombre",
    "Settings.permissions.users.lastname": "Apellido",
    "Settings.permissions.users.form.sso": "connect with sso",
    "Settings.permissions.users.form.sso.description": "when enabled (on), users can login via sso",
    "Settings.permissions.users.listview.header.subtitle": "Todos los usuarios que tienen acceso al panel de administración de strapi",
    "Settings.permissions.users.tabs.label": "Permisos de pestañas",
    "Settings.profile.form.notify.data.loaded": "Se han cargado los datos de tu perfil",
    "Settings.profile.form.section.experience.clear.select": "Borrar el idioma de interfaz seleccionado",
    "Settings.profile.form.section.experience.here": "documentación",
    "Settings.profile.form.section.experience.interfaceLanguage": "Idioma de interfaz",
    "Settings.profile.form.section.experience.interfaceLanguage.hint": "Esto solo mostrará su propia interfaz en el idioma elegido.",
    "Settings.profile.form.section.experience.interfaceLanguageHelp": "La selección cambiará el idioma de la interfaz solo para usted. Consulte esta {here} para que otros idiomas estén disponibles para su equipo.",
    "Settings.profile.form.section.experience.title": "Experiencia",
    "Settings.profile.form.section.head.title": "Perfil de usuario",
    "Settings.profile.form.section.profile.page.title": "Página de perfil",
    "Settings.roles.create.description": "Definir los derechos otorgados al rol",
    "Settings.roles.create.title": "Crea un rol",
    "Settings.roles.created": "Rol creado",
    "Settings.roles.edit.title": "Editar un rol",
    "Settings.roles.form.button.users-with-role": "Usuarios con este rol",
    "Settings.roles.form.created": "Creado",
    "Settings.roles.form.description": "Nombre y descripción del rol",
    "Settings.roles.form.permission.property-label": "permisos de {label}",
    "Settings.roles.form.permissions.attributesPermissions": "Permisos de los campos",
    "Settings.roles.form.permissions.create": "Crear",
    "Settings.roles.form.permissions.delete": "Eliminar",
    "Settings.roles.form.permissions.publish": "Publicar",
    "Settings.roles.form.permissions.read": "Leer",
    "Settings.roles.form.permissions.update": "Actualizar",
    "Settings.roles.list.button.add": "Agregar nuevo rol",
    "Settings.roles.list.description": "Lista de roles",
    "Settings.roles.title.singular": "rol",
    "Settings.sso.description": "Configure los ajustes para la función de inicio de sesión único (SSO).",
    "Settings.sso.form.defaultRole.description": "Asociará al nuevo usuario autenticado al rol seleccionado",
    "Settings.sso.form.defaultRole.description-not-allowed": "Debes tener permiso para leer los roles de administrador.",
    "Settings.sso.form.defaultRole.label": "Rol predeterminado",
    "Settings.sso.form.registration.description": "Crear un nuevo usuario en el inicio de sesión (SSO) si no existe una cuenta",
    "Settings.sso.form.registration.label": "Auto-registro",
    "Settings.sso.title": "Inicio de sesión único (SSO)",
    "Settings.webhooks.create": "Crea un webhook",
    "Settings.webhooks.create.header": "Crea un nuevo encabezado",
    "Settings.webhooks.created": "Webhook creado",
    "Settings.webhooks.event.publish-tooltip": "Este evento solo existe para contenidos con el sistema Borrador/Publicación habilitado",
    "Settings.webhooks.events.create": "Crear",
    "Settings.webhooks.events.update": "Actualizar",
    "Settings.webhooks.form.events": "Eventos",
    "Settings.webhooks.form.headers": "Encabezados",
    "Settings.webhooks.form.url": "Url",
    "Settings.webhooks.headers.remove": "eliminar fila de encabezado {number}",
    "Settings.webhooks.key": "Clave",
    "Settings.webhooks.list.button.add": "Agregar nuevo webhook",
    "Settings.webhooks.list.description": "Recibe notificaciones de cambios POST.",
    "Settings.webhooks.list.empty.description": "Agregue el primero a esta lista.",
    "Settings.webhooks.list.empty.link": "Ver nuestra documentación",
    "Settings.webhooks.list.empty.title": "Todavía no hay webhooks",
    "Settings.webhooks.list.th.actions": "acciones",
    "Settings.webhooks.list.th.status": "estado",
    "Settings.webhooks.singular": "webhook",
    "Settings.webhooks.title": "Webhooks",
    "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# recurso seleccionado} other {# recursos seleccionados}}",
    "Settings.webhooks.trigger": "Desencadenante",
    "Settings.webhooks.trigger.cancel": "Cancelar disparador",
    "Settings.webhooks.trigger.pending": "Pendiente…",
    "Settings.webhooks.trigger.save": "Guarde para desencadenar",
    "Settings.webhooks.trigger.success": "¡Éxito!",
    "Settings.webhooks.trigger.success.label": "Desencadenante éxitoso",
    "Settings.webhooks.trigger.test": "Probar desencadenante",
    "Settings.webhooks.trigger.title": "Guardar antes de desencadenar",
    "Settings.webhooks.value": "Valor",
    Username: Username,
    Users: Users,
    "Users & Permissions": "Usuarios y permisos",
    "Users.components.List.empty": "No hay usuarios...",
    "Users.components.List.empty.withFilters": "No hay usuarios con los filtros aplicados...",
    "Users.components.List.empty.withSearch": "No hay usuarios correspondientes a la búsqueda ({search})...",
    "admin.pages.MarketPlacePage.head": "Marketplace - Plugins",
    "admin.pages.MarketPlacePage.submit.plugin.link": "Envíe su plugin",
    "admin.pages.MarketPlacePage.subtitle": "Saca más partido a Strapi",
    anErrorOccurred: anErrorOccurred,
    "app.component.CopyToClipboard.label": "Copiar al portapapeles",
    "app.component.search.label": "Buscar {target}",
    "app.component.table.duplicate": "Copiar {target}",
    "app.component.table.edit": "Editar {target}",
    "app.component.table.select.one-entry": "Seleccionar {target}",
    "app.components.BlockLink.blog": "Blog",
    "app.components.BlockLink.blog.content": "Lea las últimas noticias sobre Strapi y el ecosistema.",
    "app.components.BlockLink.code": "Ejemplos de código",
    "app.components.BlockLink.code.content": "Aprenda probando proyectos reales desarrollados por la comunidad.",
    "app.components.BlockLink.documentation.content": "Descubra los conceptos esenciales, guías e instrucciones.",
    "app.components.BlockLink.tutorial": "Tutoriales",
    "app.components.BlockLink.tutorial.content": "Siga las instrucciones paso a paso para usar y personalizar Strapi.",
    "app.components.Button.cancel": "Cancelar",
    "app.components.Button.confirm": "Confirmar",
    "app.components.Button.reset": "Reiniciar",
    "app.components.ComingSoonPage.comingSoon": "Próximamente",
    "app.components.ConfirmDialog.title": "Confirmación",
    "app.components.DownloadInfo.download": "Descarga en curso...",
    "app.components.DownloadInfo.text": "Esto puede tardar un minuto. Gracias por su paciencia.",
    "app.components.EmptyAttributes.title": "Aún no hay campos",
    "app.components.EmptyStateLayout.content-document": "No se encontró contenido",
    "app.components.EmptyStateLayout.content-permissions": "No tienes los permisos para acceder a ese contenido.",
    "app.components.HomePage.button.blog": "VER MÁS EN EL BLOG",
    "app.components.HomePage.community": "Encuentre la comunidad en la web",
    "app.components.HomePage.community.content": "Hable con los miembros del equipo, colaboradores y desarrolladores en diferentes canales.",
    "app.components.HomePage.create": "Crea tu primer Tipo de Contenido",
    "app.components.HomePage.roadmap": "Vea nuestros próximos objetivos",
    "app.components.HomePage.welcome": "¡Bienvenido a bordo!",
    "app.components.HomePage.welcome.again": "¡Bienvenido ",
    "app.components.HomePage.welcomeBlock.content": "Estamos felices de tenerlo como miembro de la comunidad. Estamos constantemente en busca de comentarios así que no dude en enviarnos un DM en ",
    "app.components.HomePage.welcomeBlock.content.again": "Esperamos que estés progresando en tu proyecto.... Siéntase libre de leer las últimas novedades sobre Strapi. Estamos dando lo mejor de nosotros mismos para mejorar el producto basándonos en sus comentarios.",
    "app.components.HomePage.welcomeBlock.content.issues": "problema.",
    "app.components.HomePage.welcomeBlock.content.raise": " o reportar cualquier ",
    "app.components.ImgPreview.hint": "Arrastre y suelte el archivo en esta área o {browse} para subir un archivo.",
    "app.components.ImgPreview.hint.browse": "buscar",
    "app.components.InputFile.newFile": "Añadir nuevo archivo",
    "app.components.InputFileDetails.open": "Abrir en una nueva pestaña",
    "app.components.InputFileDetails.originalName": "Nombre original:",
    "app.components.InputFileDetails.remove": "Eliminar este archivo",
    "app.components.InputFileDetails.size": "Tamaño:",
    "app.components.InstallPluginPage.Download.description": "La descarga e instalación del plugin podría llevar unos segundos.",
    "app.components.InstallPluginPage.Download.title": "Descargando...",
    "app.components.InstallPluginPage.description": "Extienda su aplicación sin esfuerzo.",
    "app.components.LeftMenu.collapse": "Contraer la barra de navegación",
    "app.components.LeftMenu.expand": "Expandir la barra de navegación",
    "app.components.LeftMenu.logout": "Cerrar sesión",
    "app.components.LeftMenu.trialCountdown": "Tu prueba termina el {date}.",
    "app.components.LeftMenuFooter.help": "Ayuda",
    "app.components.LeftMenuFooter.poweredBy": "Potenciado por ",
    "app.components.LeftMenuLinkContainer.collectionTypes": "Tipos de Colección",
    "app.components.LeftMenuLinkContainer.configuration": "Configuraciones",
    "app.components.LeftMenuLinkContainer.general": "General",
    "app.components.LeftMenuLinkContainer.noPluginsInstalled": "No hay plugins instalados todavía",
    "app.components.LeftMenuLinkContainer.plugins": "Plugins",
    "app.components.LeftMenuLinkContainer.singleTypes": "Tipos Únicos",
    "app.components.ListPluginsPage.deletePlugin.description": "Es posible que la desinstalación del plugin tarde unos segundos.",
    "app.components.ListPluginsPage.deletePlugin.title": "Desinstalar",
    "app.components.ListPluginsPage.description": "Lista de los plugins instalados en el proyecto.",
    "app.components.ListPluginsPage.head.title": "Lista de plugins",
    "app.components.Logout.logout": "Cerrar sesión",
    "app.components.Logout.profile": "Perfil",
    "app.components.MarketplaceBanner": "Descubra los plugins creados por la comunidad y muchas más cosas increíbles para impulsar su proyecto, en Strapi Awesome.",
    "app.components.MarketplaceBanner.image.alt": "un logo de cohete strapi",
    "app.components.MarketplaceBanner.link": "Échale un vistazo ahora",
    "app.components.NotFoundPage.back": "Volver a la página de inicio",
    "app.components.NotFoundPage.description": "No encontrado",
    "app.components.Official": "Oficial",
    "app.components.Onboarding.help.button": "Boton de ayuda",
    "app.components.Onboarding.label.completed": "% completado",
    "app.components.Onboarding.title": "Vídeos introductorios",
    "app.components.PluginCard.Button.label.download": "Descargar",
    "app.components.PluginCard.Button.label.install": "Ya instalado",
    "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "La función de recarga automática debe estar desactivada. Por favor, inicie su aplicación con `yarn develop`.",
    "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "¡Entendido!",
    "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Por motivos de seguridad, el plugin sólo puede descargarse en entorno de desarrollo.",
    "app.components.PluginCard.PopUpWarning.install.impossible.title": "Imposible descargar",
    "app.components.PluginCard.compatible": "Compatible con su aplicación",
    "app.components.PluginCard.compatibleCommunity": "Compatible con la comunidad",
    "app.components.PluginCard.more-details": "Más detalles",
    "app.components.ToggleCheckbox.off-label": "Apagado",
    "app.components.ToggleCheckbox.on-label": "Encendido",
    "app.components.Users.MagicLink.connect": "Envíe este enlace al usuario para que se conecte.",
    "app.components.Users.MagicLink.connect.sso": "Envíe este enlace al usuario, el primer inicio de sesión se puede realizar a través de un proveedor de SSO",
    "app.components.Users.ModalCreateBody.block-title.details": "Detalles",
    "app.components.Users.ModalCreateBody.block-title.roles": "Roles del usuario",
    "app.components.Users.ModalCreateBody.block-title.roles.description": "Un usuario puede tener uno o varios roles.",
    "app.components.Users.SortPicker.button-label": "Ordenar por",
    "app.components.Users.SortPicker.sortby.email_asc": "Correo electrónico (de la A a la Z)",
    "app.components.Users.SortPicker.sortby.email_desc": "Correo electrónico (de la Z a la A)",
    "app.components.Users.SortPicker.sortby.firstname_asc": "Nombre (de la A a la Z)",
    "app.components.Users.SortPicker.sortby.firstname_desc": "Nombre (de la Z a la A)",
    "app.components.Users.SortPicker.sortby.lastname_asc": "Apellido (de la A a la Z)",
    "app.components.Users.SortPicker.sortby.lastname_desc": "Apellido (de la Z a la A)",
    "app.components.Users.SortPicker.sortby.username_asc": "Nombre de usuario (de la A a la Z)",
    "app.components.Users.SortPicker.sortby.username_desc": "Nombre de usuario (de la Z a la A)",
    "app.components.listPlugins.button": "Añadir nuevo plugin",
    "app.components.listPlugins.title.none": "No hay plugins instalados",
    "app.components.listPluginsPage.deletePlugin.error": "Se ha producido un error al desinstalar el plugin",
    "app.containers.App.notification.error.init": "Se produjo un error al solicitar la API",
    "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Si no recibe este enlace, comuníquese con su administrador.",
    "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Es posible que tarde unos minutos en recibir el enlace de recuperación de contraseña.",
    "app.containers.AuthPage.ForgotPasswordSuccess.title": "Correo electrónico enviado",
    "app.containers.Users.EditPage.form.active.label": "Activo",
    "app.containers.Users.EditPage.header.label": "Editar {name}",
    "app.containers.Users.EditPage.header.label-loading": "Editar usuario",
    "app.containers.Users.EditPage.roles-bloc-title": "Roles atribuidos",
    "app.containers.Users.ModalForm.footer.button-success": "Crear usuario",
    "app.links.configure-view": "Configurar la vista",
    "app.static.links.cheatsheet": "CheatSheet",
    "app.utils.SelectOption.defaultMessage": " ",
    "app.utils.add-filter": "Añadir filtro",
    "app.utils.close-label": "Cerrar",
    "app.utils.defaultMessage": " ",
    "app.utils.duplicate": "Duplicar",
    "app.utils.edit": "Editar",
    "app.utils.errors.file-too-big.message": "El archivo es demasiado grande",
    "app.utils.filter-value": "Filtro",
    "app.utils.filters": "Filtros",
    "app.utils.notify.data-loaded": "{target} se ha cargado",
    "app.utils.placeholder.defaultMessage": " ",
    "app.utils.publish": "Publicar",
    "app.utils.select-all": "Seleccionar todo",
    "app.utils.select-field": "Seleccionar campo",
    "app.utils.select-filter": "Seleccionar filtro",
    "app.utils.unpublish": "Anular publicación",
    clearLabel: clearLabel,
    "coming.soon": "¡Este contenido está actualmente en construcción y estará de regreso en unas semanas!",
    "component.Input.error.validation.integer": "El valor debe ser un número entero",
    "components.AutoReloadBlocker.description": "Inicia Strapi con uno de los siguientes comandos:",
    "components.AutoReloadBlocker.header": "Es necesario recargar para este plugin.",
    "components.ErrorBoundary.title": "Algo salió mal...",
    "components.FilterOptions.FILTER_TYPES.$contains": "contiene",
    "components.FilterOptions.FILTER_TYPES.$containsi": "contiene (insensible a mayúsculas y minúsculas)",
    "components.FilterOptions.FILTER_TYPES.$endsWith": "termina con",
    "components.FilterOptions.FILTER_TYPES.$endsWithi": "termina con (insensible a mayúsculas y minúsculas)",
    "components.FilterOptions.FILTER_TYPES.$eq": "es",
    "components.FilterOptions.FILTER_TYPES.$eqi": "es (insensible a mayúsculas y minúsculas)",
    "components.FilterOptions.FILTER_TYPES.$gt": "es mayor que",
    "components.FilterOptions.FILTER_TYPES.$gte": "es mayor o igual a",
    "components.FilterOptions.FILTER_TYPES.$lt": "es menor que",
    "components.FilterOptions.FILTER_TYPES.$lte": "es menor o igual a",
    "components.FilterOptions.FILTER_TYPES.$ne": "no es",
    "components.FilterOptions.FILTER_TYPES.$nei": "no es (insensible a mayúsculas y minúsculas)",
    "components.FilterOptions.FILTER_TYPES.$notContains": "no contiene",
    "components.FilterOptions.FILTER_TYPES.$notContainsi": "no contiene (insensible a mayúsculas y minúsculas)",
    "components.FilterOptions.FILTER_TYPES.$notNull": "is not null",
    "components.FilterOptions.FILTER_TYPES.$null": "is null",
    "components.FilterOptions.FILTER_TYPES.$startsWith": "comienza con",
    "components.FilterOptions.FILTER_TYPES.$startsWithi": "comienza con (insensible a mayúsculas y minúsculas)",
    "components.Input.error.attribute.key.taken": "Este valor ya existe",
    "components.Input.error.attribute.sameKeyAndName": "No puede ser igual",
    "components.Input.error.attribute.taken": "Este nombre de campo ya existe",
    "components.Input.error.contain.lowercase": "La contraseña debe contener al menos un carácter en minúscula",
    "components.Input.error.contain.number": "La contraseña debe contener al menos un número",
    "components.Input.error.contain.uppercase": "La contraseña debe contener al menos un carácter en mayúscula",
    "components.Input.error.contentTypeName.taken": "Este nombre ya existe",
    "components.Input.error.custom-error": "{errorMessage} ",
    "components.Input.error.password.noMatch": "Las contraseñas no coinciden",
    "components.Input.error.validation.email": "Esto no es un email",
    "components.Input.error.validation.json": "Esto no coincide con el formato JSON",
    "components.Input.error.validation.max": "El valor es demasiado alto {max}.",
    "components.Input.error.validation.maxLength": "El valor es demasiado largo {max}.",
    "components.Input.error.validation.min": "El valor es demasiado bajo {min}.",
    "components.Input.error.validation.minLength": "El valor es demasiado corto {min}.",
    "components.Input.error.validation.minSupMax": "No puede ser superior",
    "components.Input.error.validation.regex": "El valor no coincide con el de regex.",
    "components.Input.error.validation.required": "Este valor es obligatorio.",
    "components.Input.error.validation.unique": "Este valor ya se utiliza.",
    "components.InputSelect.option.placeholder": "Elige aquí",
    "components.ListRow.empty": "No hay datos que mostrar.",
    "components.NotAllowedInput.text": "Sin permisos para ver este campo.",
    "components.OverlayBlocker.description": "Está utilizando una función que necesita que el servidor se reinicie. Por favor, espere hasta que el servidor esté listo..",
    "components.OverlayBlocker.description.serverError": "El servidor debería haberse reiniciado, compruebe sus logs en el terminal.",
    "components.OverlayBlocker.title": "Esperando el reinicio...",
    "components.OverlayBlocker.title.serverError": "El reinicio está llevando más tiempo de lo esperado",
    "components.PageFooter.select": "entradas por página",
    "components.ProductionBlocker.description": "Por razones de seguridad tenemos que desactivar este plugin en otros entornos.",
    "components.ProductionBlocker.header": "Este plugin sólo está disponible en entornos de desarrollo.",
    "components.Search.placeholder": "Buscar...",
    "components.TableHeader.sort": "Ordenar por {label}",
    "components.Wysiwyg.ToggleMode.markdown-mode": "Modo de Markdown",
    "components.Wysiwyg.ToggleMode.preview-mode": "Modo de vista previa",
    "components.Wysiwyg.collapse": "Contraer menú",
    "components.Wysiwyg.selectOptions.H1": "Título H1",
    "components.Wysiwyg.selectOptions.H2": "Título H2",
    "components.Wysiwyg.selectOptions.H3": "Título H3",
    "components.Wysiwyg.selectOptions.H4": "Título H4",
    "components.Wysiwyg.selectOptions.H5": "Título H5",
    "components.Wysiwyg.selectOptions.H6": "Título H6",
    "components.Wysiwyg.selectOptions.title": "Añadir un título",
    "components.WysiwygBottomControls.charactersIndicators": "caracteres",
    "components.WysiwygBottomControls.fullscreen": "Expandir",
    "components.WysiwygBottomControls.uploadFiles": "Arrastrar y soltar archivos, pegar desde el portapapeles o {browse}.",
    "components.WysiwygBottomControls.uploadFiles.browse": "seleccionarlos",
    "components.pagination.go-to": "Ir a la página {page}",
    "components.pagination.go-to-next": "Ir a la página siguiente",
    "components.pagination.go-to-previous": "Regresar a la página anterior",
    "components.pagination.remaining-links": "Y {number} enlaces más",
    "components.popUpWarning.button.cancel": "No, cancelar",
    "components.popUpWarning.button.confirm": "Sí, confirmar",
    "components.popUpWarning.message": "¿Estás seguro de que quieres borrar esto?",
    "components.popUpWarning.title": "Por favor, confirme",
    "form.button.done": "Hecho",
    "global.prompt.unsaved": "¿Está seguro de que quiere salir de esta página? Todas sus modificaciones se perderán",
    "notification.contentType.relations.conflict": "El Tipo de Contenido tiene relaciones conflictivas",
    "notification.default.title": "Información:",
    "notification.error": "Se ha producido un error",
    "notification.error.layout": "No se pudo recuperar el esquema",
    "notification.form.error.fields": "El formulario contiene algunos errores",
    "notification.form.success.fields": "Cambios guardados",
    "notification.link-copied": "Enlace copiado en el portapapeles",
    "notification.permission.not-allowed-read": "No tienes permiso para ver este documento",
    "notification.success.delete": "El elemento ha sido eliminado",
    "notification.success.saved": "Guardado",
    "notification.success.title": "Éxito:",
    "notification.version.update.message": "¡Hay una nueva versión de Strapi disponible!",
    "notification.warning.title": "Advertencia:",
    or: or,
    "request.error.model.unknown": "Este modelo no existe",
    skipToContent: skipToContent,
    submit: submit,
    "Auth.form.active.label": "Activo",
    "Auth.reset-password.title": "Resetear contraseña",
    "Roles.RoleRow.user-count": "{number, plural, =0 {#  usuario} one {#  usuario} other {# usuarios}}",
    "Settings.application.customization": "Personalización",
    "Settings.application.customization.carousel.title": "Logo",
    "Settings.application.customization.carousel.change-action": "Cambiarlogo",
    "Settings.application.customization.carousel.reset-action": "Resetear logo",
    "Settings.application.customization.carousel-slide.label": "Logo slider",
    "Settings.application.customization.carousel-hint": "Cambiar el logo del panel de administración (Dimensión máxima: {dimension}x{dimension}, Tamaño máximo del archivo: {size}KB)",
    "Settings.application.customization.modal.cancel": "Cancelar",
    "Settings.application.customization.modal.upload": "Subir logo",
    "Settings.application.customization.modal.tab.label": "¿Cómo te gustaría subir tus archivos?",
    "Settings.application.customization.modal.upload.from-computer": "Desde el ordenador",
    "Settings.application.customization.modal.upload.file-validation": "Dimensión máxima: {dimension}x{dimension}, Tamaño máximo: {size}KB",
    "Settings.application.customization.modal.upload.error-format": "Se cargó un formato incorrecto (formatos aceptados: jpeg, jpg, png, svg).",
    "Settings.application.customization.modal.upload.error-size": "El archivo es muy grande (Dimensión máxima: {dimension}x{dimension},Tamaño máximo del archivo: {size}KB)",
    "Settings.application.customization.modal.upload.error-network": "Error de red",
    "Settings.application.customization.modal.upload.cta.browse": "Buscar archivos",
    "Settings.application.customization.modal.upload.drag-drop": "Arrastrar aquí o",
    "Settings.application.customization.modal.upload.from-url": "Desde una url",
    "Settings.application.customization.modal.upload.from-url.input-label": "URL",
    "Settings.application.customization.modal.upload.next": "Siguiente",
    "Settings.application.customization.modal.pending": "Logo pendiente",
    "Settings.application.customization.modal.pending.choose-another": "Elegir otro logo",
    "Settings.application.customization.modal.pending.title": "Logo ya subido",
    "Settings.application.customization.modal.pending.subtitle": "Gestiona el logo elegido antes de subirlo",
    "Settings.application.customization.modal.pending.upload": "Subir logo",
    "Settings.application.customization.modal.pending.card-badge": "imagen",
    "Settings.profile.form.section.experience.mode.label": "Modo de la interfaz",
    "Settings.profile.form.section.experience.mode.hint": "Mostrar la interfaz en el modo seleccionado.",
    "Settings.profile.form.section.experience.mode.option-label": "Mode {name} ",
    light: light,
    dark: dark,
    "Usecase.back-end": "Desarrollador Back-end",
    "Usecase.button.skip": "Saltar esta pregunta",
    "Usecase.content-creator": "Creador de Contenido",
    "Usecase.front-end": "Desarrollador Front-end",
    "Usecase.full-stack": "Desarrollador Full-stack",
    "Usecase.input.work-type": "¿Qué tipo de trabajo realizas?",
    "Usecase.notification.success.project-created": "El proyecto ha sido creado con éxito",
    "Usecase.other": "Otro",
    "Usecase.title": "Dinos algo de ti",
    "admin.pages.MarketPlacePage.offline.title": "Estás offline",
    "admin.pages.MarketPlacePage.offline.subtitle": "Necesita estar conectado a Internet para acceder a Strapi Market.",
    "admin.pages.MarketPlacePage.plugins": "Plugins",
    "admin.pages.MarketPlacePage.plugin.copy": "Copiar comando de instalación",
    "admin.pages.MarketPlacePage.plugin.copy.success": "Comando de instalación listo para ser pegado en su terminal",
    "admin.pages.MarketPlacePage.plugin.info": "Aprender más",
    "admin.pages.MarketPlacePage.plugin.info.label": "Aprender más about {pluginName}",
    "admin.pages.MarketPlacePage.plugin.info.text": "Aprender más",
    "admin.pages.MarketPlacePage.plugin.installed": "Instalado",
    "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Hecho por Strapi",
    "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Plugin verificado por Strapi",
    "admin.pages.MarketPlacePage.providers": "Proovedores",
    "admin.pages.MarketPlacePage.search.clear": "Limpiar la búsqueda",
    "admin.pages.MarketPlacePage.search.empty": "No hay resultados para \"{target}\"",
    "admin.pages.MarketPlacePage.search.placeholder": "Búscar",
    "admin.pages.MarketPlacePage.submit.provider.link": "Enviar proveedor",
    "admin.pages.MarketPlacePage.tab-group.label": "Plugins y proveedores para Strapi",
    "admin.pages.MarketPlacePage.missingPlugin.title": "¿Necesitas un plugin?",
    "admin.pages.MarketPlacePage.missingPlugin.description": "¡Díganos qué plugin está buscando y le informaremos a los desarrolladores de plugin de nuestra comunidad en caso de que estén buscando inspiración!",
    "app.components.GuidedTour.CM.create.content": "<p>Cree y administre todo el contenido aquí en el Administrador de contenido.</p><p>Ej: tomando el ejemplo del sitio web del blog más allá, uno puede escribir un artículo, guardarlo y publicarlo como desee.</p>< p>💡 Consejo rápido: no olvides presionar publicar en el contenido que crees.</p>",
    "app.components.GuidedTour.CM.create.title": "⚡️ Crear contenido",
    "app.components.GuidedTour.CM.success.content": "<p>¡Asombroso, un paso más!</p><b>🚀  Mirar el contenido en acción</b>",
    "app.components.GuidedTour.CM.success.cta.title": "Testear el API",
    "app.components.GuidedTour.CM.success.title": "Paso 2: Completado ✅",
    "app.components.GuidedTour.CTB.create.content": "<p>Los tipos de colección lo ayudan a administrar varias entradas, los tipos únicos son adecuados para administrar solo una entrada.</p> <p>Ej: para un sitio web de blog, Los artículos serían del tipo Colección, mientras que la Página de inicio sería del tipo Único.</p>",
    "app.components.GuidedTour.CTB.create.cta.title": "Crear un tipo de colección",
    "app.components.GuidedTour.CTB.create.title": "🧠 Crear un primer tipo de colección",
    "app.components.GuidedTour.CTB.success.content": "<p>¡Bien hecho!</p><b>⚡️ ¿Qué te gustaría compartir con el mundo?</b>",
    "app.components.GuidedTour.CTB.success.title": "Paso 1: Completado ✅",
    "app.components.GuidedTour.apiTokens.create.content": "<p>Genera un token de autenticación aquí y recupera el contenido que acabas de crear.</p>",
    "app.components.GuidedTour.apiTokens.create.cta.title": "Generar un token de API",
    "app.components.GuidedTour.apiTokens.create.title": "🚀 Ver contenido en acción",
    "app.components.GuidedTour.apiTokens.success.content": "<p>Vea el contenido en acción haciendo una solicitud HTTP:</p><ul><li><p>A esta URL: <light>https: //'<'TU_DOMINIO'>'/api/'<'TU_CT'>'</light></p></li><li><p>Con el encabezado: <light>Autorización: portador '<' TU_API_TOKEN'>'</light></p></li></ul><p>Para obtener más formas de interactuar con el contenido, consulte la <documentationLink>documentación</documentationLink>.</p>",
    "app.components.GuidedTour.apiTokens.success.cta.title": "Volver a la página de inicio",
    "app.components.GuidedTour.apiTokens.success.title": "Paso 3: Completado ✅",
    "app.components.GuidedTour.create-content": "Crear contenido",
    "app.components.GuidedTour.home.CM.title": "⚡️ ¿Qué te gustaría compartir con el mundo?",
    "app.components.GuidedTour.home.CTB.cta.title": "Ir al Creador de tipo de contenido",
    "app.components.GuidedTour.home.CTB.title": "🧠 Construye la estructura del contenido",
    "app.components.GuidedTour.home.apiTokens.cta.title": "Probar la API",
    "app.components.GuidedTour.skip": "Omitir el recorrido",
    "app.components.GuidedTour.title": "3 pasos para comenzar",
    "app.components.LeftMenu.logo.alt": "Logotipo de la aplicación",
    "app.components.LeftMenu.plugins": "Complementos",
    "app.components.LeftMenu.navbrand.title": "Panel de control de Strapi",
    "app.components.LeftMenu.navbrand.workplace": "Lugar de trabajo",
    "app.page.not.found": "¡Vaya! Parece que no podemos encontrar la página que estás buscando...",
    "components.Input.error.validation.lowercase": "El valor debe ser una cadena en minúsculas",
    "content-manager.ListViewTable.relation-loading": "Las relaciones se están cargando",
    "content-manager.ListViewTable.relation-more": "Esta relación contiene más entidades de las que se muestran",
    "content-manager.apiError.Este atributo debe ser único": "{field} debe ser único",
    "form.button.continue": "Continue",
    "global.search": "Buscar",
    "global.actions": "Acciones",
    "global.active": "Activo",
    "global.inactive": "Inactivo",
    "global.back": "Volver",
    "global.cancel": "Cancelar",
    "global.change-password": "Cambiar contraseña",
    "global.content-manager": "Administrador de Contenido",
    "global.continue": "Continuar",
    "global.delete": "Borrar",
    "global.delete-target": "Borrar {target}",
    "global.description": "Descripción",
    "global.details": "Detalles",
    "global.disabled": "Desactivado",
    "global.documentation": "Documentación",
    "global.enabled": "Habilitado",
    "global.finish": "Terminar",
    "global.marketplace": "Marketplace",
    "global.name": "Nombre",
    "global.none": "Ninguno",
    "global.password": "Contraseña",
    "global.plugins": "Plugins",
    "global.plugins.content-manager": "Administrador de Contenido",
    "global.plugins.content-manager.description": "Forma rápida de ver, editar y eliminar los datos en tu base de datos.",
    "global.plugins.content-type-builder": "Generador de tipo de contenido",
    "global.plugins.content-type-builder.description": "Modeliza la estructura de datos de tu API. Crea nuevos campos y relaciones en solo un minuto. Los archivos se crean y actualizan automáticamente en tu proyecto.",
    "global.plugins.email": "Correo electrónico",
    "global.plugins.email.description": "Configura tu aplicación para enviar correos electrónicos.",
    "global.plugins.upload": "Biblioteca Multimedia",
    "global.plugins.upload.description": "Gestión de archivos multimedia.",
    "global.plugins.graphql": "GraphQL",
    "global.plugins.graphql.description": "Agrega un punto final de GraphQL con métodos API predeterminados.",
    "global.plugins.documentation": "Documentación",
    "global.plugins.documentation.description": "Cree un documento OpenAPI y visualice su API con SWAGGER UI.",
    "global.plugins.i18n": "Internacionalización",
    "global.plugins.i18n.description": "Este complemento permite crear, leer y actualizar contenido en diferentes idiomas, tanto desde el Panel de administración como desde la API.",
    "global.plugins.sentry": "Sentry",
    "global.plugins.sentry.description": "Enviar errores de Strapi a Sentry.",
    "global.plugins.users-permissions": "Roles & Permisos",
    "global.plugins.users-permissions.description": "Proteja su API con un proceso de autenticación completo basado en JWT. Este complemento también viene con una estrategia ACL que le permite administrar los permisos entre los grupos de usuarios.",
    "global.profile": "Perfil",
    "global.reset-password": "Resetear contraseña",
    "global.roles": "Roles",
    "global.save": "Guardar",
    "global.see-more": "Ver más",
    "global.select": "Seleccionar",
    "global.select-all-entries": "Selecionar todas las entradas",
    "global.settings": "Configuraciones",
    "global.strapi-super-admin": "Super Admin",
    "global.strapi-editor": "Editor",
    "global.strapi-author": "Autor",
    "global.table.header.email": "Email",
    "global.table.header.firstname": "Nombre",
    "global.table.header.isActive": "Estado del usuario",
    "global.table.header.lastname": "Apellido",
    "global.table.header.roles": "Roles",
    "global.table.header.username": "Nombre de usuario",
    "global.type": "Tipo",
    "global.users": "Usuarios",
    "notification.warning.404": "404 - No Encontrado",
    "components.Blocks.blocks.heading1": "Título 1",
    "components.Blocks.blocks.heading2": "Título 2",
    "components.Blocks.blocks.heading3": "Título 3",
    "components.Blocks.blocks.heading4": "Título 4",
    "components.Blocks.blocks.heading5": "Título 5",
    "components.Blocks.blocks.heading6": "Título 6",
    "components.Blocks.blocks.image": "Imagen"
};

export { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, es as default, light, or, skipToContent, submit };
//# sourceMappingURL=es.json.mjs.map
