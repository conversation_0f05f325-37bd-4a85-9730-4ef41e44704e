{"version": 3, "file": "routes.mjs", "sources": ["../../../admin/src/history/routes.tsx"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { type PathRouteProps } from 'react-router-dom';\n\nconst ProtectedHistoryPage = React.lazy(() =>\n  import('./pages/History').then((mod) => ({ default: mod.ProtectedHistoryPage }))\n);\n\n/**\n * These routes will be merged with the rest of the Content Manager routes\n */\nconst routes: PathRouteProps[] = [\n  {\n    path: ':collectionType/:slug/:id/history',\n    Component: ProtectedHistoryPage,\n  },\n  {\n    path: ':collectionType/:slug/history',\n    Component: ProtectedHistoryPage,\n  },\n];\n\nexport { routes };\n"], "names": ["ProtectedHistoryPage", "React", "lazy", "then", "mod", "default", "routes", "path", "Component"], "mappings": ";;AAKA,MAAMA,oBAAuBC,iBAAAA,KAAAA,CAAMC,IAAI,CAAC,IACtC,OAAO,qBAAA,CAAA,CAAmBC,IAAI,CAAC,CAACC,GAAAA,IAAS;AAAEC,YAAAA,OAAAA,EAASD,IAAIJ;SAAqB,CAAA,CAAA,CAAA;AAG/E;;AAEC,UACKM,MAA2B,GAAA;AAC/B,IAAA;QACEC,IAAM,EAAA,mCAAA;QACNC,SAAWR,EAAAA;AACb,KAAA;AACA,IAAA;QACEO,IAAM,EAAA,+BAAA;QACNC,SAAWR,EAAAA;AACb;AACD;;;;"}