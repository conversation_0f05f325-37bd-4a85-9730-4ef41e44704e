import { jsx, jsxs } from 'react/jsx-runtime';
import * as React from 'react';
import { Main, Box, Typography, Flex, Button, Link } from '@strapi/design-system';
import camelCase from 'lodash/camelCase';
import { useIntl } from 'react-intl';
import { useLocation, useNavigate, NavLink } from 'react-router-dom';
import * as yup from 'yup';
import { Form } from '../../../components/Form.mjs';
import { InputRenderer as MemoizedInputRenderer } from '../../../components/FormInputs/Renderer.mjs';
import { Logo } from '../../../components/UnauthenticatedLogo.mjs';
import { useAuth } from '../../../features/Auth.mjs';
import { UnauthenticatedLayout, LayoutContent, Column } from '../../../layouts/UnauthenticatedLayout.mjs';
import { translatedErrors as errorsTrads } from '../../../utils/translatedErrors.mjs';

const LOGIN_SCHEMA = yup.object().shape({
    email: yup.string().nullable().email({
        id: errorsTrads.email.id,
        defaultMessage: 'Not a valid email'
    }).required(errorsTrads.required),
    password: yup.string().required(errorsTrads.required).nullable(),
    rememberMe: yup.bool().nullable()
});
const Login = ({ children })=>{
    const [apiError, setApiError] = React.useState();
    const { formatMessage } = useIntl();
    const { search: searchString } = useLocation();
    const query = React.useMemo(()=>new URLSearchParams(searchString), [
        searchString
    ]);
    const navigate = useNavigate();
    const { login } = useAuth('Login', (auth)=>auth);
    const handleLogin = async (body)=>{
        setApiError(undefined);
        const res = await login(body);
        if ('error' in res) {
            const message = res.error.message ?? 'Something went wrong';
            if (camelCase(message).toLowerCase() === 'usernotactive') {
                navigate('/auth/oops');
                return;
            }
            setApiError(message);
        } else {
            const redirectTo = query.get('redirectTo');
            const redirectUrl = redirectTo ? decodeURIComponent(redirectTo) : '/';
            navigate(redirectUrl);
        }
    };
    return /*#__PURE__*/ jsx(UnauthenticatedLayout, {
        children: /*#__PURE__*/ jsxs(Main, {
            children: [
                /*#__PURE__*/ jsxs(LayoutContent, {
                    children: [
                        /*#__PURE__*/ jsxs(Column, {
                            children: [
                                /*#__PURE__*/ jsx(Logo, {}),
                                /*#__PURE__*/ jsx(Box, {
                                    paddingTop: 6,
                                    paddingBottom: 1,
                                    children: /*#__PURE__*/ jsx(Typography, {
                                        variant: "alpha",
                                        tag: "h1",
                                        children: formatMessage({
                                            id: 'Auth.form.welcome.title',
                                            defaultMessage: 'Welcome!'
                                        })
                                    })
                                }),
                                /*#__PURE__*/ jsx(Box, {
                                    paddingBottom: 7,
                                    children: /*#__PURE__*/ jsx(Typography, {
                                        variant: "epsilon",
                                        textColor: "neutral600",
                                        children: formatMessage({
                                            id: 'Auth.form.welcome.subtitle',
                                            defaultMessage: 'Log in to your Strapi account'
                                        })
                                    })
                                }),
                                apiError ? /*#__PURE__*/ jsx(Typography, {
                                    id: "global-form-error",
                                    role: "alert",
                                    tabIndex: -1,
                                    textColor: "danger600",
                                    children: apiError
                                }) : null
                            ]
                        }),
                        /*#__PURE__*/ jsx(Form, {
                            method: "PUT",
                            initialValues: {
                                email: '',
                                password: '',
                                rememberMe: false
                            },
                            onSubmit: (values)=>{
                                handleLogin(values);
                            },
                            validationSchema: LOGIN_SCHEMA,
                            children: /*#__PURE__*/ jsxs(Flex, {
                                direction: "column",
                                alignItems: "stretch",
                                gap: 6,
                                children: [
                                    [
                                        {
                                            label: formatMessage({
                                                id: 'Auth.form.email.label',
                                                defaultMessage: 'Email'
                                            }),
                                            name: 'email',
                                            placeholder: formatMessage({
                                                id: 'Auth.form.email.placeholder',
                                                defaultMessage: '<EMAIL>'
                                            }),
                                            required: true,
                                            type: 'string'
                                        },
                                        {
                                            label: formatMessage({
                                                id: 'global.password',
                                                defaultMessage: 'Password'
                                            }),
                                            name: 'password',
                                            required: true,
                                            type: 'password'
                                        },
                                        {
                                            label: formatMessage({
                                                id: 'Auth.form.rememberMe.label',
                                                defaultMessage: 'Remember me'
                                            }),
                                            name: 'rememberMe',
                                            type: 'checkbox'
                                        }
                                    ].map((field)=>/*#__PURE__*/ jsx(MemoizedInputRenderer, {
                                            ...field
                                        }, field.name)),
                                    /*#__PURE__*/ jsx(Button, {
                                        fullWidth: true,
                                        type: "submit",
                                        children: formatMessage({
                                            id: 'Auth.form.button.login',
                                            defaultMessage: 'Login'
                                        })
                                    })
                                ]
                            })
                        }),
                        children
                    ]
                }),
                /*#__PURE__*/ jsx(Flex, {
                    justifyContent: "center",
                    children: /*#__PURE__*/ jsx(Box, {
                        paddingTop: 4,
                        children: /*#__PURE__*/ jsx(Link, {
                            isExternal: false,
                            tag: NavLink,
                            to: "/auth/forgot-password",
                            children: formatMessage({
                                id: 'Auth.link.forgot-password',
                                defaultMessage: 'Forgot your password?'
                            })
                        })
                    })
                })
            ]
        })
    });
};

export { Login };
//# sourceMappingURL=Login.mjs.map
