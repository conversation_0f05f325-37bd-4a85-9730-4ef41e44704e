{"version": 3, "file": "TriggerContainer.js", "sources": ["../../../../../../../../../admin/src/pages/Settings/pages/Webhooks/components/TriggerContainer.tsx"], "sourcesContent": ["import { Box, Flex, Grid, Typography } from '@strapi/design-system';\nimport { Check, <PERSON>, Loader } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\n/* -------------------------------------------------------------------------------------------------\n * TriggerContainer\n * -----------------------------------------------------------------------------------------------*/\n\ninterface TriggerContainerProps extends Pick<StatusProps, 'isPending'> {\n  onCancel: () => void;\n  response?: {\n    statusCode: number;\n    message?: string;\n  };\n}\n\nconst TriggerContainer = ({ isPending, onCancel, response }: TriggerContainerProps) => {\n  const { statusCode, message } = response ?? {};\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box background=\"neutral0\" padding={5} shadow=\"filterShadow\" hasRadius>\n      <Grid.Root gap={4} style={{ alignItems: 'center' }}>\n        <Grid.Item col={3} direction=\"column\" alignItems=\"stretch\">\n          <Typography>\n            {formatMessage({\n              id: 'Settings.webhooks.trigger.test',\n              defaultMessage: 'Test-trigger',\n            })}\n          </Typography>\n        </Grid.Item>\n        <Grid.Item col={3} direction=\"column\" alignItems=\"stretch\">\n          <Status isPending={isPending} statusCode={statusCode} />\n        </Grid.Item>\n        <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n          {!isPending ? (\n            <Message statusCode={statusCode} message={message} />\n          ) : (\n            <Flex justifyContent=\"flex-end\">\n              <button onClick={onCancel} type=\"button\">\n                <Flex gap={2} alignItems=\"center\">\n                  <Typography textColor=\"neutral400\">\n                    {formatMessage({\n                      id: 'Settings.webhooks.trigger.cancel',\n                      defaultMessage: 'cancel',\n                    })}\n                  </Typography>\n                  <Cross fill=\"neutral400\" height=\"1.2rem\" width=\"1.2rem\" />\n                </Flex>\n              </button>\n            </Flex>\n          )}\n        </Grid.Item>\n      </Grid.Root>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Status\n * -----------------------------------------------------------------------------------------------*/\n\ninterface StatusProps {\n  isPending: boolean;\n  statusCode?: number;\n}\n\nconst Status = ({ isPending, statusCode }: StatusProps) => {\n  const { formatMessage } = useIntl();\n\n  if (isPending || !statusCode) {\n    return (\n      <Flex gap={2} alignItems=\"center\">\n        <Loader height=\"1.2rem\" width=\"1.2rem\" />\n        <Typography>\n          {formatMessage({ id: 'Settings.webhooks.trigger.pending', defaultMessage: 'pending' })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (statusCode >= 200 && statusCode < 300) {\n    return (\n      <Flex gap={2} alignItems=\"center\">\n        <Check fill=\"success700\" height=\"1.2rem\" width=\"1.2rem\" />\n        <Typography>\n          {formatMessage({ id: 'Settings.webhooks.trigger.success', defaultMessage: 'success' })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (statusCode >= 300) {\n    return (\n      <Flex gap={2} alignItems=\"center\">\n        <Cross fill=\"danger700\" height=\"1.2rem\" width=\"1.2rem\" />\n        <Typography>\n          {formatMessage({ id: 'Settings.error', defaultMessage: 'error' })} {statusCode}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  return null;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Message\n * -----------------------------------------------------------------------------------------------*/\n\ninterface MessageProps {\n  statusCode?: number;\n  message?: string;\n}\n\nconst Message = ({ statusCode, message }: MessageProps) => {\n  const { formatMessage } = useIntl();\n\n  if (!statusCode) {\n    return null;\n  }\n\n  if (statusCode >= 200 && statusCode < 300) {\n    return (\n      <Flex justifyContent=\"flex-end\">\n        <Typography textColor=\"neutral600\" ellipsis>\n          {formatMessage({\n            id: 'Settings.webhooks.trigger.success.label',\n            defaultMessage: 'Trigger succeeded',\n          })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (statusCode >= 300) {\n    return (\n      <Flex justifyContent=\"flex-end\">\n        <Flex maxWidth={`25rem`} justifyContent=\"flex-end\" title={message}>\n          <Typography ellipsis textColor=\"neutral600\">\n            {message}\n          </Typography>\n        </Flex>\n      </Flex>\n    );\n  }\n\n  return null;\n};\n\nexport { TriggerContainer };\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPending", "onCancel", "response", "statusCode", "message", "formatMessage", "useIntl", "_jsx", "Box", "background", "padding", "shadow", "hasRadius", "_jsxs", "Grid", "Root", "gap", "style", "alignItems", "<PERSON><PERSON>", "col", "direction", "Typography", "id", "defaultMessage", "Status", "Message", "Flex", "justifyContent", "button", "onClick", "type", "textColor", "Cross", "fill", "height", "width", "Loader", "Check", "ellipsis", "max<PERSON><PERSON><PERSON>", "title"], "mappings": ";;;;;;;AAgBMA,MAAAA,gBAAAA,GAAmB,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAyB,GAAA;AAChF,IAAA,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE,GAAGF,YAAY,EAAC;IAC7C,MAAM,EAAEG,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;AAE1B,IAAA,qBACEC,cAACC,CAAAA,gBAAAA,EAAAA;QAAIC,UAAW,EAAA,UAAA;QAAWC,OAAS,EAAA,CAAA;QAAGC,MAAO,EAAA,cAAA;QAAeC,SAAS,EAAA,IAAA;gCACpEC,eAAA,CAACC,kBAAKC,IAAI,EAAA;YAACC,GAAK,EAAA,CAAA;YAAGC,KAAO,EAAA;gBAAEC,UAAY,EAAA;AAAS,aAAA;;AAC/C,8BAAAX,cAAA,CAACO,kBAAKK,IAAI,EAAA;oBAACC,GAAK,EAAA,CAAA;oBAAGC,SAAU,EAAA,QAAA;oBAASH,UAAW,EAAA,SAAA;AAC/C,oBAAA,QAAA,gBAAAX,cAACe,CAAAA,uBAAAA,EAAAA;kCACEjB,aAAc,CAAA;4BACbkB,EAAI,EAAA,gCAAA;4BACJC,cAAgB,EAAA;AAClB,yBAAA;;;AAGJ,8BAAAjB,cAAA,CAACO,kBAAKK,IAAI,EAAA;oBAACC,GAAK,EAAA,CAAA;oBAAGC,SAAU,EAAA,QAAA;oBAASH,UAAW,EAAA,SAAA;AAC/C,oBAAA,QAAA,gBAAAX,cAACkB,CAAAA,MAAAA,EAAAA;wBAAOzB,SAAWA,EAAAA,SAAAA;wBAAWG,UAAYA,EAAAA;;;AAE5C,8BAAAI,cAAA,CAACO,kBAAKK,IAAI,EAAA;oBAACC,GAAK,EAAA,CAAA;oBAAGC,SAAU,EAAA,QAAA;oBAASH,UAAW,EAAA,SAAA;AAC9C,oBAAA,QAAA,EAAA,CAAClB,0BACAO,cAACmB,CAAAA,OAAAA,EAAAA;wBAAQvB,UAAYA,EAAAA,UAAAA;wBAAYC,OAASA,EAAAA;uCAE1CG,cAACoB,CAAAA,iBAAAA,EAAAA;wBAAKC,cAAe,EAAA,UAAA;AACnB,wBAAA,QAAA,gBAAArB,cAACsB,CAAAA,QAAAA,EAAAA;4BAAOC,OAAS7B,EAAAA,QAAAA;4BAAU8B,IAAK,EAAA,QAAA;AAC9B,4BAAA,QAAA,gBAAAlB,eAACc,CAAAA,iBAAAA,EAAAA;gCAAKX,GAAK,EAAA,CAAA;gCAAGE,UAAW,EAAA,QAAA;;kDACvBX,cAACe,CAAAA,uBAAAA,EAAAA;wCAAWU,SAAU,EAAA,YAAA;kDACnB3B,aAAc,CAAA;4CACbkB,EAAI,EAAA,kCAAA;4CACJC,cAAgB,EAAA;AAClB,yCAAA;;kDAEFjB,cAAC0B,CAAAA,WAAAA,EAAAA;wCAAMC,IAAK,EAAA,YAAA;wCAAaC,MAAO,EAAA,QAAA;wCAASC,KAAM,EAAA;;;;;;;;;;AASjE;AAWA,MAAMX,SAAS,CAAC,EAAEzB,SAAS,EAAEG,UAAU,EAAe,GAAA;IACpD,MAAM,EAAEE,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;IAE1B,IAAIN,SAAAA,IAAa,CAACG,UAAY,EAAA;AAC5B,QAAA,qBACEU,eAACc,CAAAA,iBAAAA,EAAAA;YAAKX,GAAK,EAAA,CAAA;YAAGE,UAAW,EAAA,QAAA;;8BACvBX,cAAC8B,CAAAA,YAAAA,EAAAA;oBAAOF,MAAO,EAAA,QAAA;oBAASC,KAAM,EAAA;;8BAC9B7B,cAACe,CAAAA,uBAAAA,EAAAA;8BACEjB,aAAc,CAAA;wBAAEkB,EAAI,EAAA,mCAAA;wBAAqCC,cAAgB,EAAA;AAAU,qBAAA;;;;AAI5F;IAEA,IAAIrB,UAAAA,IAAc,GAAOA,IAAAA,UAAAA,GAAa,GAAK,EAAA;AACzC,QAAA,qBACEU,eAACc,CAAAA,iBAAAA,EAAAA;YAAKX,GAAK,EAAA,CAAA;YAAGE,UAAW,EAAA,QAAA;;8BACvBX,cAAC+B,CAAAA,WAAAA,EAAAA;oBAAMJ,IAAK,EAAA,YAAA;oBAAaC,MAAO,EAAA,QAAA;oBAASC,KAAM,EAAA;;8BAC/C7B,cAACe,CAAAA,uBAAAA,EAAAA;8BACEjB,aAAc,CAAA;wBAAEkB,EAAI,EAAA,mCAAA;wBAAqCC,cAAgB,EAAA;AAAU,qBAAA;;;;AAI5F;AAEA,IAAA,IAAIrB,cAAc,GAAK,EAAA;AACrB,QAAA,qBACEU,eAACc,CAAAA,iBAAAA,EAAAA;YAAKX,GAAK,EAAA,CAAA;YAAGE,UAAW,EAAA,QAAA;;8BACvBX,cAAC0B,CAAAA,WAAAA,EAAAA;oBAAMC,IAAK,EAAA,WAAA;oBAAYC,MAAO,EAAA,QAAA;oBAASC,KAAM,EAAA;;8BAC9CvB,eAACS,CAAAA,uBAAAA,EAAAA;;wBACEjB,aAAc,CAAA;4BAAEkB,EAAI,EAAA,gBAAA;4BAAkBC,cAAgB,EAAA;AAAQ,yBAAA,CAAA;AAAG,wBAAA,GAAA;AAAErB,wBAAAA;;;;;AAI5E;IAEA,OAAO,IAAA;AACT,CAAA;AAWA,MAAMuB,UAAU,CAAC,EAAEvB,UAAU,EAAEC,OAAO,EAAgB,GAAA;IACpD,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;AAE1B,IAAA,IAAI,CAACH,UAAY,EAAA;QACf,OAAO,IAAA;AACT;IAEA,IAAIA,UAAAA,IAAc,GAAOA,IAAAA,UAAAA,GAAa,GAAK,EAAA;AACzC,QAAA,qBACEI,cAACoB,CAAAA,iBAAAA,EAAAA;YAAKC,cAAe,EAAA,UAAA;AACnB,YAAA,QAAA,gBAAArB,cAACe,CAAAA,uBAAAA,EAAAA;gBAAWU,SAAU,EAAA,YAAA;gBAAaO,QAAQ,EAAA,IAAA;0BACxClC,aAAc,CAAA;oBACbkB,EAAI,EAAA,yCAAA;oBACJC,cAAgB,EAAA;AAClB,iBAAA;;;AAIR;AAEA,IAAA,IAAIrB,cAAc,GAAK,EAAA;AACrB,QAAA,qBACEI,cAACoB,CAAAA,iBAAAA,EAAAA;YAAKC,cAAe,EAAA,UAAA;AACnB,YAAA,QAAA,gBAAArB,cAACoB,CAAAA,iBAAAA,EAAAA;gBAAKa,QAAU,EAAA,CAAC,KAAK,CAAC;gBAAEZ,cAAe,EAAA,UAAA;gBAAWa,KAAOrC,EAAAA,OAAAA;AACxD,gBAAA,QAAA,gBAAAG,cAACe,CAAAA,uBAAAA,EAAAA;oBAAWiB,QAAQ,EAAA,IAAA;oBAACP,SAAU,EAAA,YAAA;AAC5B5B,oBAAAA,QAAAA,EAAAA;;;;AAKX;IAEA,OAAO,IAAA;AACT,CAAA;;;;"}