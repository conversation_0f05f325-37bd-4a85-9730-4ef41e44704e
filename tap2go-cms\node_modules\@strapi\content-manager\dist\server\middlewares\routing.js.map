{"version": 3, "file": "routing.js", "sources": ["../../../server/src/middlewares/routing.ts"], "sourcesContent": ["import type { UID, Core, Struct } from '@strapi/types';\nimport type { Context, Next } from 'koa';\nimport isNil from 'lodash/isNil';\n\ninterface ContentType extends Struct.ContentTypeSchema {\n  plugin?: string;\n}\n\nexport default async (ctx: Context, next: Next) => {\n  const { model }: { model: UID.ContentType } = ctx.params;\n\n  const ct: ContentType = strapi.contentTypes[model];\n\n  if (!ct) {\n    return ctx.send({ error: 'contentType.notFound' }, 404);\n  }\n\n  let controllers;\n  if (!ct.plugin || ct.plugin === 'admin') {\n    controllers = strapi.admin.controllers;\n  } else {\n    controllers = strapi.plugin(ct.plugin).controllers;\n  }\n\n  const { route }: { route: Core.Route } = ctx.state;\n\n  if (typeof route.handler !== 'string') {\n    return next();\n  }\n\n  const [, action] = route.handler.split('.');\n\n  let actionConfig: any;\n  if (!ct.plugin || ct.plugin === 'admin') {\n    actionConfig = strapi.config.get(`admin.layout.${ct.modelName}.actions.${action}`);\n  } else {\n    actionConfig = strapi.plugin(ct.plugin).config(`layout.${ct.modelName}.actions.${action}`);\n  }\n\n  if (!isNil(actionConfig)) {\n    const [controller, action] = actionConfig.split('.');\n\n    if (controller && action) {\n      return controllers[controller.toLowerCase()][action](ctx, next);\n    }\n  }\n\n  await next();\n};\n"], "names": ["ctx", "next", "model", "params", "ct", "strapi", "contentTypes", "send", "error", "controllers", "plugin", "admin", "route", "state", "handler", "action", "split", "actionConfig", "config", "get", "modelName", "isNil", "controller", "toLowerCase"], "mappings": ";;;;AAQA,cAAe,CAAA,OAAOA,GAAcC,EAAAA,IAAAA,GAAAA;AAClC,IAAA,MAAM,EAAEC,KAAK,EAAE,GAA+BF,IAAIG,MAAM;AAExD,IAAA,MAAMC,EAAkBC,GAAAA,MAAAA,CAAOC,YAAY,CAACJ,KAAM,CAAA;AAElD,IAAA,IAAI,CAACE,EAAI,EAAA;QACP,OAAOJ,GAAAA,CAAIO,IAAI,CAAC;YAAEC,KAAO,EAAA;SAA0B,EAAA,GAAA,CAAA;AACrD;IAEA,IAAIC,WAAAA;AACJ,IAAA,IAAI,CAACL,EAAGM,CAAAA,MAAM,IAAIN,EAAGM,CAAAA,MAAM,KAAK,OAAS,EAAA;QACvCD,WAAcJ,GAAAA,MAAAA,CAAOM,KAAK,CAACF,WAAW;KACjC,MAAA;AACLA,QAAAA,WAAAA,GAAcJ,OAAOK,MAAM,CAACN,EAAGM,CAAAA,MAAM,EAAED,WAAW;AACpD;AAEA,IAAA,MAAM,EAAEG,KAAK,EAAE,GAA0BZ,IAAIa,KAAK;AAElD,IAAA,IAAI,OAAOD,KAAAA,CAAME,OAAO,KAAK,QAAU,EAAA;QACrC,OAAOb,IAAAA,EAAAA;AACT;AAEA,IAAA,MAAM,GAAGc,MAAO,CAAA,GAAGH,MAAME,OAAO,CAACE,KAAK,CAAC,GAAA,CAAA;IAEvC,IAAIC,YAAAA;AACJ,IAAA,IAAI,CAACb,EAAGM,CAAAA,MAAM,IAAIN,EAAGM,CAAAA,MAAM,KAAK,OAAS,EAAA;AACvCO,QAAAA,YAAAA,GAAeZ,MAAOa,CAAAA,MAAM,CAACC,GAAG,CAAC,CAAC,aAAa,EAAEf,EAAAA,CAAGgB,SAAS,CAAC,SAAS,EAAEL,OAAO,CAAC,CAAA;KAC5E,MAAA;AACLE,QAAAA,YAAAA,GAAeZ,OAAOK,MAAM,CAACN,EAAGM,CAAAA,MAAM,EAAEQ,MAAM,CAAC,CAAC,OAAO,EAAEd,EAAGgB,CAAAA,SAAS,CAAC,SAAS,EAAEL,OAAO,CAAC,CAAA;AAC3F;IAEA,IAAI,CAACM,MAAMJ,YAAe,CAAA,EAAA;AACxB,QAAA,MAAM,CAACK,UAAYP,EAAAA,MAAAA,CAAO,GAAGE,YAAAA,CAAaD,KAAK,CAAC,GAAA,CAAA;AAEhD,QAAA,IAAIM,cAAcP,MAAQ,EAAA;YACxB,OAAON,WAAW,CAACa,UAAWC,CAAAA,WAAW,GAAG,CAACR,MAAAA,CAAO,CAACf,GAAKC,EAAAA,IAAAA,CAAAA;AAC5D;AACF;IAEA,MAAMA,IAAAA,EAAAA;AACR,CAAA;;;;"}