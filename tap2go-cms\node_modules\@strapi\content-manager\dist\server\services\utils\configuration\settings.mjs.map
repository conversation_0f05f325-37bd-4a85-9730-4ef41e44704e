{"version": 3, "file": "settings.mjs", "sources": ["../../../../../server/src/services/utils/configuration/settings.ts"], "sourcesContent": ["import { isEmpty, pick, pipe, propOr, isEqual } from 'lodash/fp';\nimport { traverse } from '@strapi/utils';\nimport qs from 'qs';\nimport { isSortable, getDefaultMainField, getSortableAttributes } from './attributes';\n\n/** General settings */\nconst DEFAULT_SETTINGS = {\n  bulkable: true,\n  filterable: true,\n  searchable: true,\n  pageSize: 10,\n};\n\nconst settingsFields = [\n  'searchable',\n  'filterable',\n  'bulkable',\n  'pageSize',\n  'mainField',\n  'defaultSortBy',\n  'defaultSortOrder',\n];\n\nconst getModelSettings = pipe([propOr({}, 'config.settings'), pick(settingsFields)]);\n\nasync function isValidDefaultSort(schema: any, value: any) {\n  const parsedValue = qs.parse(value);\n\n  const omitNonSortableAttributes = ({ schema, key }: any, { remove }: any) => {\n    const sortableAttributes = getSortableAttributes(schema);\n    if (!sortableAttributes.includes(key)) {\n      remove(key);\n    }\n  };\n\n  const sanitizedValue = await traverse.traverseQuerySort(\n    omitNonSortableAttributes,\n    { schema, getModel: strapi.getModel.bind(strapi) },\n    parsedValue\n  );\n\n  // If any of the keys has been removed, the sort attribute is not valid\n  return isEqual(parsedValue, sanitizedValue);\n}\n\nconst createDefaultSettings = async (schema: any) => {\n  const defaultField = getDefaultMainField(schema);\n\n  return {\n    ...DEFAULT_SETTINGS,\n    mainField: defaultField,\n    defaultSortBy: defaultField,\n    defaultSortOrder: 'ASC',\n    ...getModelSettings(schema),\n  };\n};\n\nconst syncSettings = async (configuration: any, schema: any) => {\n  if (isEmpty(configuration.settings)) return createDefaultSettings(schema);\n\n  const defaultField = getDefaultMainField(schema);\n\n  const { mainField = defaultField, defaultSortBy = defaultField } = configuration.settings || {};\n\n  return {\n    ...configuration.settings,\n    mainField: isSortable(schema, mainField) ? mainField : defaultField,\n    defaultSortBy: (await isValidDefaultSort(schema, defaultSortBy)) ? defaultSortBy : defaultField,\n  };\n};\n\nexport { isValidDefaultSort, createDefaultSettings, syncSettings };\n"], "names": ["DEFAULT_SETTINGS", "bulkable", "filterable", "searchable", "pageSize", "settingsFields", "getModelSettings", "pipe", "propOr", "pick", "isValidDefaultSort", "schema", "value", "parsedValue", "qs", "parse", "omitNonSortableAttributes", "key", "remove", "sortableAttributes", "getSortableAttributes", "includes", "sanitizedValue", "traverse", "traverseQuerySort", "getModel", "strapi", "bind", "isEqual", "createDefaultSettings", "defaultField", "getDefaultMainField", "mainField", "defaultSortBy", "defaultSortOrder", "syncSettings", "configuration", "isEmpty", "settings", "isSortable"], "mappings": ";;;;;AAKA,wBACA,MAAMA,gBAAmB,GAAA;IACvBC,QAAU,EAAA,IAAA;IACVC,UAAY,EAAA,IAAA;IACZC,UAAY,EAAA,IAAA;IACZC,QAAU,EAAA;AACZ,CAAA;AAEA,MAAMC,cAAiB,GAAA;AACrB,IAAA,YAAA;AACA,IAAA,YAAA;AACA,IAAA,UAAA;AACA,IAAA,UAAA;AACA,IAAA,WAAA;AACA,IAAA,eAAA;AACA,IAAA;AACD,CAAA;AAED,MAAMC,mBAAmBC,IAAK,CAAA;AAACC,IAAAA,MAAAA,CAAO,EAAI,EAAA,iBAAA,CAAA;IAAoBC,IAAKJ,CAAAA,cAAAA;AAAgB,CAAA,CAAA;AAEnF,eAAeK,kBAAAA,CAAmBC,MAAW,EAAEC,KAAU,EAAA;IACvD,MAAMC,WAAAA,GAAcC,EAAGC,CAAAA,KAAK,CAACH,KAAAA,CAAAA;IAE7B,MAAMI,yBAAAA,GAA4B,CAAC,EAAEL,MAAM,EAAEM,GAAG,EAAO,EAAE,EAAEC,MAAM,EAAO,GAAA;AACtE,QAAA,MAAMC,qBAAqBC,qBAAsBT,CAAAA,MAAAA,CAAAA;AACjD,QAAA,IAAI,CAACQ,kBAAAA,CAAmBE,QAAQ,CAACJ,GAAM,CAAA,EAAA;YACrCC,MAAOD,CAAAA,GAAAA,CAAAA;AACT;AACF,KAAA;AAEA,IAAA,MAAMK,cAAiB,GAAA,MAAMC,QAASC,CAAAA,iBAAiB,CACrDR,yBACA,EAAA;AAAEL,QAAAA,MAAAA;AAAQc,QAAAA,QAAAA,EAAUC,MAAOD,CAAAA,QAAQ,CAACE,IAAI,CAACD,MAAAA;KACzCb,EAAAA,WAAAA,CAAAA;;AAIF,IAAA,OAAOe,QAAQf,WAAaS,EAAAA,cAAAA,CAAAA;AAC9B;AAEA,MAAMO,wBAAwB,OAAOlB,MAAAA,GAAAA;AACnC,IAAA,MAAMmB,eAAeC,mBAAoBpB,CAAAA,MAAAA,CAAAA;IAEzC,OAAO;AACL,QAAA,GAAGX,gBAAgB;QACnBgC,SAAWF,EAAAA,YAAAA;QACXG,aAAeH,EAAAA,YAAAA;QACfI,gBAAkB,EAAA,KAAA;AAClB,QAAA,GAAG5B,iBAAiBK,MAAO;AAC7B,KAAA;AACF;AAEMwB,MAAAA,YAAAA,GAAe,OAAOC,aAAoBzB,EAAAA,MAAAA,GAAAA;AAC9C,IAAA,IAAI0B,OAAQD,CAAAA,aAAAA,CAAcE,QAAQ,CAAA,EAAG,OAAOT,qBAAsBlB,CAAAA,MAAAA,CAAAA;AAElE,IAAA,MAAMmB,eAAeC,mBAAoBpB,CAAAA,MAAAA,CAAAA;IAEzC,MAAM,EAAEqB,SAAYF,GAAAA,YAAY,EAAEG,aAAAA,GAAgBH,YAAY,EAAE,GAAGM,aAAAA,CAAcE,QAAQ,IAAI,EAAC;IAE9F,OAAO;AACL,QAAA,GAAGF,cAAcE,QAAQ;QACzBN,SAAWO,EAAAA,UAAAA,CAAW5B,MAAQqB,EAAAA,SAAAA,CAAAA,GAAaA,SAAYF,GAAAA,YAAAA;AACvDG,QAAAA,aAAAA,EAAe,MAAOvB,kBAAmBC,CAAAA,MAAAA,EAAQsB,iBAAkBA,aAAgBH,GAAAA;AACrF,KAAA;AACF;;;;"}