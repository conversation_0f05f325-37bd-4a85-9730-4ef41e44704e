"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See LICENSE in the project root for license information.
Object.defineProperty(exports, "__esModule", { value: true });
exports.FolderConstants = exports.FileConstants = void 0;
/**
 * String constants for common filenames and parts of filenames.
 *
 * @public
 */
// eslint-disable-next-line @typescript-eslint/typedef
exports.FileConstants = {
    /**
     * "package.json" - the configuration file that defines an NPM package
     */
    PackageJson: 'package.json'
};
/**
 * String constants for common folder names.
 *
 * @public
 */
// eslint-disable-next-line @typescript-eslint/typedef
exports.FolderConstants = {
    /**
     * ".git" - the data storage for a Git working folder
     */
    Git: '.git',
    /**
     * "node_modules" - the folder where package managers install their files
     */
    NodeModules: 'node_modules'
};
//# sourceMappingURL=Constants.js.map