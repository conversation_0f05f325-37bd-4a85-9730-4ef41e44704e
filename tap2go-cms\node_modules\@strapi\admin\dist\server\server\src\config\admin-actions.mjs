const actions = [
    {
        uid: 'marketplace.read',
        displayName: 'Access the marketplace',
        pluginName: 'admin',
        section: 'settings',
        category: 'plugins and marketplace',
        subCategory: 'marketplace'
    },
    {
        uid: 'webhooks.create',
        displayName: 'Create',
        pluginName: 'admin',
        section: 'settings',
        category: 'webhooks'
    },
    {
        uid: 'webhooks.read',
        displayName: 'Read',
        pluginName: 'admin',
        section: 'settings',
        category: 'webhooks'
    },
    {
        uid: 'webhooks.update',
        displayName: 'Update',
        pluginName: 'admin',
        section: 'settings',
        category: 'webhooks'
    },
    {
        uid: 'webhooks.delete',
        displayName: 'Delete',
        pluginName: 'admin',
        section: 'settings',
        category: 'webhooks'
    },
    {
        uid: 'users.create',
        displayName: 'Create (invite)',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'users'
    },
    {
        uid: 'users.read',
        displayName: 'Read',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'users',
        aliases: [
            {
                actionId: 'plugin::content-manager.explorer.read',
                subjects: [
                    'admin::user'
                ]
            }
        ]
    },
    {
        uid: 'users.update',
        displayName: 'Update',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'users'
    },
    {
        uid: 'users.delete',
        displayName: 'Delete',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'users'
    },
    {
        uid: 'roles.create',
        displayName: 'Create',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'roles'
    },
    {
        uid: 'roles.read',
        displayName: 'Read',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'roles',
        aliases: [
            {
                actionId: 'plugin::content-manager.explorer.read',
                subjects: [
                    'admin::role'
                ]
            }
        ]
    },
    {
        uid: 'roles.update',
        displayName: 'Update',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'roles'
    },
    {
        uid: 'roles.delete',
        displayName: 'Delete',
        pluginName: 'admin',
        section: 'settings',
        category: 'users and roles',
        subCategory: 'roles'
    },
    {
        uid: 'api-tokens.access',
        displayName: 'Access the API tokens settings page',
        pluginName: 'admin',
        section: 'settings',
        category: 'api tokens',
        subCategory: 'api Tokens'
    },
    {
        uid: 'api-tokens.create',
        displayName: 'Create (generate)',
        pluginName: 'admin',
        section: 'settings',
        category: 'api tokens',
        subCategory: 'general'
    },
    {
        uid: 'api-tokens.read',
        displayName: 'Read',
        pluginName: 'admin',
        section: 'settings',
        category: 'api tokens',
        subCategory: 'general'
    },
    {
        uid: 'api-tokens.update',
        displayName: 'Update',
        pluginName: 'admin',
        section: 'settings',
        category: 'api tokens',
        subCategory: 'general'
    },
    {
        uid: 'api-tokens.regenerate',
        displayName: 'Regenerate',
        pluginName: 'admin',
        section: 'settings',
        category: 'api tokens',
        subCategory: 'general'
    },
    {
        uid: 'api-tokens.delete',
        displayName: 'Delete (revoke)',
        pluginName: 'admin',
        section: 'settings',
        category: 'api tokens',
        subCategory: 'general'
    },
    {
        uid: 'project-settings.update',
        displayName: 'Update the project level settings',
        pluginName: 'admin',
        section: 'settings',
        category: 'project'
    },
    {
        uid: 'project-settings.read',
        displayName: 'Read the project level settings',
        pluginName: 'admin',
        section: 'settings',
        category: 'project'
    },
    {
        uid: 'transfer.tokens.access',
        displayName: 'Access the transfer tokens settings page',
        pluginName: 'admin',
        section: 'settings',
        category: 'transfer tokens',
        subCategory: 'transfer tokens'
    },
    {
        uid: 'transfer.tokens.create',
        displayName: 'Create (generate)',
        pluginName: 'admin',
        section: 'settings',
        category: 'transfer tokens',
        subCategory: 'general'
    },
    {
        uid: 'transfer.tokens.read',
        displayName: 'Read',
        pluginName: 'admin',
        section: 'settings',
        category: 'transfer tokens',
        subCategory: 'general'
    },
    {
        uid: 'transfer.tokens.update',
        displayName: 'Update',
        pluginName: 'admin',
        section: 'settings',
        category: 'transfer tokens',
        subCategory: 'general'
    },
    {
        uid: 'transfer.tokens.regenerate',
        displayName: 'Regenerate',
        pluginName: 'admin',
        section: 'settings',
        category: 'transfer tokens',
        subCategory: 'general'
    },
    {
        uid: 'transfer.tokens.delete',
        displayName: 'Delete (revoke)',
        pluginName: 'admin',
        section: 'settings',
        category: 'transfer tokens',
        subCategory: 'general'
    }
];
var adminActions = {
    actions
};

export { actions, adminActions as default };
//# sourceMappingURL=admin-actions.mjs.map
