{"version": 3, "file": "index.js", "sources": ["../../../../admin/src/components/BoundRoute/index.jsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport map from 'lodash/map';\nimport tail from 'lodash/tail';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport getMethodColor from './getMethodColor';\n\nconst MethodBox = styled(Box)`\n  margin: -1px;\n  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};\n`;\n\nfunction BoundRoute({ route }) {\n  const { formatMessage } = useIntl();\n\n  const { method, handler: title, path } = route;\n  const formattedRoute = path ? tail(path.split('/')) : [];\n  const [controller = '', action = ''] = title ? title.split('.') : [];\n  const colors = getMethodColor(route.method);\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Typography variant=\"delta\" tag=\"h3\">\n        {formatMessage({\n          id: 'users-permissions.BoundRoute.title',\n          defaultMessage: 'Bound route to',\n        })}\n        &nbsp;\n        <span>{controller}</span>\n        <Typography variant=\"delta\" textColor=\"primary600\">\n          .{action}\n        </Typography>\n      </Typography>\n      <Flex hasRadius background=\"neutral0\" borderColor=\"neutral200\" gap={0}>\n        <MethodBox background={colors.background} borderColor={colors.border} padding={2}>\n          <Typography fontWeight=\"bold\" textColor={colors.text}>\n            {method}\n          </Typography>\n        </MethodBox>\n        <Box paddingLeft={2} paddingRight={2}>\n          {map(formattedRoute, (value) => (\n            <Typography key={value} textColor={value.includes(':') ? 'neutral600' : 'neutral900'}>\n              /{value}\n            </Typography>\n          ))}\n        </Box>\n      </Flex>\n    </Flex>\n  );\n}\n\nBoundRoute.defaultProps = {\n  route: {\n    handler: 'Nocontroller.error',\n    method: 'GET',\n    path: '/there-is-no-path',\n  },\n};\n\nBoundRoute.propTypes = {\n  route: PropTypes.shape({\n    handler: PropTypes.string,\n    method: PropTypes.string,\n    path: PropTypes.string,\n  }),\n};\n\nexport default BoundRoute;\n"], "names": ["MethodBox", "styled", "Box", "theme", "spaces", "BoundRoute", "route", "formatMessage", "useIntl", "method", "handler", "title", "path", "formattedRoute", "tail", "split", "controller", "action", "colors", "getMethodColor", "_jsxs", "Flex", "direction", "alignItems", "gap", "Typography", "variant", "tag", "id", "defaultMessage", "_jsx", "span", "textColor", "hasRadius", "background", "borderColor", "border", "padding", "fontWeight", "text", "paddingLeft", "paddingRight", "map", "value", "includes", "defaultProps", "propTypes", "PropTypes", "shape", "string"], "mappings": ";;;;;;;;;;;;AAWA,MAAMA,SAAAA,GAAYC,uBAAOC,CAAAA,gBAAAA,CAAI;;iBAEZ,EAAE,CAAC,EAAEC,KAAK,EAAE,GAAKA,KAAMC,CAAAA,MAAM,CAAC,CAAE,CAAA,CAAC,KAAK,EAAE,CAAC,EAAED,KAAK,EAAE,GAAKA,KAAMC,CAAAA,MAAM,CAAC,CAAA,CAAE,CAAC;AACxF,CAAC;AAED,SAASC,UAAAA,CAAW,EAAEC,KAAK,EAAE,EAAA;IAC3B,MAAM,EAAEC,aAAa,EAAE,GAAGC,iBAAAA,EAAAA;IAE1B,MAAM,EAAEC,MAAM,EAAEC,OAAAA,EAASC,KAAK,EAAEC,IAAI,EAAE,GAAGN,KAAAA;AACzC,IAAA,MAAMO,iBAAiBD,IAAOE,GAAAA,IAAAA,CAAKF,KAAKG,KAAK,CAAC,QAAQ,EAAE;AACxD,IAAA,MAAM,CAACC,UAAAA,GAAa,EAAE,EAAEC,MAAS,GAAA,EAAE,CAAC,GAAGN,KAAQA,GAAAA,KAAAA,CAAMI,KAAK,CAAC,OAAO,EAAE;IACpE,MAAMG,MAAAA,GAASC,cAAeb,CAAAA,KAAAA,CAAMG,MAAM,CAAA;AAE1C,IAAA,qBACEW,eAACC,CAAAA,iBAAAA,EAAAA;QAAKC,SAAU,EAAA,QAAA;QAASC,UAAW,EAAA,SAAA;QAAUC,GAAK,EAAA,CAAA;;0BACjDJ,eAACK,CAAAA,uBAAAA,EAAAA;gBAAWC,OAAQ,EAAA,OAAA;gBAAQC,GAAI,EAAA,IAAA;;oBAC7BpB,aAAc,CAAA;wBACbqB,EAAI,EAAA,oCAAA;wBACJC,cAAgB,EAAA;AAClB,qBAAA,CAAA;AAAG,oBAAA,GAAA;kCAEHC,cAACC,CAAAA,MAAAA,EAAAA;AAAMf,wBAAAA,QAAAA,EAAAA;;kCACPI,eAACK,CAAAA,uBAAAA,EAAAA;wBAAWC,OAAQ,EAAA,OAAA;wBAAQM,SAAU,EAAA,YAAA;;AAAa,4BAAA,GAAA;AAC/Cf,4BAAAA;;;;;0BAGNG,eAACC,CAAAA,iBAAAA,EAAAA;gBAAKY,SAAS,EAAA,IAAA;gBAACC,UAAW,EAAA,UAAA;gBAAWC,WAAY,EAAA,YAAA;gBAAaX,GAAK,EAAA,CAAA;;kCAClEM,cAAC9B,CAAAA,SAAAA,EAAAA;AAAUkC,wBAAAA,UAAAA,EAAYhB,OAAOgB,UAAU;AAAEC,wBAAAA,WAAAA,EAAajB,OAAOkB,MAAM;wBAAEC,OAAS,EAAA,CAAA;AAC7E,wBAAA,QAAA,gBAAAP,cAACL,CAAAA,uBAAAA,EAAAA;4BAAWa,UAAW,EAAA,MAAA;AAAON,4BAAAA,SAAAA,EAAWd,OAAOqB,IAAI;AACjD9B,4BAAAA,QAAAA,EAAAA;;;kCAGLqB,cAAC5B,CAAAA,gBAAAA,EAAAA;wBAAIsC,WAAa,EAAA,CAAA;wBAAGC,YAAc,EAAA,CAAA;kCAChCC,GAAI7B,CAAAA,cAAAA,EAAgB,CAAC8B,KAAAA,iBACpBvB,eAACK,CAAAA,uBAAAA,EAAAA;AAAuBO,gCAAAA,SAAAA,EAAWW,KAAMC,CAAAA,QAAQ,CAAC,GAAA,CAAA,GAAO,YAAe,GAAA,YAAA;;AAAc,oCAAA,GAAA;AAClFD,oCAAAA;;AADaA,6BAAAA,EAAAA,KAAAA,CAAAA;;;;;;AAQ7B;AAEAtC,UAAAA,CAAWwC,YAAY,GAAG;IACxBvC,KAAO,EAAA;QACLI,OAAS,EAAA,oBAAA;QACTD,MAAQ,EAAA,KAAA;QACRG,IAAM,EAAA;AACR;AACF,CAAA;AAEAP,UAAAA,CAAWyC,SAAS,GAAG;IACrBxC,KAAOyC,EAAAA,SAAAA,CAAUC,KAAK,CAAC;AACrBtC,QAAAA,OAAAA,EAASqC,UAAUE,MAAM;AACzBxC,QAAAA,MAAAA,EAAQsC,UAAUE,MAAM;AACxBrC,QAAAA,IAAAA,EAAMmC,UAAUE;AAClB,KAAA;AACF,CAAA;;;;"}