{"version": 3, "file": "useFormatTimeStamp.js", "sources": ["../../../../../../../../../../ee/admin/src/pages/SettingsPage/pages/AuditLogs/hooks/useFormatTimeStamp.ts"], "sourcesContent": ["import parseISO from 'date-fns/parseISO';\nimport { useIntl } from 'react-intl';\n\nexport const useFormatTimeStamp = () => {\n  const { formatDate } = useIntl();\n\n  const formatTimeStamp = (value: string) => {\n    const date = parseISO(value);\n\n    const formattedDate = formatDate(date, {\n      dateStyle: 'long',\n    });\n    const formattedTime = formatDate(date, {\n      timeStyle: 'medium',\n      hourCycle: 'h24',\n    });\n\n    return `${formattedDate}, ${formattedTime}`;\n  };\n\n  return formatTimeStamp;\n};\n"], "names": ["useFormatTimeStamp", "formatDate", "useIntl", "formatTimeStamp", "value", "date", "parseISO", "formattedDate", "dateStyle", "formattedTime", "timeStyle", "hourCycle"], "mappings": ";;;;;MAGaA,kBAAqB,GAAA,IAAA;IAChC,MAAM,EAAEC,UAAU,EAAE,GAAGC,iBAAAA,EAAAA;AAEvB,IAAA,MAAMC,kBAAkB,CAACC,KAAAA,GAAAA;AACvB,QAAA,MAAMC,OAAOC,QAASF,CAAAA,KAAAA,CAAAA;QAEtB,MAAMG,aAAAA,GAAgBN,WAAWI,IAAM,EAAA;YACrCG,SAAW,EAAA;AACb,SAAA,CAAA;QACA,MAAMC,aAAAA,GAAgBR,WAAWI,IAAM,EAAA;YACrCK,SAAW,EAAA,QAAA;YACXC,SAAW,EAAA;AACb,SAAA,CAAA;AAEA,QAAA,OAAO,CAAC,EAAEJ,aAAAA,CAAc,EAAE,EAAEE,cAAc,CAAC;AAC7C,KAAA;IAEA,OAAON,eAAAA;AACT;;;;"}