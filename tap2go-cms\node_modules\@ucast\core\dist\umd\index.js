!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(((t=t||self).ucast=t.ucast||{},t.ucast.core={}))}(this,(function(t){"use strict";function n(t,n){for(var r=0;r<n.length;r++){var e=n[r];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}function r(){return(r=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(this,arguments)}function e(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n}var i=function(){function t(t,n){this.operator=t,this.value=n,Object.defineProperty(this,"t",{writable:!0})}var r,e,i;return t.prototype.addNote=function(t){this.t=this.t||[],this.t.push(t)},r=t,(e=[{key:"notes",get:function(){return this.t}}])&&n(r.prototype,e),i&&n(r,i),t}(),o=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n}(i),u=function(t){function n(n,r){if(!Array.isArray(r))throw new Error('"'+n+'" operator expects to receive an array of conditions');return t.call(this,n,r)||this}return e(n,t),n}(o),f=function(t){function n(n,r,e){var i;return(i=t.call(this,n,e)||this).field=r,i}return e(n,t),n}(i),s=new o("__null__",null),c=Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function a(t,n){return n instanceof u&&n.operator===t}function h(t,n){return 1===n.length?n[0]:new u(t,function t(n,r,e){for(var i=e||[],o=0,u=r.length;o<u;o++){var f=r[o];a(n,f)?t(n,f.value,i):i.push(f)}return i}(t,n))}var v=function(t){return t},d=function(){return Object.create(null)},l=Object.defineProperty(d(),"__@type@__",{value:"ignore value"});function p(t,n,r){if(void 0===r&&(r=!1),!t||t&&t.constructor!==Object)return!1;for(var e in t){if(c(t,e)&&c(n,e)&&(!r||t[e]!==l))return!0}return!1}function b(t){var n=[];for(var r in t)c(t,r)&&t[r]!==l&&n.push(r);return n}function w(t,n){n!==s&&t.push(n)}var y=function(t){return h("and",t)},j={compound:function(t,n,r){var e=(Array.isArray(n)?n:[n]).map((function(t){return r.parse(t)}));return new u(t.name,e)},field:function(t,n,r){return new f(t.name,r.field,n)},document:function(t,n){return new o(t.name,n)}},O=function(){function t(t,n){var e=this;void 0===n&&(n=d()),this.i=void 0,this.o=void 0,this.u=void 0,this.s=void 0,this.h=void 0,this.parse=this.parse.bind(this),this.s={operatorToConditionName:n.operatorToConditionName||v,defaultOperatorName:n.defaultOperatorName||"eq",mergeFinalConditions:n.mergeFinalConditions||y},this.i=Object.keys(t).reduce((function(n,i){return n[i]=r({name:e.s.operatorToConditionName(i)},t[i]),n}),{}),this.o=r({},n.fieldContext,{field:"",query:{},parse:this.parse,hasOperators:function(t){return p(t,e.i,n.useIgnoreValue)}}),this.u=r({},n.documentContext,{parse:this.parse,query:{}}),this.h=n.useIgnoreValue?b:Object.keys}var n=t.prototype;return n.setParse=function(t){this.parse=t,this.o.parse=t,this.u.parse=t},n.parseField=function(t,n,r,e){var i=this.i[n];if(!i)throw new Error('Unsupported operator "'+n+'"');if("field"!==i.type)throw new Error("Unexpected "+i.type+' operator "'+n+'" at field level');return this.o.field=t,this.o.query=e,this.parseInstruction(i,r,this.o)},n.parseInstruction=function(t,n,r){return"function"==typeof t.validate&&t.validate(t,n),(t.parse||j[t.type])(t,n,r)},n.parseFieldOperators=function(t,n){for(var r=[],e=this.h(n),i=0,o=e.length;i<o;i++){var u=e[i];if(!this.i[u])throw new Error('Field query for "'+t+'" may contain only operators or a plain object as a value');w(r,this.parseField(t,u,n[u],n))}return r},n.parse=function(t){var n=[],r=this.h(t);this.u.query=t;for(var e=0,i=r.length;e<i;e++){var o=r[e],u=t[o],f=this.i[o];if(f){if("document"!==f.type&&"compound"!==f.type)throw new Error('Cannot use parsing instruction for operator "'+o+'" in "document" context as it is supposed to be used in  "'+f.type+'" context');w(n,this.parseInstruction(f,u,this.u))}else this.o.hasOperators(u)?n.push.apply(n,this.parseFieldOperators(o,u)):w(n,this.parseField(o,this.s.defaultOperatorName,u,t))}return this.s.mergeFinalConditions(n)},t}();function _(t,n){var r=t[n];if("function"!=typeof r)throw new Error('Unable to interpret "'+n+'" condition. Did you forget to register interpreter for it?');return r}function m(t){return t.operator}var x=O.prototype.parseInstruction;t.CompoundCondition=u,t.Condition=i,t.DocumentCondition=o,t.FieldCondition=f,t.ITSELF="__itself__",t.NULL_CONDITION=s,t.ObjectQueryParser=O,t.buildAnd=y,t.buildOr=function(t){return h("or",t)},t.createInterpreter=function(t,n){var e,i=n,o=i&&i.getInterpreterName||m;switch(i?i.numberOfArguments:0){case 1:e=function(n){var r=o(n,i);return _(t,r)(n,u)};break;case 3:e=function(n,r,e){var f=o(n,i);return _(t,f)(n,r,e,u)};break;default:e=function(n,r){var e=o(n,i);return _(t,e)(n,r,u)}}var u=r({},i,{interpret:e});return u.interpret},t.createTranslatorFactory=function(t,n){return function(r){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];var u=t.apply(void 0,[r].concat(i)),f=n.bind(null,u);return f.ast=u,f}},t.defaultInstructionParsers=j,t.hasOperators=p,t.identity=v,t.ignoreValue=l,t.isCompound=a,t.object=d,t.optimizedCompoundCondition=h,t.parseInstruction=x,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=index.js.map
