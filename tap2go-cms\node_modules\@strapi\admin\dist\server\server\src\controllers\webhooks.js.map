{"version": 3, "file": "webhooks.js", "sources": ["../../../../../server/src/controllers/webhooks.ts"], "sourcesContent": ["import isLocalhostIp from 'is-localhost-ip';\n// Regular import references a deprecated node module,\n// See https://www.npmjs.com/package/punycode.js#installation\nimport punycode from 'punycode/';\nimport type { Context } from 'koa';\nimport _ from 'lodash';\n\nimport { yup, validateYupSchema } from '@strapi/utils';\n\nimport type { Modules } from '@strapi/types';\n\nimport {\n  CreateWebhook,\n  DeleteWebhook,\n  DeleteWebhooks,\n  GetWebhook,\n  UpdateWebhook,\n  TriggerWebhook,\n  GetWebhooks,\n} from '../../../shared/contracts/webhooks';\n\nconst urlRegex =\n  /^(?:([a-z0-9+.-]+):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9_]-*)*[a-z\\u00a1-\\uffff0-9_]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9_]-*)*[a-z\\u00a1-\\uffff0-9_]+)*\\.?)(?::\\d{2,5})?(?:[/?#]\\S*)?$/;\n\nconst webhookValidator = yup\n  .object({\n    name: yup.string().required(),\n    url: yup\n      .string()\n      .matches(urlRegex, 'url must be a valid URL')\n      .required()\n      .test(\n        'is-public-url',\n        \"Url is not supported because it isn't reachable over the public internet\",\n        async (url) => {\n          if (process.env.NODE_ENV !== 'production') {\n            return true;\n          }\n\n          try {\n            const parsedUrl = new URL(punycode.toASCII(url!));\n            const isLocalUrl = await isLocalhostIp(parsedUrl.hostname);\n            return !isLocalUrl;\n          } catch {\n            return false;\n          }\n        }\n      ),\n    headers: yup.lazy((data) => {\n      if (typeof data !== 'object') {\n        return yup.object().required();\n      }\n\n      return yup\n        .object(\n          // @ts-expect-error lodash types\n          _.mapValues(data, () => {\n            yup.string().min(1).required();\n          })\n        )\n        .required();\n    }),\n    events: yup.array().of(yup.string()).required(),\n  })\n  .noUnknown();\n\nconst updateWebhookValidator = webhookValidator.shape({\n  isEnabled: yup.boolean(),\n});\n\nexport default {\n  async listWebhooks(ctx: Context) {\n    const webhooks = await strapi.get('webhookStore').findWebhooks();\n    ctx.send({ data: webhooks } satisfies GetWebhooks.Response);\n  },\n\n  async getWebhook(ctx: Context) {\n    const { id } = ctx.params;\n    const webhook = await strapi.get('webhookStore').findWebhook(id);\n\n    if (!webhook) {\n      return ctx.notFound('webhook.notFound');\n    }\n\n    ctx.send({ data: webhook } satisfies GetWebhook.Response);\n  },\n\n  async createWebhook(ctx: Context) {\n    const { body } = ctx.request as CreateWebhook.Request;\n\n    await validateYupSchema(webhookValidator)(body);\n\n    const webhook = await strapi.get('webhookStore').createWebhook(body);\n\n    strapi.get('webhookRunner').add(webhook);\n\n    ctx.created({ data: webhook } satisfies CreateWebhook.Response);\n  },\n\n  async updateWebhook(ctx: Context) {\n    const { id } = ctx.params as UpdateWebhook.Params;\n    const { body } = ctx.request as UpdateWebhook.Request;\n\n    await validateYupSchema(updateWebhookValidator)(body);\n\n    const webhook = await strapi.get('webhookStore').findWebhook(id);\n\n    if (!webhook) {\n      return ctx.notFound('webhook.notFound');\n    }\n\n    const updatedWebhook = await strapi.get('webhookStore').updateWebhook(id, {\n      ...webhook,\n      ...body,\n    });\n\n    if (!updatedWebhook) {\n      return ctx.notFound('webhook.notFound');\n    }\n\n    strapi.get('webhookRunner').update(updatedWebhook);\n\n    ctx.send({ data: updatedWebhook } satisfies UpdateWebhook.Response);\n  },\n\n  async deleteWebhook(ctx: Context) {\n    const { id } = ctx.params;\n    const webhook = await strapi.get('webhookStore').findWebhook(id);\n\n    if (!webhook) {\n      return ctx.notFound('webhook.notFound');\n    }\n\n    await strapi.get('webhookStore').deleteWebhook(id);\n\n    strapi.get('webhookRunner').remove(webhook);\n\n    ctx.body = { data: webhook } satisfies DeleteWebhook.Response;\n  },\n\n  async deleteWebhooks(ctx: Context) {\n    const { ids } = ctx.request.body as DeleteWebhooks.Request['body'];\n\n    if (!Array.isArray(ids) || ids.length === 0) {\n      return ctx.badRequest('ids must be an array of id');\n    }\n\n    for (const id of ids) {\n      const webhook = await strapi.get('webhookStore').findWebhook(id);\n\n      if (webhook) {\n        await strapi.get('webhookStore').deleteWebhook(id);\n        strapi.get('webhookRunner').remove(webhook);\n      }\n    }\n\n    ctx.send({ data: ids } satisfies DeleteWebhooks.Response);\n  },\n\n  async triggerWebhook(ctx: Context) {\n    const { id } = ctx.params;\n\n    const webhook = await strapi.get('webhookStore').findWebhook(id);\n\n    const response = await strapi\n      .get('webhookRunner')\n      .run(webhook as Modules.WebhookStore.Webhook, 'trigger-test', {});\n\n    ctx.body = { data: response } satisfies TriggerWebhook.Response;\n  },\n};\n"], "names": ["urlRegex", "webhookValidator", "yup", "object", "name", "string", "required", "url", "matches", "test", "process", "env", "NODE_ENV", "parsedUrl", "URL", "punycode", "toASCII", "isLocalUrl", "isLocalhostIp", "hostname", "headers", "lazy", "data", "_", "mapValues", "min", "events", "array", "of", "noUnknown", "updateWebhookValidator", "shape", "isEnabled", "boolean", "listWebhooks", "ctx", "webhooks", "strapi", "get", "findWebhooks", "send", "getWebhook", "id", "params", "webhook", "findWebhook", "notFound", "createWebhook", "body", "request", "validateYupSchema", "add", "created", "updateWebhook", "updatedWebhook", "update", "deleteWebhook", "remove", "deleteWebhooks", "ids", "Array", "isArray", "length", "badRequest", "triggerWebhook", "response", "run"], "mappings": ";;;;;;;AAqBA,MAAMA,QACJ,GAAA,oTAAA;AAEF,MAAMC,gBAAAA,GAAmBC,SACtBC,CAAAA,MAAM,CAAC;IACNC,IAAMF,EAAAA,SAAAA,CAAIG,MAAM,EAAA,CAAGC,QAAQ,EAAA;AAC3BC,IAAAA,GAAAA,EAAKL,SACFG,CAAAA,MAAM,EACNG,CAAAA,OAAO,CAACR,QAAAA,EAAU,yBAClBM,CAAAA,CAAAA,QAAQ,EACRG,CAAAA,IAAI,CACH,eAAA,EACA,4EACA,OAAOF,GAAAA,GAAAA;AACL,QAAA,IAAIG,OAAQC,CAAAA,GAAG,CAACC,QAAQ,KAAK,YAAc,EAAA;YACzC,OAAO,IAAA;AACT;QAEA,IAAI;AACF,YAAA,MAAMC,SAAY,GAAA,IAAIC,GAAIC,CAAAA,QAAAA,CAASC,OAAO,CAACT,GAAAA,CAAAA,CAAAA;AAC3C,YAAA,MAAMU,UAAa,GAAA,MAAMC,aAAcL,CAAAA,SAAAA,CAAUM,QAAQ,CAAA;AACzD,YAAA,OAAO,CAACF,UAAAA;AACV,SAAA,CAAE,OAAM;YACN,OAAO,KAAA;AACT;AACF,KAAA,CAAA;IAEJG,OAASlB,EAAAA,SAAAA,CAAImB,IAAI,CAAC,CAACC,IAAAA,GAAAA;QACjB,IAAI,OAAOA,SAAS,QAAU,EAAA;YAC5B,OAAOpB,SAAAA,CAAIC,MAAM,EAAA,CAAGG,QAAQ,EAAA;AAC9B;QAEA,OAAOJ,SAAAA,CACJC,MAAM;QAELoB,CAAEC,CAAAA,SAAS,CAACF,IAAM,EAAA,IAAA;AAChBpB,YAAAA,SAAAA,CAAIG,MAAM,EAAA,CAAGoB,GAAG,CAAC,GAAGnB,QAAQ,EAAA;AAC9B,SAAA,CAAA,CAAA,CAEDA,QAAQ,EAAA;AACb,KAAA,CAAA;IACAoB,MAAQxB,EAAAA,SAAAA,CAAIyB,KAAK,EAAGC,CAAAA,EAAE,CAAC1B,SAAIG,CAAAA,MAAM,IAAIC,QAAQ;AAC/C,CAAA,CAAA,CACCuB,SAAS,EAAA;AAEZ,MAAMC,sBAAAA,GAAyB7B,gBAAiB8B,CAAAA,KAAK,CAAC;AACpDC,IAAAA,SAAAA,EAAW9B,UAAI+B,OAAO;AACxB,CAAA,CAAA;AAEA,eAAe;AACb,IAAA,MAAMC,cAAaC,GAAY,EAAA;AAC7B,QAAA,MAAMC,WAAW,MAAMC,MAAAA,CAAOC,GAAG,CAAC,gBAAgBC,YAAY,EAAA;AAC9DJ,QAAAA,GAAAA,CAAIK,IAAI,CAAC;YAAElB,IAAMc,EAAAA;AAAS,SAAA,CAAA;AAC5B,KAAA;AAEA,IAAA,MAAMK,YAAWN,GAAY,EAAA;AAC3B,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AACzB,QAAA,MAAMC,UAAU,MAAMP,MAAAA,CAAOC,GAAG,CAAC,cAAA,CAAA,CAAgBO,WAAW,CAACH,EAAAA,CAAAA;AAE7D,QAAA,IAAI,CAACE,OAAS,EAAA;YACZ,OAAOT,GAAAA,CAAIW,QAAQ,CAAC,kBAAA,CAAA;AACtB;AAEAX,QAAAA,GAAAA,CAAIK,IAAI,CAAC;YAAElB,IAAMsB,EAAAA;AAAQ,SAAA,CAAA;AAC3B,KAAA;AAEA,IAAA,MAAMG,eAAcZ,GAAY,EAAA;AAC9B,QAAA,MAAM,EAAEa,IAAI,EAAE,GAAGb,IAAIc,OAAO;AAE5B,QAAA,MAAMC,wBAAkBjD,gBAAkB+C,CAAAA,CAAAA,IAAAA,CAAAA;AAE1C,QAAA,MAAMJ,UAAU,MAAMP,MAAAA,CAAOC,GAAG,CAAC,cAAA,CAAA,CAAgBS,aAAa,CAACC,IAAAA,CAAAA;AAE/DX,QAAAA,MAAAA,CAAOC,GAAG,CAAC,eAAiBa,CAAAA,CAAAA,GAAG,CAACP,OAAAA,CAAAA;AAEhCT,QAAAA,GAAAA,CAAIiB,OAAO,CAAC;YAAE9B,IAAMsB,EAAAA;AAAQ,SAAA,CAAA;AAC9B,KAAA;AAEA,IAAA,MAAMS,eAAclB,GAAY,EAAA;AAC9B,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AACzB,QAAA,MAAM,EAAEK,IAAI,EAAE,GAAGb,IAAIc,OAAO;AAE5B,QAAA,MAAMC,wBAAkBpB,sBAAwBkB,CAAAA,CAAAA,IAAAA,CAAAA;AAEhD,QAAA,MAAMJ,UAAU,MAAMP,MAAAA,CAAOC,GAAG,CAAC,cAAA,CAAA,CAAgBO,WAAW,CAACH,EAAAA,CAAAA;AAE7D,QAAA,IAAI,CAACE,OAAS,EAAA;YACZ,OAAOT,GAAAA,CAAIW,QAAQ,CAAC,kBAAA,CAAA;AACtB;QAEA,MAAMQ,cAAAA,GAAiB,MAAMjB,MAAOC,CAAAA,GAAG,CAAC,cAAgBe,CAAAA,CAAAA,aAAa,CAACX,EAAI,EAAA;AACxE,YAAA,GAAGE,OAAO;AACV,YAAA,GAAGI;AACL,SAAA,CAAA;AAEA,QAAA,IAAI,CAACM,cAAgB,EAAA;YACnB,OAAOnB,GAAAA,CAAIW,QAAQ,CAAC,kBAAA,CAAA;AACtB;AAEAT,QAAAA,MAAAA,CAAOC,GAAG,CAAC,eAAiBiB,CAAAA,CAAAA,MAAM,CAACD,cAAAA,CAAAA;AAEnCnB,QAAAA,GAAAA,CAAIK,IAAI,CAAC;YAAElB,IAAMgC,EAAAA;AAAe,SAAA,CAAA;AAClC,KAAA;AAEA,IAAA,MAAME,eAAcrB,GAAY,EAAA;AAC9B,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AACzB,QAAA,MAAMC,UAAU,MAAMP,MAAAA,CAAOC,GAAG,CAAC,cAAA,CAAA,CAAgBO,WAAW,CAACH,EAAAA,CAAAA;AAE7D,QAAA,IAAI,CAACE,OAAS,EAAA;YACZ,OAAOT,GAAAA,CAAIW,QAAQ,CAAC,kBAAA,CAAA;AACtB;AAEA,QAAA,MAAMT,MAAOC,CAAAA,GAAG,CAAC,cAAA,CAAA,CAAgBkB,aAAa,CAACd,EAAAA,CAAAA;AAE/CL,QAAAA,MAAAA,CAAOC,GAAG,CAAC,eAAiBmB,CAAAA,CAAAA,MAAM,CAACb,OAAAA,CAAAA;AAEnCT,QAAAA,GAAAA,CAAIa,IAAI,GAAG;YAAE1B,IAAMsB,EAAAA;AAAQ,SAAA;AAC7B,KAAA;AAEA,IAAA,MAAMc,gBAAevB,GAAY,EAAA;AAC/B,QAAA,MAAM,EAAEwB,GAAG,EAAE,GAAGxB,GAAIc,CAAAA,OAAO,CAACD,IAAI;QAEhC,IAAI,CAACY,MAAMC,OAAO,CAACF,QAAQA,GAAIG,CAAAA,MAAM,KAAK,CAAG,EAAA;YAC3C,OAAO3B,GAAAA,CAAI4B,UAAU,CAAC,4BAAA,CAAA;AACxB;QAEA,KAAK,MAAMrB,MAAMiB,GAAK,CAAA;AACpB,YAAA,MAAMf,UAAU,MAAMP,MAAAA,CAAOC,GAAG,CAAC,cAAA,CAAA,CAAgBO,WAAW,CAACH,EAAAA,CAAAA;AAE7D,YAAA,IAAIE,OAAS,EAAA;AACX,gBAAA,MAAMP,MAAOC,CAAAA,GAAG,CAAC,cAAA,CAAA,CAAgBkB,aAAa,CAACd,EAAAA,CAAAA;AAC/CL,gBAAAA,MAAAA,CAAOC,GAAG,CAAC,eAAiBmB,CAAAA,CAAAA,MAAM,CAACb,OAAAA,CAAAA;AACrC;AACF;AAEAT,QAAAA,GAAAA,CAAIK,IAAI,CAAC;YAAElB,IAAMqC,EAAAA;AAAI,SAAA,CAAA;AACvB,KAAA;AAEA,IAAA,MAAMK,gBAAe7B,GAAY,EAAA;AAC/B,QAAA,MAAM,EAAEO,EAAE,EAAE,GAAGP,IAAIQ,MAAM;AAEzB,QAAA,MAAMC,UAAU,MAAMP,MAAAA,CAAOC,GAAG,CAAC,cAAA,CAAA,CAAgBO,WAAW,CAACH,EAAAA,CAAAA;QAE7D,MAAMuB,QAAAA,GAAW,MAAM5B,MAAAA,CACpBC,GAAG,CAAC,iBACJ4B,GAAG,CAACtB,OAAyC,EAAA,cAAA,EAAgB,EAAC,CAAA;AAEjET,QAAAA,GAAAA,CAAIa,IAAI,GAAG;YAAE1B,IAAM2C,EAAAA;AAAS,SAAA;AAC9B;AACF,CAAE;;;;"}